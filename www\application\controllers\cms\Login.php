<?php

require_once __DIR__ . "/../Base.php";

class Login extends Base {
	
	public function __construct() {
		parent::__construct();
		
		$this->load_usession();

		$this->load_smarty();
		
		$this->view->set("app_author", conf("app_author"));
		$this->view->set("app_copyright", conf("app_copyright"));
		$this->view->set("app_copyright_date", conf("app_copyright_date"));
		$this->view->set("app_domain", conf("app_domain"));
		$this->view->set("cms_name", conf("cms", "name"));
		$this->view->set("cms_skin", conf("cms", "skin"));
		$this->view->set("cms_root", $this->path->base . conf("cms", "path"));
	}

	public function index() {
		$email = "";
		$is_perm = true;

		$role = conf("cms", "role");
		if ($this->input->post()) {
			$email = $this->input->post("email");
			$password = $this->input->post("password");
			$is_perm = $this->input->post("is_perm");
			$res = $this->data->users->login($email, $role, $password, $is_perm);
			if ($res) {
				$def = conf("cms", "path");
				$location = _get("ret", $def);
				redirect($location);
			} else {
				$this->view->set("error", 1);
				$this->data->sessions->reset_current();
			}
		}
		$this->view->set("email", $email);
		$this->view->set("is_perm", $is_perm);
		$this->view->set("expired", Data_sessions::get_current_id() && !$this->is_user($role));
		$this->view->set("jstest", jstest());

		$this->render("cms/login_index");
	}

	public function password() {
		
		$secret = $this->input->get("secret");
		
		if ($secret) {
			$this->password_reset($secret);
			
			$this->view->set("success", $this->get_flash("success"));
			$this->view->set("jstest", jstest());
			
			$this->render("cms/login_reset");
		} else {
			$email = $this->input->post("email");
			
			$this->password_send($email);
			
			$this->view->set("email", $email);
			$this->view->set("success", $this->get_flash("success"));
			$this->view->set("jstest", jstest());
			
			$this->render("cms/login_password");
		}
		
	}
	
	private function password_send($email) {
		if (!($this->input->post() && $this->is_human())) {
			return;
		}
		
		// prevent any bruteforce attempts
		sleep(1);
		$filter = array(
				"email" => $email,
				"role_id" => conf("cms", "role"),
		);
		$user = $this->data->users->get_first($filter);
		if ($user) {
			$user->generate_reset_secret();
			$this->data->update($user);
			
			$url = sprintf("%s%slogin/password?secret=%s", $this->path->http_base, conf("cms", "path"), $user->reset_secret);
			$email_model = array(
					"name" => $user->name,
					"app_domain" => conf("app_domain"),
					"url" => $url,
			);
			
			$subject = "Възстановяване на парола";
			$this->create_email_send("password-recovery", $email_model, $subject, $email, "html");
			
			$this->set_flash("success");
			refresh();
		} else {
			$this->view->set("error", 1);
		}
	}
	
	private function password_reset($secret) {
		$filter = array(
				"reset_secret" => $secret,
		);
		$user = $this->data->users->get_first($filter);

		if ($user) {
			if ($this->input->post("password") && $this->is_human()) {
				$user->password = $this->input->post("password");
				$user->password_repeat = $this->input->post("password_repeat");
				
				$validator = $user->validator();
				$v = $validator->validate("admin");
				
				if ($user->validate("admin")) {
					$user->reset_secret = null;
					$this->data->update($user);
					
					$this->set_flash("success");
					refresh();
				}
			}
		} else {
			$this->view->set("error", 1);
		}
	}
	
	private function is_human() {
		$jstest = intval($this->input->post("jstest"));
		
		return jstest($jstest);
	}
}
