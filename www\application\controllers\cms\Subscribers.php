<?php

require_once "base/Post_edit.php";

class Subscribers extends Post_edit {

	protected function opts() {
		return array(
				"add"=>false,
				"edit"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->subscribers->get_count($filter);
	}
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->subscribers->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function on_indexing($filter, array $objects) {
		if ($this->post_action == "export") {
			$this->export($objects);
		}
	}
	protected function get_object($id) {
		return $this->data->subscribers->id($id);
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
		);
	}
	protected function orders() {
		return array (
				"time" => "дата и час",
				"email" => "имейл",
				"name" => "име",
		);
	}
	protected function direction() {
		return "desc";
	}
	private function export($objects) {
		$this->classloader->load("ext", "excel_xml");

		$data = array(1 => array("имейл", "име", "дата и час"));
		foreach($objects as $o) {
			$data[] = array(
					$o->email,
					$o->name,
					conv_date_string($o->time),
			);
		}
		$xls = new excel_xml("UTF-8", false, "subscribers");
		$xls->addArray($data);
		$xls->generateXML("subscribers_" . conv_date_string("now", "date"));
		exit;
	}
}