<?php

require_once "base/Post_edit.php";

class Cmslog extends Post_edit {

	protected function opts() {
		return array(
				"add"=>false,
				"edit"=>false,
				"delete"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->cms_logs->get_count($filter);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->cms_logs->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"time" => array("type"=>"datetime", "label"=>"Добавен", "range"=>1),
		);
	}
	protected function orders() {
		return array (
			"time" => "дата и час",
			"email" => "потребител",
			"section" => "раздел" ,
			"action" => "действие" ,
			"description" => "детайли" ,
		);
	}
	protected function direction() {
		return "desc";
	}
}