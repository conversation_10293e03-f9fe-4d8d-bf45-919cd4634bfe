$parent: '.chart';

#{$parent} {
    gap: 3rem;
    background: linear-gradient(180deg, #F8F8F8 0%, rgba(255, 255, 255, 0.2) 115.66%);
    border: 1px solid $color-primary;
    backdrop-filter: blur(10px);
    border-radius: 3px;
    padding-bottom: 6rem;

    &--inner {
        #{$parent}__tab {
            &.-current {
                background-color: $color-gray-light;
                &::before {
                    background-color: $color-gray-light;
                }
            }
        }
    }

    &__tabs {
        display: flex;
        justify-content: center;
        margin-bottom: 3rem;
    }

    &__table {
        display: none;
        flex-direction: column;
        &.-visible {
            display: flex;
        }
    }

    &__row {
        display: flex;
        &:first-child {
            #{$parent}__cell--white {
                border-top: 1px solid $color-primary;
                border-radius: 3px 3px 0 0;
            }
        }
        &:last-child {
            #{$parent}__cell--white {
                border-bottom: 1px solid $color-primary;
                border-radius: 0 0 3px 3px;
            }
        }
    }

    &__cell {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 3rem;
        padding-bottom: 3rem;
        width: 23%;
        border-bottom: 1px solid #EDEDED;
        padding-left: 1rem;
        padding-right: 1rem;

        &--left {
            width: 30%;
            justify-content: flex-start;
            padding-right: 2rem;
            font-size: clamp(1.2rem, 1.5vw, 1.6rem);
            padding-left: 0;
        }

        &--white {
            background-color: $color-white;
            border-left: 1px solid $color-primary;
            border-right: 1px solid $color-primary;
            border-bottom: none;
            #{$parent}__title {
                color: $color-black;
            }
        }
    }

    &__title {
        font-family: $font-title;
        color: #939393;
        font-size: clamp(1.4rem, 1.5vw, 1.7rem);
        text-align: center;
        span {
            color: $color-primary;
        }
    }

    &__tab {
        position: relative;
        cursor: pointer;
        &.-current {
            background-color: $color-white;
            border: 1px solid $color-primary;
            border-top: none;
            border-radius: 0 0 3px 3px;
            &::before {
                content: '';
                position: absolute;
                top: -2px;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: $color-white;
            }

            #{$parent}__link {
                color: $color-primary;
                text-decoration: underline;
            }
        }
    }

    &__link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2.4rem 2.5vw;
        font-family: $font-title;
        color: #939393;
    }

    /* 640 down */
    @include media(md-down) {
        &__title {
            transform: rotate(-90deg);
        }
    } 
}