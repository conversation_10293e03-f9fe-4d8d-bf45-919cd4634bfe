<?php
/* Smarty version 3.1.30, created on 2025-07-05 15:33:18
  from "W:\work\dvm_finance\www\application\views\public\about.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_68691b8e09de96_61223975',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f461ae380b2ec5fb7e41cf290bea0fabbba9e8e9' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\about.tpl',
      1 => 1751718764,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/feat-6.tpl' => 1,
    'file:./_partial/feat-3.tpl' => 1,
    'file:./_partial/benefits.tpl' => 1,
    'file:./_partial/reviews.tpl' => 1,
    'file:./_partial/contacts.tpl' => 1,
  ),
),false)) {
function content_68691b8e09de96_61223975 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_39417608068691b8e09d6e8_80864220', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_39417608068691b8e09d6e8_80864220 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					<?php echo $_smarty_tpl->tpl_vars['node']->value->subtitle;?>

				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node']->value->text);?>

					</p>
				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-relative">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['members']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_0_saved = $_smarty_tpl->tpl_vars['i'];
?>
				<div class="team-card <?php if ($_smarty_tpl->tpl_vars['i']->first) {?>team-card--left<?php } else { ?>team-card--right<?php }?>">
					<img class="team-card__icon" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image2,'icon'), ENT_QUOTES, 'UTF-8');?>
" alt="">
					<h3 class="team-card__name"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</h3>
					<div class="g-editor">
						<p>
							<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

						</p>
					</div>
					<img class="team-card__signature" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'signature'), ENT_QUOTES, 'UTF-8');?>
" alt="">
				</div>
				<?php if ($_smarty_tpl->tpl_vars['i']->first) {?>
					<img class="g-img-spacer g-radius-3" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['node']->value->image,'banner4'), ENT_QUOTES, 'UTF-8');?>
" alt="">
				<?php }?>
			<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_0_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

		</div>
		<div class="g-wrap">
			<div class="g-padded--xs">
				<div class="g-row g-gap-1 g-gap-v-1">
					<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-6.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['feat']->value), 0, false);
?>

				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm facts">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head">
				<h2 class="g-title g-title--md  js-anim">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_about_support']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h2>
			</div>
			<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-3.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['support_feat']->value), 0, false);
?>

		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-relative near">
				<div class="near__accent">
					<img class="near__accent-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo-white.svg" alt="">
				</div>
				<div class="near__bg">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['location_feat']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_1_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<div class="near__bg-img">
							<img class="near__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'banner5'), ENT_QUOTES, 'UTF-8');?>
" alt="">
						</div>
					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_1_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
				<div class="g-row g-gap-1 g-gap-v-1 g-gap-v-xm-0">
					<div class="col-1 col-xm-1-3 is-hidden is-inline--xm"></div>
					<div class="col-1 col-md-1-2 col-xm-1-3 g-vertical-self">
						<div class="near__center">
							<?php $_smarty_tpl->_assignInScope('preview', explode("\n",$_smarty_tpl->tpl_vars['node_about_locations']->value->preview));
?>
							<span class="g-title g-title--lg g-mb-0"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['preview']->value[0], ENT_QUOTES, 'UTF-8');?>
</span>
							<span class="g-title g-title--xs g-mb-0"><?php echo htmlspecialchars((($tmp = @$_smarty_tpl->tpl_vars['preview']->value[1])===null||$tmp==='' ? '' : $tmp), ENT_QUOTES, 'UTF-8');?>
</span>
						</div>
					</div>
					<div class="col-1 col-md-1-2 col-xm-1-3">
						<div class="near__content">
							<div class="g-editor near__txt">
								<p>
									<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node_about_locations']->value->text);?>

								</p>
							</div>
							<div class="near__vertical">
								<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['location_feat']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_2_saved = $_smarty_tpl->tpl_vars['i'];
?>
									<p class="g-title g-title--xs g-mb-0"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</p>
								<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_2_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm accorion-slider">
		<div class="g-wrap g-wrap--xl">
			<div class="g-row g-gap-2 g-gap-v-1 g-gap-v-xm-0">
				<div class="col-1 col-xm-1-12 col-lg-1-4">
					<div class="g-head g-mb-0">
						<p class="g-uppercase g-font-title g-color-primary g-title--sub g-mb-30">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_about_team']->value->title, ENT_QUOTES, 'UTF-8');?>

						</p>
					</div>
				</div>
				<div class="col-1 col-xm-1-2 col-lg-5-12">
					<ul class="accordion  accordion--team fade-up">
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['members2']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_3_saved = $_smarty_tpl->tpl_vars['i'];
?>
							<li class="accordion__item" data-id="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->index, ENT_QUOTES, 'UTF-8');?>
">
								<button class="accordion__control <?php if ($_smarty_tpl->tpl_vars['i']->first) {?>-active-control<?php }?>">
									<p class="g-uppercase g-font-title g-title--sub g-mb-10"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->subtitle, ENT_QUOTES, 'UTF-8');?>
</p>
									<h2 class="g-title g-title--m g-mb-0">
										<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>

									</h2>
								</button>
								<div class="accordion__panel">
									<div class="accordion__team-box is-hidden--xm">
										<img class="accordion__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'feat4'), ENT_QUOTES, 'UTF-8');?>
" alt="">
										<span class="accordion__logo-box">
											<img class="accordion__logo" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo.svg" alt="">
										</span>
									</div>
									<div class="g-editor">
										<p>
											<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

										</p>
									</div>
								</div>
							</li>
						<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_3_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

					</ul>
				</div>
				<div class="col-1 col-xm-5-12 col-lg-1-4 g-vertical-self is-hidden is-inline--xm">
					<div class="accordion__team-box">
						<div class="accordion__team-img-box">
							<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['members2']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_4_saved = $_smarty_tpl->tpl_vars['i'];
?>
								<img class="accordion__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'feat4'), ENT_QUOTES, 'UTF-8');?>
" alt="">
							<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_4_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

						</div>
						<span class="accordion__logo-box">
							<img class="accordion__logo" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo.svg" alt="">
						</span>
					</div>
				</div>
			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/benefits.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/reviews.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_reviews']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/contacts.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php
}
}
/* {/block "content"} */
}
