<?php

require_once "base/Post_edit.php";

class <PERSON>o extends Post_edit {

	protected function get_count($filter) {
		return $this->data->custom_urls->get_count($filter);
	}
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->custom_urls->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function get_object($id) {
		return $this->data->custom_urls->get_first(array("id"=>$id));
	}
	protected function get_object_title(ci_bean $object) {
		return $object->url;
	}
	protected function create_object() {
		return new custom_url();
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"is_active" => array("type"=>"checkbox", "label"=>"Активен", "def"=>-1),
		);
	}

	
	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		$object->url = custom_url::clean_url($object->url);
	}
}