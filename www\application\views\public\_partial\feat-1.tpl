<section class="g-padded--sm">
	<div class="g-wrap g-wrap--xl">
		{if $show_title}
			<div class="g-head">
				<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
					{$node->title}
				</p>
				<h2 class="g-title g-title--md g-title--underline js-anim">
					{$node->subtitle nofilter}
				</h2>
			</div>
		{/if}
		<div class="g-padded--sm g-padded--no-top g-relative">
			<img class="g-img-spacer" src="{$node->image|image:banner3}" alt="">
			<div class="audience-panel">
				<div class="g-wrap">
					<div class="audience-panel__wrap">
						<ul class="audience-panel__tabs">
							{foreach $node->children("feat") as $i}
								<li class="audience-panel__tab {if $i@first}-current{/if}" data-id="{$i@index}">
									<button class="audience-panel__tab-btn">
										<img class="audience-panel__icon" src="{$i->image2|image:icon}" alt="">
										{$i->title}
									</button>
								</li>
							{/foreach}
						</ul>
						<span class="audience-panel__vl"></span>
						<div class="audience-panel__content">
							<div class="audience-panel__carousel">
								{foreach $feat as $i}
									<div class="audience-panel__slide">
										<div class="g-editor g-font-title">
											<p>
												{$i->text|pt nofilter}
											</p>
										</div>
									</div>
								{/foreach}
							</div>
							{if $show_link}
								<ul>
									<li>
										<a class="btn btn--md btn--black" href="{$node_partner|path}">
											{@misc.become_partner}
											<i class="icon icon--handshake"></i>
										</a>
									</li>
								</ul>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>