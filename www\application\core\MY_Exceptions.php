<?php

class MY_Exceptions extends CI_Exceptions {

	const ERRORS = array(
			400=>array("header"=>"Bad Request", "message"=>"The request could not be understood."),
			401=>array("header"=>"Unauthorized", "message"=>"The request requires user authentication."),
			403=>array("header"=>"Forbidden", "message"=>"The request is forbidden."),
			404=>array("header"=>"Not Found", "message"=>"The resource was not found."),
			500=>array("header"=>"Internal Server Error", "message"=>"Un unexpected error occured and someone responsible has been notified."),
	);

	public function show_404($page = "", $log_error = TRUE){
		if ($this->is_ajax()) {
			$this->ajax_error(404);
		} else {
			$this->friendly_html_error(404);
		}
	}

	public function show_error($heading, $message, $template = "error_general", $status_code = 500) {
		$this->clear_ob();

		if ($this->is_ajax()) {
			$this->ajax_error($status_code, $message);
		} else {
			if (ini_get("display_errors")) {
				return parent::show_error($heading, $message, $template, $status_code);
			} else {
				return $this->friendly_html_error($status_code);
			}
		}
	}

	public function show_exception($exception) {
		$this->clear_ob();

		if ($this->is_ajax()) {
			$message = "" . $exception;
			$this->ajax_error(500, $message);
		} else {
			if (ini_get("display_errors")) {
				return parent::show_exception($exception);
			} else {
				return $this->friendly_html_error(500);
			}
		}
	}

	public function show_php_error($severity, $message, $filepath, $line) {
		$this->clear_ob();

		$severity = isset($this->levels[$severity]) ? $this->levels[$severity] : $severity;

		if ($this->is_ajax()) {
			$message = sprintf("Severity: %s\nMessage: %s\nFilename: %s\nLine Number: %s", $severity, $message, $filepath, $line);
			$this->ajax_error(500, $message);
		} else {
			if (ini_get("display_errors")) {
				parent::show_php_error($severity, $message, $filepath, $line);
			} else {
				$this->friendly_html_error(500);
			}
		}

	}

	private function is_ajax() {
		return isset($_SERVER["HTTP_X_REQUESTED_WITH"]) && $_SERVER["HTTP_X_REQUESTED_WITH"] === "XMLHttpRequest";
	}

	private function friendly_html_error($code) {
		$error = self::get_error($code);
		$header = sprintf("HTTP/1.1 %s %s", $code, $error["header"]);
		$message = $error["message"];
		if (class_exists("CI_Controller") && get_instance() && get_instance()->config) {
			// CI is loaded, load friendly view
			$CI =& get_instance();

			$data["message"] = $message;
			$data["root"] = $CI->path->base;
			$data["app_domain"] = conf("app_domain");
			$data["app_image"] = conf("app_image");

			$CI->output->set_header($header);
			$CI->load->view("errors/html/error_friendly", $data);
			$CI->output->_display();
		} else {
			// default view otherwise
			echo parent::show_error($error["header"], $message);
		}
	}

	private function ajax_error($code, $message="") {
		$error = self::get_error($code);
		// hide details if display_errors is off
		if (!ini_get("display_errors") || !$message) {
			$message = $error["message"];
		}

		$header = sprintf("HTTP/1.1 %s %s", $code, $error["header"]);

		$response = array(
				"error" => true,
				"message" => $message
		);

		$response = json_encode($response);

		$ok = false;
		if (class_exists("CI_Controller")) {
			// CI is loaded
			$CI =& get_instance();

			if ($CI) {
				$ok = true;

				$CI->output
						->set_header($header)
						->set_content_type("application/json")
						->set_output($response)
						->_display();
			}
		}
		if (!$ok) {
			// echo and bailout
			header($header);
			header("Content-Type: application/json");
			echo $response;
			exit;
		}
	}

	private static function get_error($code) {
		if (isset(self::ERRORS[$code])) {
			return self::ERRORS[$code];
		}
		return self::ERRORS[500];
	}

	private function clear_ob() {
		while (ob_get_level() > 1) {
			ob_end_clean();
		}
	}
}
