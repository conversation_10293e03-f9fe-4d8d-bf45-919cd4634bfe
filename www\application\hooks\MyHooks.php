<?php

class MyHooks {

	public function generate_routing() {

		$routes_static_file = APPPATH . "config/nodes.php";
		$routes_dyn_file = APPPATH . "config/routes_dyn.php";
		$compiled_file = config_item("routes_dyn_compiled");

		if (filemtime($routes_dyn_file) > @filemtime($compiled_file)) {
			require_once(BASEPATH ."database/DB.php");
			$db =& DB();
			
			require_once($routes_static_file);
			require_once($routes_dyn_file);
			$nodes = $config["nodes"];
			$def = $config["default_node"];

			$nodes_map = array();
			foreach($route["nodes"] as $key=>$val) {
				$k = (is_int($key) ? $val : $key);
				if (isset($nodes[$k])) {
					$id = $nodes[$k];
					$nodes_map[$id] = $val;
				}
			}
			$ids = array_map("intval", array_keys($nodes_map));

			$routes = array();

			if ($ids) {
				$ids = join(",", $ids);
				$sql = "SELECT fn.id, l.code lang, fn.path FROM v_full_nodes fn JOIN languages l ON (fn.language_id=l.id) WHERE fn.id IN ($ids) ORDER BY FIELD(fn.id, $ids), l.id";
				$query = $db->query($sql);
				
				$full_nodes = array();
				foreach ($query->result() as $row) {
					$full_nodes[] = $row;
				}
				$query->free_result();

				$routes = array_merge($routes, self::create_node_routing($controller_path, $full_nodes, $nodes_map, false));
				$routes = array_merge($routes, self::create_node_routing($controller_path, $full_nodes, $nodes_map, true));
			}
			
			$sql = "SELECT code FROM languages";
			$query = $db->query($sql);
			$langs = array();
			foreach ($query->result() as $row) {
				$langs[] = $row->code;
			}
			$query->free_result();

			foreach($route["lang"] as $key=>$val) {
				$k = (is_int($key) ? $val : $key);
				$routes["{$k}"] = "{$controller_path}{$val}";
				foreach ($langs as $lang) {
					$routes["{$lang}/{$k}"] = "{$controller_path}{$val}";
				}
			}
			
			$id = $nodes[$def];
			$node_map = $nodes_map[$id];
			$def = $node_map;
			if (is_array($def)) {
				if (isset($def[""])) {
					$def = $def[""];
				} elseif (isset($def["/"])) {
					$def = $def["/"];
				} else {
					$keys = array_keys($def);
					$def = $def[$keys[0]];
				}
			}
			$routes[""] = $def;
			foreach ($langs as $lang) {
				$routes["{$lang}"] = "{$controller_path}{$def}";
			}
			
			$compiled = "<?php\n\n";
			$compiled .= "\$dynamic = " . var_export($routes, true). ";\n";
			$compiled .= '$route = $route + $dynamic;';

			file_put_contents($compiled_file, $compiled, LOCK_EX);
		}
	}
	
	private static function create_node_routing($controller_path, $full_nodes, $nodes_map, $use_lang) {
		$res = array();
		foreach ($full_nodes as $node) {
			if (isset($nodes_map[$node->id])) {
				$node_map = $nodes_map[$node->id];
				if (!is_array($node_map)) {
					$node_map = array("/" => $node_map);
				}
				
				foreach ($node_map as $source=>$target) {
					$target = trim($target, '/');
					$url = (is_int($source) ? $target : trim($source, '/'));
					$path = self::urlencode_path($node->path) . ($url ? '/' . $url : "");
					$lang = ($use_lang ? $node->lang . ($path ? '/' : '') : "");
					$res[$lang . $path] =  "{$controller_path}{$target}";
				}
			}
		}
		return $res;
	}

	public function files_fix() {
		$_FILES = self::fix_files_array($_FILES);
	}

	public function stat_headers() {
		if (conf("show_exec_stats_headers")) {
			$ci =& get_instance();
			$ci->output
				->set_header("cpu-time: " . rutime("user"))
				->set_header(sprintf("exec-time: %.3f", microtime(true) - PHP_TIME_START));
		}
	}

	private static function fix_files_array(array $arr) {
		$res = array();
		foreach($arr as $title => $array) {
			if (isset($array["name"], $array["type"], $array["tmp_name"], $array["error"], $array["size"])) {
				if (is_array($array["name"])) {
					$newarr = array();
					foreach($array["name"] as $sub => $val) {
						$newarr[$sub] = array(
							"name" => $array["name"][$sub],
							"type" => $array["type"][$sub],
							"tmp_name" => $array["tmp_name"][$sub],
							"error" => $array["error"][$sub],
							"size" => $array["size"][$sub],
						);
					}
					$res[$title] = self::fix_files_array($newarr);
				} else {
					$res[$title] = $array;
				}
			}
		}
		return $res;
	}

	private static function urlencode_path($url) {
		return str_replace("%2F", "/", urlencode($url));
	}
}
