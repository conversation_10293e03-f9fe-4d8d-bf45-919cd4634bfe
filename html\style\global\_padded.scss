/**********************************************************
 * Padded sections
**********************************************************/

.g-padded {
	&--xl {
		padding-bottom: 7rem;
		padding-top: 7rem;
	}
	&--lg {
		padding-bottom: 6rem;
		padding-top: 6rem;
	}
	&--xm {
		padding-bottom: 5rem;
		padding-top: 5rem;
		position: relative;
	}    
	&--md {
		padding-bottom: 5rem;
		padding-top: 5rem;
		position: relative;
		z-index: 1;
	}
	&--sm {
		padding-bottom: 3rem;
		padding-top: 3rem;
		position: relative;
	}

	&--no-top {
		padding-top: 0 !important;
	}

	&--no-bottom {
		padding-bottom: 0 !important;
	}
	

	@include media(xm) {
		&--xl {
			padding-bottom: 15rem;
			padding-top: 15rem;
		}
		&--lg {
			padding-bottom: 10rem;
			padding-top: 10rem;
		}
		&--xm {
			padding-bottom: 8rem;
			padding-top: 8rem;
		}    
		&--md {
			padding-bottom: 6rem;
			padding-top: 6rem;
		}
		&--sm {
			padding-bottom: 3rem;
			padding-top: 3rem;
		}
		&--xs {
			padding-bottom: 2rem;
			padding-top: 2rem;
		}
	}

	@include media(lg) {
		&--xl {
			padding-bottom: 20rem;
			padding-top: 20rem;
		}
		&--lg {
			padding-bottom: 15rem;
			padding-top: 15rem;
		}
		&--xm {
			padding-bottom: 12rem;
			padding-top: 12rem;
		}
		&--md {
			padding-bottom: 8rem;
			padding-top: 8rem;
		}
		&--sm {
			padding-bottom: 5rem;
			padding-top: 5rem;
		}
		&--xs {
			padding-bottom: 3rem;
			padding-top: 3rem;
		}
	}
}