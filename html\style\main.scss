//=====================================================
// Variables, setup
//=====================================================

@import
	'abstract/media_queries',
	'abstract/variables',
	'abstract/fonts',
	'abstract/mixins',
	'abstract/reset';



//=====================================================
// Plugin styles - must come before components
//=====================================================

@import
	'vendor/slick',
	'vendor/venobox',
	'vendor/ionRangeSlider',
	// Overwrite default styles for plugins
	'vendor/overwrite';



//=====================================================
// Global styles - must come before components
//=====================================================

@import	
	'global/animations',
	'global/clearfix',
	'global/global',
	'global/editor',
	'global/head',
	'global/is',
	'global/padded',
	'global/row',
	'global/row-responsive',
	'global/gap',
	'global/invert', // this needs to stay below global/row in order for the flex-direction to be properly overwritten
	'global/section',
	'global/title',
	'global/wrap';



//=====================================================
// Components
//=====================================================

// Components that are often overwritten - must come first
@import
	'component/btn',
	'component/icon';

// Other components - sorted alphabetically
@import
	'component/checkbox',
	'component/consent',
	'component/footer',
	'component/form',
	'component/header',
	'component/hero',
	'component/underline',
	'component/scroll',
	'component/tabs',
	'component/video',
	'component/gallery',
	'component/nav',
	'component/contacts',
	'component/facts',
	'component/calculator',
	'component/hero-img',
	'component/box',
	'component/steps',
	'component/audience-panel',
	'component/partners-accordion',
	'component/chart',
	'component/related',
	'component/team-card',
	'component/frame-box',
	'component/article',
	'component/accordion',
	'component/review',
	'component/brave',
	'component/services',
	'component/near';