<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:39:31
  from "W:\work\dvm_finance\www\application\views\public\news-list.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6ba37ca616_19038264',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a78f9163d436b31908bbf5d0e595769c47654238' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\news-list.tpl',
      1 => 1751730161,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/news-item.tpl' => 1,
  ),
),false)) {
function content_686b6ba37ca616_19038264 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_path')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.path.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
if (!is_callable('smarty_modifier_date')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.date.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_1292803726686b6ba37c9fb4_43557760', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_1292803726686b6ba37c9fb4_43557760 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
			<ul class="tabs tabs--news">
				<li class="tabs__item <?php if ($_smarty_tpl->tpl_vars['node']->value == $_smarty_tpl->tpl_vars['node_news']->value) {?>-current<?php }?>">
					<a class="tabs__link" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_news']->value), ENT_QUOTES, 'UTF-8');?>
"><?php echo smarty_modifier_dict("misc.all");?>
</a>
				</li>
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['categories']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
					<li class="tabs__item <?php if ($_smarty_tpl->tpl_vars['node']->value == $_smarty_tpl->tpl_vars['i']->value) {?>-current<?php }?>">
						<a class="tabs__link" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['i']->value), ENT_QUOTES, 'UTF-8');?>
"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</a>
					</li>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

			</ul>
		</div>
		<?php if ($_smarty_tpl->tpl_vars['featured']->value) {?>
			<div class="g-wrap g-padded--sm">
				<a class="article article--lg" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['featured']->value), ENT_QUOTES, 'UTF-8');?>
">
					<div class="article__img-box">
						<img class="article__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['featured']->value->image,'news'), ENT_QUOTES, 'UTF-8');?>
" alt="">
					</div>
					<div class="article__content">
						<?php if ($_smarty_tpl->tpl_vars['featured']->value->date) {?>
							<p class="article__date"><?php echo htmlspecialchars(smarty_modifier_date($_smarty_tpl->tpl_vars['featured']->value->date,$_smarty_tpl->tpl_vars['lng']->value->code), ENT_QUOTES, 'UTF-8');?>
</p>
						<?php }?>
						<h3 class="article__title">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['featured']->value->title, ENT_QUOTES, 'UTF-8');?>

						</h3>
						<ul>
							<li>
								<div class="btn g-link">
									<?php echo smarty_modifier_dict("misc.more");?>

									<i class="icon icon--arrow-btn-black"></i>
								</div>
							</li>
						</ul>
					</div>
				</a>
			</div>
		<?php }?>
		<div class="g-wrap g-wrap--xl g-padded--sm g-padded--no-bottom">
			<div class="g-row g-gap-1 g-gap-v-3">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['list']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
					<?php $_smarty_tpl->_subTemplateRender("file:./_partial/news-item.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

			</div>
		</div>
	</section>
<?php
}
}
/* {/block "content"} */
}
