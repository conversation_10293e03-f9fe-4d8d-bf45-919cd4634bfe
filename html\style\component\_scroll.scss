/**********************************************************
 * 
**********************************************************/
$parent: '.scroll';

@keyframes scrolling {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
}

#{$parent} {
    align-items: center;
    display: flex;
    overflow: hidden;

    &--slides {
        align-items: flex-start;
        #{$parent}__group {
            align-items: flex-start;
            gap: 0rem;
            animation: scrolling 90s linear infinite;
        }
    }

    > * {
        flex: 0 0 100%;
    }
    
    &:hover {
        #{$parent}__group {
            animation-play-state: paused;
        }
    }
    
    &__group {
        align-items: center;
        animation: scrolling 30s linear infinite;
        display: flex;
        gap: 2rem;
        will-change: transform;
        padding-right: 2rem;
    }

    &__item {
        align-items: center;
        display: flex;
        width: 100%;
    }

    &__img {
        display: block;
        max-width: none;
        height: 5rem;
    }

    @include media(xm) {
        // &__group {
        //     gap: 8rem;
        //     padding-right: 8rem;
        // }
    }

    @include media(xl) {
        // &__group {
        //     gap: 12rem;
        //     padding-right: 12rem;
        // }
    }
}