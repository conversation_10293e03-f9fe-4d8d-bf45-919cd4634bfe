.related {
    position: relative;
    background-color: rgba($color-white, .1);
    border-radius: 5px;
    border: 1px solid $color-gray-light;
    backdrop-filter: blur(10px);
    &__btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        &--prev {
            left: 1.5rem;
        }
        &--next {
            right: 1.5rem;
        }
    }

    &__slide {
        display: flex !important;
        flex-direction: column;
        gap: 2.5rem;
    }

    &__header,
    &__content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    &__paging {
        font-size: clamp(1.8rem, 1.5vw, 2rem);
        color: #939393;
    }

    /* 1024 down */
    @include media(xm-down) {
        padding-left: 0;
        padding-right: 0;
        border: none;
        .g-wrap {
            padding-left: 0;
            padding-right: 0;
        }
    }
}