<?php

require_once "base/Post_edit.php";

class Sessions extends Post_edit {

	protected function opts() {
		return array(
				"add"=>false,
				"edit"=>false,
				"delete"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->sessions->get_count($filter);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->sessions->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"login" => array("type"=>"date", "label"=>"Вход", "range"=>1),
				"is_active" => array("type"=>"checkbox", "label"=>"Активна", "def"=>-1),
				"is_perm" => array("type"=>"checkbox", "label"=>"Постоянна", "def"=>-1),
		);
	}
	protected function orders() {
		return array (
				"login" => "вход",
				"logout" => "изход",
				"user" => "потребител" ,
				"ip" => "IP адрес" ,
		);
	}
	protected function direction() {
		return "desc";
	}
	
	protected function on_indexing($filter, array $objects) {
		$id = _post("close");
		
		if ($id) {
			$filter = array(
				"id" => $id,
				"is_active" => 1,
			);
			$session = $this->data->sessions->get_first($filter);
		
			if ($session) {
				$session->end();
				$this->data->update($session);
				$this->set_success();
			}
		}
	}
}