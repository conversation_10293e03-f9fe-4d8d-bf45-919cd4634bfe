{extends "./base.tpl"}

{$header_cta_css="g-pin-sign--right"}
{$footer_cta=true}

{block "content"}
	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					{$node->title}
				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					{$node->subtitle nofilter}
				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						{$node->text|pt nofilter}
					</p>
				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-relative">
			{foreach $members as $i}
				<div class="team-card {if $i@first}team-card--left{else}team-card--right{/if}">
					<img class="team-card__icon" src="{$i->image2|image:icon}" alt="">
					<h3 class="team-card__name">{$i->title}</h3>
					<div class="g-editor">
						<p>
							{$i->text|pt nofilter}
						</p>
					</div>
					<img class="team-card__signature" src="{$i->image|image:signature}" alt="">
				</div>
				{if $i@first}
					<img class="g-img-spacer g-radius-3" src="{$node->image|image:banner4}" alt="">
				{/if}
			{/foreach}
		</div>
		<div class="g-wrap">
			<div class="g-padded--xs">
				<div class="g-row g-gap-1 g-gap-v-1">
					{include "./_partial/feat-6.tpl" list=$feat}
				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm facts">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head">
				<h2 class="g-title g-title--md  js-anim">
					{$node_about_support->title}
				</h2>
			</div>
			{include "./_partial/feat-3.tpl" list=$support_feat}
		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-relative near">
				<div class="near__accent">
					<img class="near__accent-icon" src="{$res}image/Sign-Logo-white.svg" alt="">
				</div>
				<div class="near__bg">
					{foreach $location_feat as $i}
						<div class="near__bg-img">
							<img class="near__img" src="{$i->image|image:banner5}" alt="">
						</div>
					{/foreach}
				</div>
				<div class="g-row g-gap-1 g-gap-v-1 g-gap-v-xm-0">
					<div class="col-1 col-xm-1-3 is-hidden is-inline--xm"></div>
					<div class="col-1 col-md-1-2 col-xm-1-3 g-vertical-self">
						<div class="near__center">
							{$preview=explode("\n", $node_about_locations->preview)}
							<span class="g-title g-title--lg g-mb-0">{$preview[0]}</span>
							<span class="g-title g-title--xs g-mb-0">{$preview[1]|default}</span>
						</div>
					</div>
					<div class="col-1 col-md-1-2 col-xm-1-3">
						<div class="near__content">
							<div class="g-editor near__txt">
								<p>
									{$node_about_locations->text|pt nofilter}
								</p>
							</div>
							<div class="near__vertical">
								{foreach $location_feat as $i}
									<p class="g-title g-title--xs g-mb-0">{$i->title}</p>
								{/foreach}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm accorion-slider">
		<div class="g-wrap g-wrap--xl">
			<div class="g-row g-gap-2 g-gap-v-1 g-gap-v-xm-0">
				<div class="col-1 col-xm-1-12 col-lg-1-4">
					<div class="g-head g-mb-0">
						<p class="g-uppercase g-font-title g-color-primary g-title--sub g-mb-30">
							{$node_about_team->title}
						</p>
					</div>
				</div>
				<div class="col-1 col-xm-1-2 col-lg-5-12">
					<ul class="accordion  accordion--team fade-up">
						{foreach $members2 as $i}
							<li class="accordion__item" data-id="{$i@index}">
								<button class="accordion__control {if $i@first}-active-control{/if}">
									<p class="g-uppercase g-font-title g-title--sub g-mb-10">{$i->subtitle}</p>
									<h2 class="g-title g-title--m g-mb-0">
										{$i->title}
									</h2>
								</button>
								<div class="accordion__panel">
									<div class="accordion__team-box is-hidden--xm">
										<img class="accordion__img" src="{$i->image|image:feat4}" alt="">
										<span class="accordion__logo-box">
											<img class="accordion__logo" src="{$res}image/Sign-Logo.svg" alt="">
										</span>
									</div>
									<div class="g-editor">
										<p>
											{$i->text|pt nofilter}
										</p>
									</div>
								</div>
							</li>
						{/foreach}
					</ul>
				</div>
				<div class="col-1 col-xm-5-12 col-lg-1-4 g-vertical-self is-hidden is-inline--xm">
					<div class="accordion__team-box">
						<div class="accordion__team-img-box">
							{foreach $members2 as $i}
								<img class="accordion__img" src="{$i->image|image:feat4}" alt="">
							{/foreach}
						</div>
						<span class="accordion__logo-box">
							<img class="accordion__logo" src="{$res}image/Sign-Logo.svg" alt="">
						</span>
					</div>
				</div>
			</div>
		</div>
	</section>
	{include "./_partial/benefits.tpl"}
	{include "./_partial/reviews.tpl" node=$node_reviews}
	{include "./_partial/contacts.tpl"}
{/block}