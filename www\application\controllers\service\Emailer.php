<?php

require_once __DIR__ . "/../Base.php";

$self = fopen($_SERVER["SCRIPT_FILENAME"], "r");
if(!flock($self, LOCK_EX | LOCK_NB)) {
	exit;
}

class Emailer extends Base {

	public function index() {
		if (!$this->input->is_cli_request()) {
			exit;
		}
		$this->load_smarty();

		$filter = array(
			"scheduled" => 1,
		);
		$emails = $this->data->emails->get_list($filter, "rand", "asc");

		$sent = 0;
		foreach($emails as $i) {
			$this->send_email($i);
			$sent++;
		}

		$log = "sent: {$sent}";

		if ($sent) {
			$this->log->log_cronjob("emailer", $log);
		}
		$this->output->append_output($log);
	}
}