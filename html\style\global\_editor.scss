/**********************************************************
 * CMS-entered text (from WYSIWYG editor)
**********************************************************/

.g-editor {
	font-size: clamp($font-body-sm, 2vw, $font-body-md);

	&--sm {
		font-size: $font-body-sm;
	}

	&--md {
		font-size: clamp($font-body-md, 2vw, $font-body-xm);
	}

	&--lg {
		font-size: clamp($font-body-md, 2vw, $font-body-lg);
	}


	/* Headings */
	h1, h2, h3, h4, h5, h6 {
		line-height: 1.3em;
		margin-bottom: 2rem;
		margin-top: 4rem;
		
		&:first-child { margin-top: 0; }
	}

	h2 { font-size: clamp($font-title-sm, 4vw, $font-title-md); }
	h3, h4 { font-size: clamp($font-body-xm, 3vw, $font-title-sm); }
	h5, h6 { font-size: clamp($font-body-xm, 2vw, $font-title-xs); }


	/* Element styles */
	a {
		@include transition-xs;
		text-decoration: underline;
		text-decoration-color: inherit;
		text-underline-offset: 2px;

		&:hover {
			
		}
	}

	p {
		margin-bottom: 1rem;

		/* No spacing for last paragraph */
		&:last-child,
		&:first-child:last-child { 
			margin-bottom: 0; 
		}
	}

	strong { font-weight: bold; }
	em { font-style: italic; }

	ul {
		list-style: disc inside;
		margin: 2rem 0;

		li {
			margin-bottom: 1.2rem;
	
			&:last-child {
				margin-bottom: 0;
			}
		}
	}


	ol {
		list-style: decimal inside;
		margin: 2rem 0;

		li {
			line-height: 1.4em;
			margin-bottom: 1.2rem;
			position: relative;
		}
	}

	img {
		display: block;
		margin-bottom: 2rem;
		margin-top: 1rem;
	}

	/* Image with caption */
	figure {
		margin: 2rem 0;

		img {
			display: block;
			margin-bottom: 0;
			width: 100%;
		}

		img + figcaption {
			color: black;
			font-size: $font-body-sm;
			line-height: 1.2em;
			margin-bottom: 3rem;
			padding: 1rem 0;
		}
	}


	table {
		border-spacing: 1px;
		border-collapse: separate;
		font-size: $font-body-sm;
		table-layout: fixed;
	}

	th {
		background-color: black;
		border-right: 1px solid transparent;
		color: white;
		font-size: $font-body-sm;
		font-weight: bold;
		padding: 1rem;
		text-align: center;
	}

	td {
		border-bottom: 1px solid #ddd;
		min-width: 20rem;
		padding: 1.5rem 1rem;
		vertical-align: top;

		ul,
		ol {
			margin-top: 0;
			margin-left: 0;
		}
	}

	tr:last-child td {
		border-bottom: none;
	}

	
	@include media(xm) {
		p {
			margin-bottom: 2rem;
		}

		/* Image with caption */
		figure {
			margin: 3rem 0;
		}

		img {
			margin-bottom: 3rem;
			margin-top: 3rem;
		}
	}

	@include media(lg) {
		&--lg {
			p {
				margin-bottom: 3rem;
			}
		}

		ul,
		ol {
			margin: 4rem 0;
		}

	}

	@include media(xl) {
		h1, h2, h3, h4, h5, h6 {
			margin-bottom: 3rem;
			margin-top: 6rem;
		}
	}
}
