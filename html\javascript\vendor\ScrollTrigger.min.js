/*!
 * ScrollTrigger 3.12.4
 * https://gsap.com
 * 
 * @license Copyright 2023, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for Club GSAP members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(){return _e||"undefined"!=typeof window&&(_e=window.gsap)&&_e.registerPlugin&&_e}function z(e,t){return~qe.indexOf(e)&&qe[qe.indexOf(e)+1][t]}function A(e){return!!~t.indexOf(e)}function B(e,t,r,n,o){return e.addEventListener(t,r,{passive:!n,capture:!!o})}function C(e,t,r,n){return e.removeEventListener(t,r,!!n)}function F(){return Be&&Be.isPressed||Ie.cache++}function G(r,n){function dd(e){if(e||0===e){o&&(Ce.history.scrollRestoration="manual");var t=Be&&Be.isPressed;e=dd.v=Math.round(e)||(Be&&Be.iOS?1:0),r(e),dd.cacheID=Ie.cache,t&&i("ss",e)}else(n||Ie.cache!==dd.cacheID||i("ref"))&&(dd.cacheID=Ie.cache,dd.v=r());return dd.v+dd.offset}return dd.offset=0,r&&dd}function J(e,t){return(t&&t._ctx&&t._ctx.selector||_e.utils.toArray)(e)[0]||("string"==typeof e&&!1!==_e.config().nullTargetWarn?console.warn("Element not found:",e):null)}function K(t,e){var r=e.s,n=e.sc;A(t)&&(t=Se.scrollingElement||ke);var o=Ie.indexOf(t),i=n===Ye.sc?1:2;~o||(o=Ie.push(t)-1),Ie[o+i]||B(t,"scroll",F);var a=Ie[o+i],s=a||(Ie[o+i]=G(z(t,r),!0)||(A(t)?n:G(function(e){return arguments.length?t[r]=e:t[r]})));return s.target=t,a||(s.smooth="smooth"===_e.getProperty(t,"scrollBehavior")),s}function L(e,t,o){function Cd(e,t){var r=Le();t||n<r-s?(a=i,i=e,l=s,s=r):o?i+=e:i=a+(e-a)/(r-l)*(s-l)}var i=e,a=e,s=Le(),l=s,n=t||50,c=Math.max(500,3*n);return{update:Cd,reset:function reset(){a=i=o?0:i,l=s=0},getVelocity:function getVelocity(e){var t=l,r=a,n=Le();return!e&&0!==e||e===i||Cd(e),s===l||c<n-l?0:(i+(o?r:-r))/((o?n:s)-t)*1e3}}}function M(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function N(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r}function O(){(Oe=_e.core.globals().ScrollTrigger)&&Oe.core&&function _integrate(){var e=Oe.core,r=e.bridge||{},t=e._scrollers,n=e._proxies;t.push.apply(t,Ie),n.push.apply(n,qe),Ie=t,qe=n,i=function _bridge(e,t){return r[e](t)}}()}function P(e){return _e=e||r(),!Te&&_e&&"undefined"!=typeof document&&document.body&&(Ce=window,ke=(Se=document).documentElement,Pe=Se.body,t=[Ce,Se,ke,Pe],_e.utils.clamp,De=_e.core.context||function(){},Ee="onpointerenter"in Pe?"pointer":"mouse",Me=k.isTouch=Ce.matchMedia&&Ce.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Ce||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,Ae=k.eventTypes=("ontouchstart"in ke?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in ke?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return o=0},500),O(),Te=1),Te}var _e,Te,Ce,Se,ke,Pe,Me,Ee,Oe,t,Be,Ae,De,o=1,Re=[],Ie=[],qe=[],Le=Date.now,i=function _bridge(e,t){return t},n="scrollLeft",a="scrollTop",ze={s:n,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:G(function(e){return arguments.length?Ce.scrollTo(e,Ye.sc()):Ce.pageXOffset||Se[n]||ke[n]||Pe[n]||0})},Ye={s:a,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:ze,sc:G(function(e){return arguments.length?Ce.scrollTo(ze.sc(),e):Ce.pageYOffset||Se[a]||ke[a]||Pe[a]||0})};ze.op=Ye,Ie.cache=0;var k=(Observer.prototype.init=function init(e){Te||P(_e)||console.warn("Please gsap.registerPlugin(Observer)"),Oe||O();var o=e.tolerance,a=e.dragMinimum,t=e.type,i=e.target,r=e.lineHeight,n=e.debounce,s=e.preventDefault,l=e.onStop,c=e.onStopDelay,u=e.ignore,f=e.wheelSpeed,d=e.event,p=e.onDragStart,g=e.onDragEnd,h=e.onDrag,v=e.onPress,b=e.onRelease,m=e.onRight,y=e.onLeft,x=e.onUp,w=e.onDown,_=e.onChangeX,T=e.onChangeY,S=e.onChange,k=e.onToggleX,E=e.onToggleY,D=e.onHover,R=e.onHoverEnd,I=e.onMove,q=e.ignoreCheck,z=e.isNormalizer,Y=e.onGestureStart,H=e.onGestureEnd,X=e.onWheel,W=e.onEnable,V=e.onDisable,U=e.onClick,G=e.scrollSpeed,j=e.capture,Q=e.allowClicks,Z=e.lockAxis,$=e.onLockAxis;function bf(){return ye=Le()}function cf(e,t){return(se.event=e)&&u&&~u.indexOf(e.target)||t&&ge&&"touch"!==e.pointerType||q&&q(e,t)}function ef(){var e=se.deltaX=N(be),t=se.deltaY=N(me),r=Math.abs(e)>=o,n=Math.abs(t)>=o;S&&(r||n)&&S(se,e,t,be,me),r&&(m&&0<se.deltaX&&m(se),y&&se.deltaX<0&&y(se),_&&_(se),k&&se.deltaX<0!=le<0&&k(se),le=se.deltaX,be[0]=be[1]=be[2]=0),n&&(w&&0<se.deltaY&&w(se),x&&se.deltaY<0&&x(se),T&&T(se),E&&se.deltaY<0!=ce<0&&E(se),ce=se.deltaY,me[0]=me[1]=me[2]=0),(ne||re)&&(I&&I(se),re&&(h(se),re=!1),ne=!1),ie&&!(ie=!1)&&$&&$(se),oe&&(X(se),oe=!1),ee=0}function ff(e,t,r){be[r]+=e,me[r]+=t,se._vx.update(e),se._vy.update(t),n?ee=ee||requestAnimationFrame(ef):ef()}function gf(e,t){Z&&!ae&&(se.axis=ae=Math.abs(e)>Math.abs(t)?"x":"y",ie=!0),"y"!==ae&&(be[2]+=e,se._vx.update(e,!0)),"x"!==ae&&(me[2]+=t,se._vy.update(t,!0)),n?ee=ee||requestAnimationFrame(ef):ef()}function hf(e){if(!cf(e,1)){var t=(e=M(e,s)).clientX,r=e.clientY,n=t-se.x,o=r-se.y,i=se.isDragging;se.x=t,se.y=r,(i||Math.abs(se.startX-t)>=a||Math.abs(se.startY-r)>=a)&&(h&&(re=!0),i||(se.isDragging=!0),gf(n,o),i||p&&p(se))}}function lf(e){return e.touches&&1<e.touches.length&&(se.isGesturing=!0)&&Y(e,se.isDragging)}function mf(){return(se.isGesturing=!1)||H(se)}function nf(e){if(!cf(e)){var t=ue(),r=fe();ff((t-de)*G,(r-pe)*G,1),de=t,pe=r,l&&te.restart(!0)}}function of(e){if(!cf(e)){e=M(e,s),X&&(oe=!0);var t=(1===e.deltaMode?r:2===e.deltaMode?Ce.innerHeight:1)*f;ff(e.deltaX*t,e.deltaY*t,0),l&&!z&&te.restart(!0)}}function pf(e){if(!cf(e)){var t=e.clientX,r=e.clientY,n=t-se.x,o=r-se.y;se.x=t,se.y=r,ne=!0,l&&te.restart(!0),(n||o)&&gf(n,o)}}function qf(e){se.event=e,D(se)}function rf(e){se.event=e,R(se)}function sf(e){return cf(e)||M(e,s)&&U(se)}this.target=i=J(i)||ke,this.vars=e,u=u&&_e.utils.toArray(u),o=o||1e-9,a=a||0,f=f||1,G=G||1,t=t||"wheel,touch,pointer",n=!1!==n,r=r||parseFloat(Ce.getComputedStyle(Pe).lineHeight)||22;var ee,te,re,ne,oe,ie,ae,se=this,le=0,ce=0,ue=K(i,ze),fe=K(i,Ye),de=ue(),pe=fe(),ge=~t.indexOf("touch")&&!~t.indexOf("pointer")&&"pointerdown"===Ae[0],he=A(i),ve=i.ownerDocument||Se,be=[0,0,0],me=[0,0,0],ye=0,xe=se.onPress=function(e){cf(e,1)||e&&e.button||(se.axis=ae=null,te.pause(),se.isPressed=!0,e=M(e),le=ce=0,se.startX=se.x=e.clientX,se.startY=se.y=e.clientY,se._vx.reset(),se._vy.reset(),B(z?i:ve,Ae[1],hf,s,!0),se.deltaX=se.deltaY=0,v&&v(se))},we=se.onRelease=function(t){if(!cf(t,1)){C(z?i:ve,Ae[1],hf,!0);var e=!isNaN(se.y-se.startY),r=se.isDragging,n=r&&(3<Math.abs(se.x-se.startX)||3<Math.abs(se.y-se.startY)),o=M(t);!n&&e&&(se._vx.reset(),se._vy.reset(),s&&Q&&_e.delayedCall(.08,function(){if(300<Le()-ye&&!t.defaultPrevented)if(t.target.click)t.target.click();else if(ve.createEvent){var e=ve.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,Ce,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}})),se.isDragging=se.isGesturing=se.isPressed=!1,l&&r&&!z&&te.restart(!0),g&&r&&g(se),b&&b(se,n)}};te=se._dc=_e.delayedCall(c||.25,function onStopFunc(){se._vx.reset(),se._vy.reset(),te.pause(),l&&l(se)}).pause(),se.deltaX=se.deltaY=0,se._vx=L(0,50,!0),se._vy=L(0,50,!0),se.scrollX=ue,se.scrollY=fe,se.isDragging=se.isGesturing=se.isPressed=!1,De(this),se.enable=function(e){return se.isEnabled||(B(he?ve:i,"scroll",F),0<=t.indexOf("scroll")&&B(he?ve:i,"scroll",nf,s,j),0<=t.indexOf("wheel")&&B(i,"wheel",of,s,j),(0<=t.indexOf("touch")&&Me||0<=t.indexOf("pointer"))&&(B(i,Ae[0],xe,s,j),B(ve,Ae[2],we),B(ve,Ae[3],we),Q&&B(i,"click",bf,!1,!0),U&&B(i,"click",sf),Y&&B(ve,"gesturestart",lf),H&&B(ve,"gestureend",mf),D&&B(i,Ee+"enter",qf),R&&B(i,Ee+"leave",rf),I&&B(i,Ee+"move",pf)),se.isEnabled=!0,e&&e.type&&xe(e),W&&W(se)),se},se.disable=function(){se.isEnabled&&(Re.filter(function(e){return e!==se&&A(e.target)}).length||C(he?ve:i,"scroll",F),se.isPressed&&(se._vx.reset(),se._vy.reset(),C(z?i:ve,Ae[1],hf,!0)),C(he?ve:i,"scroll",nf,j),C(i,"wheel",of,j),C(i,Ae[0],xe,j),C(ve,Ae[2],we),C(ve,Ae[3],we),C(i,"click",bf,!0),C(i,"click",sf),C(ve,"gesturestart",lf),C(ve,"gestureend",mf),C(i,Ee+"enter",qf),C(i,Ee+"leave",rf),C(i,Ee+"move",pf),se.isEnabled=se.isPressed=se.isDragging=!1,V&&V(se))},se.kill=se.revert=function(){se.disable();var e=Re.indexOf(se);0<=e&&Re.splice(e,1),Be===se&&(Be=0)},Re.push(se),z&&A(i)&&(Be=se),se.enable(d)},function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(Observer,[{key:"velocityX",get:function get(){return this._vx.getVelocity()}},{key:"velocityY",get:function get(){return this._vy.getVelocity()}}]),Observer);function Observer(e){this.init(e)}k.version="3.12.4",k.create=function(e){return new k(e)},k.register=P,k.getAll=function(){return Re.slice()},k.getById=function(t){return Re.filter(function(e){return e.vars.id===t})[0]},r()&&_e.registerPlugin(k);function Ca(e,t,r){var n=lt(e)&&("clamp("===e.substr(0,6)||-1<e.indexOf("max"));return(r["_"+t+"Clamp"]=n)?e.substr(6,e.length-7):e}function Da(e,t){return!t||lt(e)&&"clamp("===e.substr(0,6)?e:"clamp("+e+")"}function Fa(){return Ge=1}function Ga(){return Ge=0}function Ha(e){return e}function Ia(e){return Math.round(1e5*e)/1e5||0}function Ja(){return"undefined"!=typeof window}function Ka(){return Fe||Ja()&&(Fe=window.gsap)&&Fe.registerPlugin&&Fe}function La(e){return!!~l.indexOf(e)}function Ma(e){return("Height"===e?T:He["inner"+e])||Xe["client"+e]||We["client"+e]}function Na(e){return z(e,"getBoundingClientRect")||(La(e)?function(){return Et.width=He.innerWidth,Et.height=T,Et}:function(){return xt(e)})}function Qa(e,t){var r=t.s,n=t.d2,o=t.d,i=t.a;return Math.max(0,(r="scroll"+n)&&(i=z(e,r))?i()-Na(e)()[o]:La(e)?(Xe[r]||We[r])-Ma(n):e[r]-e["offset"+n])}function Ra(e,t){for(var r=0;r<g.length;r+=3)t&&!~t.indexOf(g[r+1])||e(g[r],g[r+1],g[r+2])}function Ta(e){return"function"==typeof e}function Ua(e){return"number"==typeof e}function Va(e){return"object"==typeof e}function Wa(e,t,r){return e&&e.progress(t?0:1)&&r&&e.pause()}function Xa(e,t){if(e.enabled){var r=e._ctx?e._ctx.add(function(){return t(e)}):t(e);r&&r.totalTime&&(e.callbackAnimation=r)}}function mb(e){return He.getComputedStyle(e)}function ob(e,t){for(var r in t)r in e||(e[r]=t[r]);return e}function qb(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0}function rb(e){var t,r=[],n=e.labels,o=e.duration();for(t in n)r.push(n[t]/o);return r}function tb(o){var i=Fe.utils.snap(o),a=Array.isArray(o)&&o.slice(0).sort(function(e,t){return e-t});return a?function(e,t,r){var n;if(void 0===r&&(r=.001),!t)return i(e);if(0<t){for(e-=r,n=0;n<a.length;n++)if(a[n]>=e)return a[n];return a[n-1]}for(n=a.length,e+=r;n--;)if(a[n]<=e)return a[n];return a[0]}:function(e,t,r){void 0===r&&(r=.001);var n=i(e);return!t||Math.abs(n-e)<r||n-e<0==t<0?n:i(t<0?e-o:e+o)}}function vb(t,r,e,n){return e.split(",").forEach(function(e){return t(r,e,n)})}function wb(e,t,r,n,o){return e.addEventListener(t,r,{passive:!n,capture:!!o})}function xb(e,t,r,n){return e.removeEventListener(t,r,!!n)}function yb(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))}function Cb(e,t){if(lt(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in H?H[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function Db(e,t,r,n,o,i,a,s){var l=o.startColor,c=o.endColor,u=o.fontSize,f=o.indent,d=o.fontWeight,p=Ne.createElement("div"),g=La(r)||"fixed"===z(r,"pinType"),h=-1!==e.indexOf("scroller"),v=g?We:r,b=-1!==e.indexOf("start"),m=b?l:c,y="border-color:"+m+";font-size:"+u+";color:"+m+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((h||s)&&g?"fixed;":"absolute;"),!h&&!s&&g||(y+=(n===Ye?I:q)+":"+(i+parseFloat(f))+"px;"),a&&(y+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),p._isStart=b,p.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),p.style.cssText=y,p.innerText=t||0===t?e+"-"+t:e,v.children[0]?v.insertBefore(p,v.children[0]):v.appendChild(p),p._offset=p["offset"+n.op.d2],X(p,0,n,b),p}function Ib(){return 34<it()-at&&(D=D||requestAnimationFrame(Z))}function Jb(){v&&v.isPressed&&!(v.startX>We.clientWidth)||(Ie.cache++,v?D=D||requestAnimationFrame(Z):Z(),at||U("scrollStart"),at=it())}function Kb(){y=He.innerWidth,m=He.innerHeight}function Lb(){Ie.cache++,Ke||h||Ne.fullscreenElement||Ne.webkitFullscreenElement||b&&y===He.innerWidth&&!(Math.abs(He.innerHeight-m)>.25*He.innerHeight)||c.restart(!0)}function Ob(){return xb(ne,"scrollEnd",Ob)||kt(!0)}function Rb(e){for(var t=0;t<j.length;t+=5)(!e||j[t+4]&&j[t+4].query===e)&&(j[t].style.cssText=j[t+1],j[t].getBBox&&j[t].setAttribute("transform",j[t+2]||""),j[t+3].uncache=1)}function Sb(e,t){var r;for(je=0;je<Tt.length;je++)!(r=Tt[je])||t&&r._ctx!==t||(e?r.kill(1):r.revert(!0,!0));S=!0,t&&Rb(t),t||U("revert")}function Tb(e,t){Ie.cache++,!t&&tt||Ie.forEach(function(e){return Ta(e)&&e.cacheID++&&(e.rec=0)}),lt(e)&&(He.history.scrollRestoration=w=e)}function Yb(){We.appendChild(_),T=!v&&_.offsetHeight||He.innerHeight,We.removeChild(_)}function Zb(t){return Je(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})}function gc(e,t,r,n){if(!e._gsap.swappedIn){for(var o,i=$.length,a=t.style,s=e.style;i--;)a[o=$[i]]=r[o];a.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(a.display="inline-block"),s[q]=s[I]="auto",a.flexBasis=r.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[ut]=qb(e,ze)+yt,a[ft]=qb(e,Ye)+yt,a[vt]=s[bt]=s.top=s.left="0",Mt(n),s[ut]=s.maxWidth=r[ut],s[ft]=s.maxHeight=r[ft],s[vt]=r[vt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function jc(e){for(var t=ee.length,r=e.style,n=[],o=0;o<t;o++)n.push(ee[o],r[ee[o]]);return n.t=e,n}function mc(e,t,r,n,o,i,a,s,l,c,u,f,d,p){Ta(e)&&(e=e(s)),lt(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?Cb("0"+e.substr(3),r):0));var g,h,v,b=d?d.time():0;if(d&&d.seek(0),isNaN(e)||(e=+e),Ua(e))d&&(e=Fe.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,f,e)),a&&X(a,r,n,!0);else{Ta(t)&&(t=t(s));var m,y,x,w,_=(e||"0").split(" ");v=J(t,s)||We,(m=xt(v)||{})&&(m.left||m.top)||"none"!==mb(v).display||(w=v.style.display,v.style.display="block",m=xt(v),w?v.style.display=w:v.style.removeProperty("display")),y=Cb(_[0],m[n.d]),x=Cb(_[1]||"0",r),e=m[n.p]-l[n.p]-c+y+o-x,a&&X(a,x,n,r-x<20||a._isStart&&20<x),r-=r-x}if(p&&(s[p]=e||-.001,e<0&&(e=0)),i){var T=e+r,C=i._isStart;g="scroll"+n.d2,X(i,T,n,C&&20<T||!C&&(u?Math.max(We[g],Xe[g]):i.parentNode[g])<=T+1),u&&(l=xt(a),u&&(i.style[n.op.p]=l[n.op.p]-n.op.m-i._offset+yt))}return d&&v&&(g=xt(v),d.seek(f),h=xt(v),d._caScrollDist=g[n.p]-h[n.p],e=e/d._caScrollDist*f),d&&d.seek(b),d?e:Math.round(e)}function oc(e,t,r,n){if(e.parentNode!==t){var o,i,a=e.style;if(t===We){for(o in e._stOrig=a.cssText,i=mb(e))+o||re.test(o)||!i[o]||"string"!=typeof a[o]||"0"===o||(a[o]=i[o]);a.top=r,a.left=n}else a.cssText=e._stOrig;Fe.core.getCache(e).uncache=1,t.appendChild(e)}}function pc(r,e,n){var o=e,i=o;return function(e){var t=Math.round(r());return t!==o&&t!==i&&3<Math.abs(t-o)&&3<Math.abs(t-i)&&(e=t,n&&n()),i=o,o=e}}function qc(e,t,r){var n={};n[t.p]="+="+r,Fe.set(e,n)}function rc(c,e){function Bk(e,t,r,n,o){var i=Bk.tween,a=t.onComplete,s={};r=r||u();var l=pc(u,r,function(){i.kill(),Bk.tween=0});return o=n&&o||0,n=n||e-r,i&&i.kill(),t[f]=e,(t.modifiers=s)[f]=function(){return l(r+n*i.ratio+o*i.ratio*i.ratio)},t.onUpdate=function(){Ie.cache++,Bk.tween&&Z()},t.onComplete=function(){Bk.tween=0,a&&a.call(i)},i=Bk.tween=Fe.to(c,t)}var u=K(c,e),f="_scroll"+e.p2;return(c[f]=u).wheelHandler=function(){return Bk.tween&&Bk.tween.kill()&&(Bk.tween=0)},wb(c,"wheel",u.wheelHandler),ne.isTouch&&wb(c,"touchmove",u.wheelHandler),Bk}var Fe,s,He,Ne,Xe,We,l,c,Je,Ve,Ue,u,Ke,Ge,f,je,d,p,g,Qe,Ze,h,v,b,m,y,E,x,w,_,T,S,$e,et,D,tt,rt,nt,ot=1,it=Date.now,R=it(),at=0,st=0,lt=function _isString(e){return"string"==typeof e},ct=Math.abs,I="right",q="bottom",ut="width",ft="height",dt="Right",pt="Left",gt="Top",ht="Bottom",vt="padding",bt="margin",mt="Width",Y="Height",yt="px",xt=function _getBounds(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==mb(e)[f]&&Fe.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},wt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},_t={toggleActions:"play",anticipatePin:0},H={top:0,left:0,center:.5,bottom:1,right:1},X=function _positionMarker(e,t,r,n){var o={display:"block"},i=r[n?"os2":"p2"],a=r[n?"p2":"os2"];e._isFlipped=n,o[r.a+"Percent"]=n?-100:0,o[r.a]=n?"1px":0,o["border"+i+mt]=1,o["border"+a+mt]=0,o[r.p]=t+"px",Fe.set(e,o)},Tt=[],Ct={},W={},V=[],U=function _dispatch(e){return W[e]&&W[e].map(function(e){return e()})||V},j=[],St=0,kt=function _refreshAll(e,t){if(!at||e||S){Yb(),tt=ne.isRefreshing=!0,Ie.forEach(function(e){return Ta(e)&&++e.cacheID&&(e.rec=e())});var r=U("refreshInit");Qe&&ne.sort(),t||Sb(),Ie.forEach(function(e){Ta(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),Tt.slice(0).forEach(function(e){return e.refresh()}),S=!1,Tt.forEach(function(e){if(e._subPinOffset&&e.pin){var t=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[t];e.revert(!0,1),e.adjustPinSpacing(e.pin[t]-r),e.refresh()}}),$e=1,Zb(!0),Tt.forEach(function(e){var t=Qa(e.scroller,e._dir),r="max"===e.vars.end||e._endClamp&&e.end>t,n=e._startClamp&&e.start>=t;(r||n)&&e.setPositions(n?t-1:e.start,r?Math.max(n?t:e.start+1,t):e.end,!0)}),Zb(!1),$e=0,r.forEach(function(e){return e&&e.render&&e.render(-1)}),Ie.forEach(function(e){Ta(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),Tb(w,1),c.pause(),St++,Z(tt=2),Tt.forEach(function(e){return Ta(e.vars.onRefresh)&&e.vars.onRefresh(e)}),tt=ne.isRefreshing=!1,U("refresh")}else wb(ne,"scrollEnd",Ob)},Q=0,Pt=1,Z=function _updateAll(e){if(2===e||!tt&&!S){ne.isUpdating=!0,nt&&nt.update(0);var t=Tt.length,r=it(),n=50<=r-R,o=t&&Tt[0].scroll();if(Pt=o<Q?-1:1,tt||(Q=o),n&&(at&&!Ge&&200<r-at&&(at=0,U("scrollEnd")),Ue=R,R=r),Pt<0){for(je=t;0<je--;)Tt[je]&&Tt[je].update(0,n);Pt=1}else for(je=0;je<t;je++)Tt[je]&&Tt[je].update(0,n);ne.isUpdating=!1}D=0},$=["left","top",q,I,bt+ht,bt+dt,bt+gt,bt+pt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],ee=$.concat([ut,ft,"boxSizing","max"+mt,"max"+Y,"position",bt,vt,vt+gt,vt+dt,vt+ht,vt+pt]),te=/([A-Z])/g,Mt=function _setState(e){if(e){var t,r,n=e.t.style,o=e.length,i=0;for((e.t._gsap||Fe.core.getCache(e.t)).uncache=1;i<o;i+=2)r=e[i+1],t=e[i],r?n[t]=r:n[t]&&n.removeProperty(t.replace(te,"-$1").toLowerCase())}},Et={left:0,top:0},re=/(webkit|moz|length|cssText|inset)/i,ne=(ScrollTrigger.prototype.init=function init(E,O){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),st){var B,n,p,A,D,R,I,q,L,Y,F,e,H,N,X,W,V,U,t,G,b,j,Q,m,Z,y,$,x,r,w,_,ee,o,g,te,re,ne,T,i,C=(E=ob(lt(E)||Ua(E)||E.nodeType?{trigger:E}:E,_t)).onUpdate,S=E.toggleClass,a=E.id,k=E.onToggle,oe=E.onRefresh,P=E.scrub,ie=E.trigger,ae=E.pin,se=E.pinSpacing,le=E.invalidateOnRefresh,M=E.anticipatePin,s=E.onScrubComplete,h=E.onSnapComplete,ce=E.once,ue=E.snap,fe=E.pinReparent,l=E.pinSpacer,de=E.containerAnimation,pe=E.fastScrollEnd,ge=E.preventOverlaps,he=E.horizontal||E.containerAnimation&&!1!==E.horizontal?ze:Ye,ve=!P&&0!==P,be=J(E.scroller||He),c=Fe.core.getCache(be),me=La(be),ye="fixed"===("pinType"in E?E.pinType:z(be,"pinType")||me&&"fixed"),xe=[E.onEnter,E.onLeave,E.onEnterBack,E.onLeaveBack],we=ve&&E.toggleActions.split(" "),_e="markers"in E?E.markers:_t.markers,Te=me?0:parseFloat(mb(be)["border"+he.p2+mt])||0,Ce=this,Se=E.onRefreshInit&&function(){return E.onRefreshInit(Ce)},ke=function _getSizeFunc(e,t,r){var n=r.d,o=r.d2,i=r.a;return(i=z(e,"getBoundingClientRect"))?function(){return i()[n]}:function(){return(t?Ma(o):e["client"+o])||0}}(be,me,he),Pe=function _getOffsetsFunc(e,t){return!t||~qe.indexOf(e)?Na(e):function(){return Et}}(be,me),Me=0,Ee=0,Oe=0,Be=K(be,he);if(Ce._startClamp=Ce._endClamp=!1,Ce._dir=he,M*=45,Ce.scroller=be,Ce.scroll=de?de.time.bind(de):Be,A=Be(),Ce.vars=E,O=O||E.animation,"refreshPriority"in E&&(Qe=1,-9999===E.refreshPriority&&(nt=Ce)),c.tweenScroll=c.tweenScroll||{top:rc(be,Ye),left:rc(be,ze)},Ce.tweenTo=B=c.tweenScroll[he.p],Ce.scrubDuration=function(e){(o=Ua(e)&&e)?ee?ee.duration(e):ee=Fe.to(O,{ease:"expo",totalProgress:"+=0",duration:o,paused:!0,onComplete:function onComplete(){return s&&s(Ce)}}):(ee&&ee.progress(1).kill(),ee=0)},O&&(O.vars.lazy=!1,O._initted&&!Ce.isReverted||!1!==O.vars.immediateRender&&!1!==E.immediateRender&&O.duration()&&O.render(0,!0,!0),Ce.animation=O.pause(),(O.scrollTrigger=Ce).scrubDuration(P),w=0,a=a||O.vars.id),ue&&(Va(ue)&&!ue.push||(ue={snapTo:ue}),"scrollBehavior"in We.style&&Fe.set(me?[We,Xe]:be,{scrollBehavior:"auto"}),Ie.forEach(function(e){return Ta(e)&&e.target===(me?Ne.scrollingElement||Xe:be)&&(e.smooth=!1)}),p=Ta(ue.snapTo)?ue.snapTo:"labels"===ue.snapTo?function _getClosestLabel(t){return function(e){return Fe.utils.snap(rb(t),e)}}(O):"labelsDirectional"===ue.snapTo?function _getLabelAtDirection(r){return function(e,t){return tb(rb(r))(e,t.direction)}}(O):!1!==ue.directional?function(e,t){return tb(ue.snapTo)(e,it()-Ee<500?0:t.direction)}:Fe.utils.snap(ue.snapTo),g=ue.duration||{min:.1,max:2},g=Va(g)?Ve(g.min,g.max):Ve(g,g),te=Fe.delayedCall(ue.delay||o/2||.1,function(){var e=Be(),t=it()-Ee<500,r=B.tween;if(!(t||Math.abs(Ce.getVelocity())<10)||r||Ge||Me===e)Ce.isActive&&Me!==e&&te.restart(!0);else{var n=(e-R)/N,o=O&&!ve?O.totalProgress():n,i=t?0:(o-_)/(it()-Ue)*1e3||0,a=Fe.utils.clamp(-n,1-n,ct(i/2)*i/.185),s=n+(!1===ue.inertia?0:a),l=Ve(0,1,p(s,Ce)),c=Math.round(R+l*N),u=ue.onStart,f=ue.onInterrupt,d=ue.onComplete;if(e<=I&&R<=e&&c!==e){if(r&&!r._initted&&r.data<=ct(c-e))return;!1===ue.inertia&&(a=l-n),B(c,{duration:g(ct(.185*Math.max(ct(s-o),ct(l-o))/i/.05||0)),ease:ue.ease||"power3",data:ct(c-e),onInterrupt:function onInterrupt(){return te.restart(!0)&&f&&f(Ce)},onComplete:function onComplete(){Ce.update(),Me=Be(),ee&&O&&O.progress(l),w=_=O&&!ve?O.totalProgress():Ce.progress,h&&h(Ce),d&&d(Ce)}},e,a*N,c-e-a*N),u&&u(Ce,B.tween)}}}).pause()),a&&(Ct[a]=Ce),i=(i=(ie=Ce.trigger=J(ie||!0!==ae&&ae))&&ie._gsap&&ie._gsap.stRevert)&&i(Ce),ae=!0===ae?ie:J(ae),lt(S)&&(S={targets:ie,className:S}),ae&&(!1===se||se===bt||(se=!(!se&&ae.parentNode&&ae.parentNode.style&&"flex"===mb(ae.parentNode).display)&&vt),Ce.pin=ae,(n=Fe.core.getCache(ae)).spacer?X=n.pinState:(l&&((l=J(l))&&!l.nodeType&&(l=l.current||l.nativeElement),n.spacerIsNative=!!l,l&&(n.spacerState=jc(l))),n.spacer=U=l||Ne.createElement("div"),U.classList.add("pin-spacer"),a&&U.classList.add("pin-spacer-"+a),n.pinState=X=jc(ae)),!1!==E.force3D&&Fe.set(ae,{force3D:!0}),Ce.spacer=U=n.spacer,r=mb(ae),m=r[se+he.os2],G=Fe.getProperty(ae),b=Fe.quickSetter(ae,he.a,yt),gc(ae,U,r),V=jc(ae)),_e){e=Va(_e)?ob(_e,wt):wt,Y=Db("scroller-start",a,be,he,e,0),F=Db("scroller-end",a,be,he,e,0,Y),t=Y["offset"+he.op.d2];var u=J(z(be,"content")||be);q=this.markerStart=Db("start",a,u,he,e,t,0,de),L=this.markerEnd=Db("end",a,u,he,e,t,0,de),de&&(T=Fe.quickSetter([q,L],he.a,yt)),ye||qe.length&&!0===z(be,"fixedMarkers")||(function _makePositionable(e){var t=mb(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(me?We:be),Fe.set([Y,F],{force3D:!0}),y=Fe.quickSetter(Y,he.a,yt),x=Fe.quickSetter(F,he.a,yt))}if(de){var f=de.vars.onUpdate,d=de.vars.onUpdateParams;de.eventCallback("onUpdate",function(){Ce.update(0,0,1),f&&f.apply(de,d||[])})}if(Ce.previous=function(){return Tt[Tt.indexOf(Ce)-1]},Ce.next=function(){return Tt[Tt.indexOf(Ce)+1]},Ce.revert=function(e,t){if(!t)return Ce.kill(!0);var r=!1!==e||!Ce.enabled,n=Ke;r!==Ce.isReverted&&(r&&(re=Math.max(Be(),Ce.scroll.rec||0),Oe=Ce.progress,ne=O&&O.progress()),q&&[q,L,Y,F].forEach(function(e){return e.style.display=r?"none":"block"}),r&&(Ke=Ce).update(r),!ae||fe&&Ce.isActive||(r?function _swapPinOut(e,t,r){Mt(r);var n=e._gsap;if(n.spacerIsNative)Mt(n.spacerState);else if(e._gsap.swappedIn){var o=t.parentNode;o&&(o.insertBefore(e,t),o.removeChild(t))}e._gsap.swappedIn=!1}(ae,U,X):gc(ae,U,mb(ae),Z)),r||Ce.update(r),Ke=n,Ce.isReverted=r)},Ce.refresh=function(e,t,r,n){if(!Ke&&Ce.enabled||t)if(ae&&e&&at)wb(ScrollTrigger,"scrollEnd",Ob);else{!tt&&Se&&Se(Ce),Ke=Ce,B.tween&&!r&&(B.tween.kill(),B.tween=0),ee&&ee.pause(),le&&O&&O.revert({kill:!1}).invalidate(),Ce.isReverted||Ce.revert(!0,!0),Ce._subPinOffset=!1;var o,i,a,s,l,c,u,f,d,p,g,h,v,b=ke(),m=Pe(),y=de?de.duration():Qa(be,he),x=N<=.01,w=0,_=n||0,T=Va(r)?r.end:E.end,C=E.endTrigger||ie,S=Va(r)?r.start:E.start||(0!==E.start&&ie?ae?"0 0":"0 100%":0),k=Ce.pinnedContainer=E.pinnedContainer&&J(E.pinnedContainer,Ce),P=ie&&Math.max(0,Tt.indexOf(Ce))||0,M=P;for(_e&&Va(r)&&(h=Fe.getProperty(Y,he.p),v=Fe.getProperty(F,he.p));M--;)(c=Tt[M]).end||c.refresh(0,1)||(Ke=Ce),!(u=c.pin)||u!==ie&&u!==ae&&u!==k||c.isReverted||((p=p||[]).unshift(c),c.revert(!0,!0)),c!==Tt[M]&&(P--,M--);for(Ta(S)&&(S=S(Ce)),S=Ca(S,"start",Ce),R=mc(S,ie,b,he,Be(),q,Y,Ce,m,Te,ye,y,de,Ce._startClamp&&"_startClamp")||(ae?-.001:0),Ta(T)&&(T=T(Ce)),lt(T)&&!T.indexOf("+=")&&(~T.indexOf(" ")?T=(lt(S)?S.split(" ")[0]:"")+T:(w=Cb(T.substr(2),b),T=lt(S)?S:(de?Fe.utils.mapRange(0,de.duration(),de.scrollTrigger.start,de.scrollTrigger.end,R):R)+w,C=ie)),T=Ca(T,"end",Ce),I=Math.max(R,mc(T||(C?"100% 0":y),C,b,he,Be()+w,L,F,Ce,m,Te,ye,y,de,Ce._endClamp&&"_endClamp"))||-.001,w=0,M=P;M--;)(u=(c=Tt[M]).pin)&&c.start-c._pinPush<=R&&!de&&0<c.end&&(o=c.end-(Ce._startClamp?Math.max(0,c.start):c.start),(u===ie&&c.start-c._pinPush<R||u===k)&&isNaN(S)&&(w+=o*(1-c.progress)),u===ae&&(_+=o));if(R+=w,I+=w,Ce._startClamp&&(Ce._startClamp+=w),Ce._endClamp&&!tt&&(Ce._endClamp=I||-.001,I=Math.min(I,Qa(be,he))),N=I-R||(R-=.01)&&.001,x&&(Oe=Fe.utils.clamp(0,1,Fe.utils.normalize(R,I,re))),Ce._pinPush=_,q&&w&&((o={})[he.a]="+="+w,k&&(o[he.p]="-="+Be()),Fe.set([q,L],o)),!ae||$e&&Ce.end>=Qa(be,he)){if(ie&&Be()&&!de)for(i=ie.parentNode;i&&i!==We;)i._pinOffset&&(R-=i._pinOffset,I-=i._pinOffset),i=i.parentNode}else o=mb(ae),s=he===Ye,a=Be(),j=parseFloat(G(he.a))+_,!y&&1<I&&(g={style:g=(me?Ne.scrollingElement||Xe:be).style,value:g["overflow"+he.a.toUpperCase()]},me&&"scroll"!==mb(We)["overflow"+he.a.toUpperCase()]&&(g.style["overflow"+he.a.toUpperCase()]="scroll")),gc(ae,U,o),V=jc(ae),i=xt(ae,!0),f=ye&&K(be,s?ze:Ye)(),se&&((Z=[se+he.os2,N+_+yt]).t=U,(M=se===vt?qb(ae,he)+N+_:0)&&(Z.push(he.d,M+yt),"auto"!==U.style.flexBasis&&(U.style.flexBasis=M+yt)),Mt(Z),k&&Tt.forEach(function(e){e.pin===k&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),ye&&Be(re)),ye&&((l={top:i.top+(s?a-R:f)+yt,left:i.left+(s?f:a-R)+yt,boxSizing:"border-box",position:"fixed"})[ut]=l.maxWidth=Math.ceil(i.width)+yt,l[ft]=l.maxHeight=Math.ceil(i.height)+yt,l[bt]=l[bt+gt]=l[bt+dt]=l[bt+ht]=l[bt+pt]="0",l[vt]=o[vt],l[vt+gt]=o[vt+gt],l[vt+dt]=o[vt+dt],l[vt+ht]=o[vt+ht],l[vt+pt]=o[vt+pt],W=function _copyState(e,t,r){for(var n,o=[],i=e.length,a=r?8:0;a<i;a+=2)n=e[a],o.push(n,n in t?t[n]:e[a+1]);return o.t=e.t,o}(X,l,fe),tt&&Be(0)),O?(d=O._initted,Ze(1),O.render(O.duration(),!0,!0),Q=G(he.a)-j+N+_,$=1<Math.abs(N-Q),ye&&$&&W.splice(W.length-2,2),O.render(0,!0,!0),d||O.invalidate(!0),O.parent||O.totalTime(O.totalTime()),Ze(0)):Q=N,g&&(g.value?g.style["overflow"+he.a.toUpperCase()]=g.value:g.style.removeProperty("overflow-"+he.a));p&&p.forEach(function(e){return e.revert(!1,!0)}),Ce.start=R,Ce.end=I,A=D=tt?re:Be(),de||tt||(A<re&&Be(re),Ce.scroll.rec=0),Ce.revert(!1,!0),Ee=it(),te&&(Me=-1,te.restart(!0)),Ke=0,O&&ve&&(O._initted||ne)&&O.progress()!==ne&&O.progress(ne||0,!0).render(O.time(),!0,!0),(x||Oe!==Ce.progress||de)&&(O&&!ve&&O.totalProgress(de&&R<-.001&&!Oe?Fe.utils.normalize(R,I,0):Oe,!0),Ce.progress=x||(A-R)/N===Oe?0:Oe),ae&&se&&(U._pinOffset=Math.round(Ce.progress*Q)),ee&&ee.invalidate(),isNaN(h)||(h-=Fe.getProperty(Y,he.p),v-=Fe.getProperty(F,he.p),qc(Y,he,h),qc(q,he,h-(n||0)),qc(F,he,v),qc(L,he,v-(n||0))),x&&!tt&&Ce.update(),!oe||tt||H||(H=!0,oe(Ce),H=!1)}},Ce.getVelocity=function(){return(Be()-D)/(it()-Ue)*1e3||0},Ce.endAnimation=function(){Wa(Ce.callbackAnimation),O&&(ee?ee.progress(1):O.paused()?ve||Wa(O,Ce.direction<0,1):Wa(O,O.reversed()))},Ce.labelToScroll=function(e){return O&&O.labels&&(R||Ce.refresh()||R)+O.labels[e]/O.duration()*N||0},Ce.getTrailing=function(t){var e=Tt.indexOf(Ce),r=0<Ce.direction?Tt.slice(0,e).reverse():Tt.slice(e+1);return(lt(t)?r.filter(function(e){return e.vars.preventOverlaps===t}):r).filter(function(e){return 0<Ce.direction?e.end<=R:e.start>=I})},Ce.update=function(e,t,r){if(!de||r||e){var n,o,i,a,s,l,c,u=!0===tt?re:Ce.scroll(),f=e?0:(u-R)/N,d=f<0?0:1<f?1:f||0,p=Ce.progress;if(t&&(D=A,A=de?Be():u,ue&&(_=w,w=O&&!ve?O.totalProgress():d)),M&&!d&&ae&&!Ke&&!ot&&at&&R<u+(u-D)/(it()-Ue)*M&&(d=1e-4),d!==p&&Ce.enabled){if(a=(s=(n=Ce.isActive=!!d&&d<1)!=(!!p&&p<1))||!!d!=!!p,Ce.direction=p<d?1:-1,Ce.progress=d,a&&!Ke&&(o=d&&!p?0:1===d?1:1===p?2:3,ve&&(i=!s&&"none"!==we[o+1]&&we[o+1]||we[o],c=O&&("complete"===i||"reset"===i||i in O))),ge&&(s||c)&&(c||P||!O)&&(Ta(ge)?ge(Ce):Ce.getTrailing(ge).forEach(function(e){return e.endAnimation()})),ve||(!ee||Ke||ot?O&&O.totalProgress(d,!(!Ke||!Ee&&!e)):(ee._dp._time-ee._start!==ee._time&&ee.render(ee._dp._time-ee._start),ee.resetTo?ee.resetTo("totalProgress",d,O._tTime/O._tDur):(ee.vars.totalProgress=d,ee.invalidate().restart()))),ae)if(e&&se&&(U.style[se+he.os2]=m),ye){if(a){if(l=!e&&p<d&&u<I+1&&u+1>=Qa(be,he),fe)if(e||!n&&!l)oc(ae,U);else{var g=xt(ae,!0),h=u-R;oc(ae,We,g.top+(he===Ye?h:0)+yt,g.left+(he===Ye?0:h)+yt)}Mt(n||l?W:V),$&&d<1&&n||b(j+(1!==d||l?0:Q))}}else b(Ia(j+Q*d));!ue||B.tween||Ke||ot||te.restart(!0),S&&(s||ce&&d&&(d<1||!et))&&Je(S.targets).forEach(function(e){return e.classList[n||ce?"add":"remove"](S.className)}),!C||ve||e||C(Ce),a&&!Ke?(ve&&(c&&("complete"===i?O.pause().totalProgress(1):"reset"===i?O.restart(!0).pause():"restart"===i?O.restart(!0):O[i]()),C&&C(Ce)),!s&&et||(k&&s&&Xa(Ce,k),xe[o]&&Xa(Ce,xe[o]),ce&&(1===d?Ce.kill(!1,1):xe[o]=0),s||xe[o=1===d?1:3]&&Xa(Ce,xe[o])),pe&&!n&&Math.abs(Ce.getVelocity())>(Ua(pe)?pe:2500)&&(Wa(Ce.callbackAnimation),ee?ee.progress(1):Wa(O,"reverse"===i?1:!d,1))):ve&&C&&!Ke&&C(Ce)}if(x){var v=de?u/de.duration()*(de._caScrollDist||0):u;y(v+(Y._isFlipped?1:0)),x(v)}T&&T(-u/de.duration()*(de._caScrollDist||0))}},Ce.enable=function(e,t){Ce.enabled||(Ce.enabled=!0,wb(be,"resize",Lb),me||wb(be,"scroll",Jb),Se&&wb(ScrollTrigger,"refreshInit",Se),!1!==e&&(Ce.progress=Oe=0,A=D=Me=Be()),!1!==t&&Ce.refresh())},Ce.getTween=function(e){return e&&B?B.tween:ee},Ce.setPositions=function(e,t,r,n){if(de){var o=de.scrollTrigger,i=de.duration(),a=o.end-o.start;e=o.start+a*e/i,t=o.start+a*t/i}Ce.refresh(!1,!1,{start:Da(e,r&&!!Ce._startClamp),end:Da(t,r&&!!Ce._endClamp)},n),Ce.update()},Ce.adjustPinSpacing=function(e){if(Z&&e){var t=Z.indexOf(he.d)+1;Z[t]=parseFloat(Z[t])+e+yt,Z[1]=parseFloat(Z[1])+e+yt,Mt(Z)}},Ce.disable=function(e,t){if(Ce.enabled&&(!1!==e&&Ce.revert(!0,!0),Ce.enabled=Ce.isActive=!1,t||ee&&ee.pause(),re=0,n&&(n.uncache=1),Se&&xb(ScrollTrigger,"refreshInit",Se),te&&(te.pause(),B.tween&&B.tween.kill()&&(B.tween=0)),!me)){for(var r=Tt.length;r--;)if(Tt[r].scroller===be&&Tt[r]!==Ce)return;xb(be,"resize",Lb),me||xb(be,"scroll",Jb)}},Ce.kill=function(e,t){Ce.disable(e,t),ee&&!t&&ee.kill(),a&&delete Ct[a];var r=Tt.indexOf(Ce);0<=r&&Tt.splice(r,1),r===je&&0<Pt&&je--,r=0,Tt.forEach(function(e){return e.scroller===Ce.scroller&&(r=1)}),r||tt||(Ce.scroll.rec=0),O&&(O.scrollTrigger=null,e&&O.revert({kill:!1}),t||O.kill()),q&&[q,L,Y,F].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),nt===Ce&&(nt=0),ae&&(n&&(n.uncache=1),r=0,Tt.forEach(function(e){return e.pin===ae&&r++}),r||(n.spacer=0)),E.onKill&&E.onKill(Ce)},Tt.push(Ce),Ce.enable(!1,!1),i&&i(Ce),O&&O.add&&!N){var v=Ce.update;Ce.update=function(){Ce.update=v,R||I||Ce.refresh()},Fe.delayedCall(.01,Ce.update),N=.01,R=I=0}else Ce.refresh();ae&&function _queueRefreshAll(){if(rt!==St){var e=rt=St;requestAnimationFrame(function(){return e===St&&kt(!0)})}}()}else this.update=this.refresh=this.kill=Ha},ScrollTrigger.register=function register(e){return s||(Fe=e||Ka(),Ja()&&window.document&&ScrollTrigger.enable(),s=st),s},ScrollTrigger.defaults=function defaults(e){if(e)for(var t in e)_t[t]=e[t];return _t},ScrollTrigger.disable=function disable(t,r){st=0,Tt.forEach(function(e){return e[r?"kill":"disable"](t)}),xb(He,"wheel",Jb),xb(Ne,"scroll",Jb),clearInterval(u),xb(Ne,"touchcancel",Ha),xb(We,"touchstart",Ha),vb(xb,Ne,"pointerdown,touchstart,mousedown",Fa),vb(xb,Ne,"pointerup,touchend,mouseup",Ga),c.kill(),Ra(xb);for(var e=0;e<Ie.length;e+=3)yb(xb,Ie[e],Ie[e+1]),yb(xb,Ie[e],Ie[e+2])},ScrollTrigger.enable=function enable(){if(He=window,Ne=document,Xe=Ne.documentElement,We=Ne.body,Fe&&(Je=Fe.utils.toArray,Ve=Fe.utils.clamp,x=Fe.core.context||Ha,Ze=Fe.core.suppressOverwrites||Ha,w=He.history.scrollRestoration||"auto",Q=He.pageYOffset,Fe.core.globals("ScrollTrigger",ScrollTrigger),We)){st=1,(_=document.createElement("div")).style.height="100vh",_.style.position="absolute",Yb(),function _rafBugFix(){return st&&requestAnimationFrame(_rafBugFix)}(),k.register(Fe),ScrollTrigger.isTouch=k.isTouch,E=k.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),wb(He,"wheel",Jb),l=[He,Ne,Xe,We],Fe.matchMedia?(ScrollTrigger.matchMedia=function(e){var t,r=Fe.matchMedia();for(t in e)r.add(t,e[t]);return r},Fe.addEventListener("matchMediaInit",function(){return Sb()}),Fe.addEventListener("matchMediaRevert",function(){return Rb()}),Fe.addEventListener("matchMedia",function(){kt(0,1),U("matchMedia")}),Fe.matchMedia("(orientation: portrait)",function(){return Kb(),Kb})):console.warn("Requires GSAP 3.11.0 or later"),Kb(),wb(Ne,"scroll",Jb);var e,t,r=We.style,n=r.borderTopStyle,o=Fe.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function value(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",e=xt(We),Ye.m=Math.round(e.top+Ye.sc())||0,ze.m=Math.round(e.left+ze.sc())||0,n?r.borderTopStyle=n:r.removeProperty("border-top-style"),u=setInterval(Ib,250),Fe.delayedCall(.5,function(){return ot=0}),wb(Ne,"touchcancel",Ha),wb(We,"touchstart",Ha),vb(wb,Ne,"pointerdown,touchstart,mousedown",Fa),vb(wb,Ne,"pointerup,touchend,mouseup",Ga),f=Fe.utils.checkPrefix("transform"),ee.push(f),s=it(),c=Fe.delayedCall(.2,kt).pause(),g=[Ne,"visibilitychange",function(){var e=He.innerWidth,t=He.innerHeight;Ne.hidden?(d=e,p=t):d===e&&p===t||Lb()},Ne,"DOMContentLoaded",kt,He,"load",kt,He,"resize",Lb],Ra(wb),Tt.forEach(function(e){return e.enable(0,1)}),t=0;t<Ie.length;t+=3)yb(xb,Ie[t],Ie[t+1]),yb(xb,Ie[t],Ie[t+2])}},ScrollTrigger.config=function config(e){"limitCallbacks"in e&&(et=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(u)||(u=t)&&setInterval(Ib,t),"ignoreMobileResize"in e&&(b=1===ScrollTrigger.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(Ra(xb)||Ra(wb,e.autoRefreshEvents||"none"),h=-1===(e.autoRefreshEvents+"").indexOf("resize"))},ScrollTrigger.scrollerProxy=function scrollerProxy(e,t){var r=J(e),n=Ie.indexOf(r),o=La(r);~n&&Ie.splice(n,o?6:2),t&&(o?qe.unshift(He,t,We,t,Xe,t):qe.unshift(r,t))},ScrollTrigger.clearMatchMedia=function clearMatchMedia(t){Tt.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},ScrollTrigger.isInViewport=function isInViewport(e,t,r){var n=(lt(e)?J(e):e).getBoundingClientRect(),o=n[r?ut:ft]*t||0;return r?0<n.right-o&&n.left+o<He.innerWidth:0<n.bottom-o&&n.top+o<He.innerHeight},ScrollTrigger.positionInViewport=function positionInViewport(e,t,r){lt(e)&&(e=J(e));var n=e.getBoundingClientRect(),o=n[r?ut:ft],i=null==t?o/2:t in H?H[t]*o:~t.indexOf("%")?parseFloat(t)*o/100:parseFloat(t)||0;return r?(n.left+i)/He.innerWidth:(n.top+i)/He.innerHeight},ScrollTrigger.killAll=function killAll(e){if(Tt.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=W.killAll||[];W={},t.forEach(function(e){return e()})}},ScrollTrigger);function ScrollTrigger(e,t){s||ScrollTrigger.register(Fe)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),x(this),this.init(e,t)}ne.version="3.12.4",ne.saveStyles=function(e){return e?Je(e).forEach(function(e){if(e&&e.style){var t=j.indexOf(e);0<=t&&j.splice(t,5),j.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Fe.core.getCache(e),x())}}):j},ne.revert=function(e,t){return Sb(!e,t)},ne.create=function(e,t){return new ne(e,t)},ne.refresh=function(e){return e?Lb():(s||ne.register())&&kt(!0)},ne.update=function(e){return++Ie.cache&&Z(!0===e?2:0)},ne.clearScrollMemory=Tb,ne.maxScroll=function(e,t){return Qa(e,t?ze:Ye)},ne.getScrollFunc=function(e,t){return K(J(e),t?ze:Ye)},ne.getById=function(e){return Ct[e]},ne.getAll=function(){return Tt.filter(function(e){return"ScrollSmoother"!==e.vars.id})},ne.isScrolling=function(){return!!at},ne.snapDirectional=tb,ne.addEventListener=function(e,t){var r=W[e]||(W[e]=[]);~r.indexOf(t)||r.push(t)},ne.removeEventListener=function(e,t){var r=W[e],n=r&&r.indexOf(t);0<=n&&r.splice(n,1)},ne.batch=function(e,t){function Bp(e,t){var r=[],n=[],o=Fe.delayedCall(i,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||o.restart(!0),r.push(e.trigger),n.push(e),a<=r.length&&o.progress(1)}}var r,n=[],o={},i=t.interval||.016,a=t.batchMax||1e9;for(r in t)o[r]="on"===r.substr(0,2)&&Ta(t[r])&&"onRefreshInit"!==r?Bp(0,t[r]):t[r];return Ta(a)&&(a=a(),wb(ne,"refresh",function(){return a=t.batchMax()})),Je(e).forEach(function(e){var t={};for(r in o)t[r]=o[r];t.trigger=e,n.push(ne.create(t))}),n};function tc(e,t,r,n){return n<t?e(n):t<0&&e(0),n<r?(n-t)/(r-t):r<0?t/(t-r):1}function uc(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(k.isTouch?" pinch-zoom":""):"none",e===Xe&&uc(We,t)}function wc(e){var t,r=e.event,n=e.target,o=e.axis,i=(r.changedTouches?r.changedTouches[0]:r).target,a=i._gsap||Fe.core.getCache(i),s=it();if(!a._isScrollT||2e3<s-a._isScrollT){for(;i&&i!==We&&(i.scrollHeight<=i.clientHeight&&i.scrollWidth<=i.clientWidth||!ie[(t=mb(i)).overflowY]&&!ie[t.overflowX]);)i=i.parentNode;a._isScroll=i&&i!==n&&!La(i)&&(ie[(t=mb(i)).overflowY]||ie[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==o||(r.stopPropagation(),r._gsapAllow=!0)}function xc(e,t,r,n){return k.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&wc,onPress:n,onDrag:n,onScroll:n,onEnable:function onEnable(){return r&&wb(Ne,k.eventTypes[0],se,!1,!0)},onDisable:function onDisable(){return xb(Ne,k.eventTypes[0],se,!0)}})}function Bc(e){function yq(){return o=!1}function Bq(){i=Qa(p,Ye),S=Ve(E?1:0,i),f&&(C=Ve(0,Qa(p,ze))),l=St}function Cq(){v._gsap.y=Ia(parseFloat(v._gsap.y)+b.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",b.offset=b.cacheID=0}function Iq(){Bq(),a.isActive()&&a.vars.scrollY>i&&(b()>i?a.progress(1)&&b(i):a.resetTo("scrollY",i))}Va(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var n,i,l,o,a,c,u,s,f=e.normalizeScrollX,t=e.momentum,r=e.allowNestedScroll,d=e.onRelease,p=J(e.target)||Xe,g=Fe.core.globals().ScrollSmoother,h=g&&g.get(),v=E&&(e.content&&J(e.content)||h&&!1!==e.content&&!h.smooth()&&h.content()),b=K(p,Ye),m=K(p,ze),y=1,x=(k.isTouch&&He.visualViewport?He.visualViewport.scale*He.visualViewport.width:He.outerWidth)/He.innerWidth,w=0,_=Ta(t)?function(){return t(n)}:function(){return t||2.8},T=xc(p,e.type,!0,r),C=Ha,S=Ha;return v&&Fe.set(v,{y:"+=0"}),e.ignoreCheck=function(e){return E&&"touchmove"===e.type&&function ignoreDrag(){if(o){requestAnimationFrame(yq);var e=Ia(n.deltaY/2),t=S(b.v-e);if(v&&t!==b.v+b.offset){b.offset=t-b.v;var r=Ia((parseFloat(v&&v._gsap.y)||0)-b.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+r+", 0, 1)",v._gsap.y=r+"px",b.cacheID=Ie.cache,Z()}return!0}b.offset&&Cq(),o=!0}()||1.05<y&&"touchstart"!==e.type||n.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){o=!1;var e=y;y=Ia((He.visualViewport&&He.visualViewport.scale||1)/x),a.pause(),e!==y&&uc(p,1.01<y||!f&&"x"),c=m(),u=b(),Bq(),l=St},e.onRelease=e.onGestureStart=function(e,t){if(b.offset&&Cq(),t){Ie.cache++;var r,n,o=_();f&&(n=(r=m())+.05*o*-e.velocityX/.227,o*=tc(m,r,n,Qa(p,ze)),a.vars.scrollX=C(n)),n=(r=b())+.05*o*-e.velocityY/.227,o*=tc(b,r,n,Qa(p,Ye)),a.vars.scrollY=S(n),a.invalidate().duration(o).play(.01),(E&&a.vars.scrollY>=i||i-1<=r)&&Fe.to({},{onUpdate:Iq,duration:o})}else s.restart(!0);d&&d(e)},e.onWheel=function(){a._ts&&a.pause(),1e3<it()-w&&(l=0,w=it())},e.onChange=function(e,t,r,n,o){if(St!==l&&Bq(),t&&f&&m(C(n[2]===t?c+(e.startX-e.x):m()+t-n[1])),r){b.offset&&Cq();var i=o[2]===r,a=i?u+e.startY-e.y:b()+r-o[1],s=S(a);i&&a!==s&&(u+=s-a),b(s)}(r||t)&&Z()},e.onEnable=function(){uc(p,!f&&"x"),ne.addEventListener("refresh",Iq),wb(He,"resize",Iq),b.smooth&&(b.target.style.scrollBehavior="auto",b.smooth=m.smooth=!1),T.enable()},e.onDisable=function(){uc(p,!0),xb(He,"resize",Iq),ne.removeEventListener("refresh",Iq),T.kill()},e.lockAxis=!1!==e.lockAxis,((n=new k(e)).iOS=E)&&!b()&&b(1),E&&Fe.ticker.add(Ha),s=n._dc,a=Fe.to(n,{ease:"power4",paused:!0,scrollX:f?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:pc(b,b(),function(){return a.pause()})},onUpdate:Z,onComplete:s.vars.onComplete}),n}var oe,ie={auto:1,scroll:1},ae=/(input|label|select|textarea)/i,se=function _captureInputs(e){var t=ae.test(e.target.tagName);(t||oe)&&(e._gsapAllow=!0,oe=t)};ne.sort=function(e){return Tt.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},ne.observe=function(e){return new k(e)},ne.normalizeScroll=function(e){if(void 0===e)return v;if(!0===e&&v)return v.enable();if(!1===e)return v&&v.kill(),void(v=e);var t=e instanceof k?e:Bc(e);return v&&v.target===t.target&&v.kill(),La(t.target)&&(v=t),t},ne.core={_getVelocityProp:L,_inputObserver:xc,_scrollers:Ie,_proxies:qe,bridge:{ss:function ss(){at||U("scrollStart"),at=it()},ref:function ref(){return Ke}}},Ka()&&Fe.registerPlugin(ne),e.ScrollTrigger=ne,e.default=ne;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

