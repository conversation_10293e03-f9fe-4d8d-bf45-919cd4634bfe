<?php

class ImageServe extends CI_Controller {
	public function __construct() {
		parent::__construct();

		$this->classloader->load("common", "Image");
		$this->load->library("thumbnails");
	}

	public function index() {
		$params = Thumbnails::parse_servename($this->input->get());
		$file = $params["file"];
		$index = $params["index"];
		$path = $params["path"];

		$file = basename($file);
		if ($index === false) {
			$index = "";
		}

		$ok = false;
		if (self::is_image($file)) {
			$res = $this->thumbnails->get($file, $index, $path);

			if ($res) {
				$this->thumbnails->send_image($res, $file);
				$ok = true;
			}
		}
		if (!$ok) {
			$this->thumbnails->send_empty();
		}
	}
	public function gc() {
		if ($this->input->is_cli_request()) {
			$this->thumbnails->gc();
		}
	}

	private static function is_image($file) {
		$ext = pathinfo($file, PATHINFO_EXTENSION);
		return in_array(strtolower($ext), array("jpg", "jpeg", "png", "gif", "svg", "webp"));
	}
}