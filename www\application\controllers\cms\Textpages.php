<?php

require_once "base/Edit_child_nodes.php";

class Textpages extends Edit_child_nodes {
	
	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
		);
	}

	protected function parent_nodes() {
		return "textpages";
	}

	protected function delete_object(ci_bean $object) {
		if ($object->id < 1000) {
			throw new CmsException("Not allowed");
		}
		$this->data->delete($object);
	}

	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);
		
		if ($object->id < 1000) {
			$this->set_opt("delete", false);
		}
	}

	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		$object->kind = node::KIND_TEXTPAGE;
	}
	
	protected function objects_filter() {
		return array(
				"parent_ids" => $this->parent_ids,
				"kinds" => node::KIND_TEXTPAGE,
		);
	}
}