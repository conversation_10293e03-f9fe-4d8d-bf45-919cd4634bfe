/**********************************************************
 * Wrap containers
**********************************************************/

.g-wrap {
	// Wrapper widths are defined in _variables.scss
	$wrap-reset: $wrapper + 2*$wrapper-padding-md;

    /* Limited width */
	margin-left: auto;
	margin-right: auto;
	max-width: $wrapper;
	padding-left: $wrapper-padding;
	padding-right: $wrapper-padding;
	width: 100%;

    /* Global wrapper - prevents horizontal scroll */
    &--main {
		clear: both; 
		min-height: 100%;
		overflow: hidden;
		position: relative;
		z-index: 3;
	}

	&--spaced {
		padding-left: $wrapper-padding;
		padding-right: $wrapper-padding;
	}

	&--xxl {
		max-width: $wrapper-xxl;
	}

	&--xl {
		max-width: $wrapper-xl;
	}

	&--lg {
		max-width: $wrapper-lg;
	}

	&--xm {
		max-width: $wrapper-xm;
	}

	&--md {
		max-width: $wrapper-md;
	}

	&--sm {
		max-width: $wrapper-sm;
	}


	@include media(md) {
		padding-left: $wrapper-padding-md;
		padding-right: $wrapper-padding-md;
	}

	// Remove padding when there's enough space on both sides
	// @media screen and (min-width: #{$wrap-reset}) {
	// 	padding-left: 0;
	// 	padding-right: 0;

	// 	&--spaced {
	// 		padding-left: $wrapper-padding-md;
	// 		padding-right: $wrapper-padding-md;
	// 	}
	// }
	
	@include media(xl) {
		&--spaced {
			padding: 0 5rem;
		}
	}
}
