<?php

class FileServe extends CI_Controller {
	public function __construct() {
		parent::__construct();

		$this->classloader->load("common");
		$this->load->library("filemanager");
		$this->load->library("datamanager", null, "data");
	}

	public function index() {

		$file = $this->input->get("f");
		$key = $this->input->get("k");
		$asset = $this->input->get("a");
		$dl = $this->input->get("dl");
		if ($dl === false) {
			$dl = true;
		}

		$file = basename($file);

		$ok = false;
		if ($file) {
			if ($asset) {
				$asset = $this->data->assets->by_file($file);
				if ($asset) {
					$absfile = $this->filemanager->get_file($file, $key, true);
					file_send($absfile, $asset->fileinfo("file")->orig_name, $dl);

					$ok = true;
				}
			} else {
				$absfile = $this->filemanager->get_file($file, $key, true);
				if (file_exists($absfile)) {
					file_send($absfile, $file, $dl);

					$ok = true;
				}
			}
		}
		if (!$ok) {
			show_404();
		}

	}
}