<?php

require_once __DIR__ . "/Node_base.php";

abstract class Public_base extends Node_base {

	public function __construct() {
		parent::__construct();
		
		$this->view->set("googlemap_key", $this->_var("googlemap-key"));

		$this->add_js_var("message", $this->get_flash("message"));
		$this->add_js_var("error", $this->get_flash("error"));
		$this->add_js_var("locale", $this->lng->locale);

		$nav = $this->get_nodes("services", "about", "partner", "calculator", "faq", "news", "contacts");
		$this->view->set("nav", $nav);
		
		$textpages = $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("textpages")));
		$footer_nav = array_merge($nav, $textpages);
		$this->view->set("footer_nav", $footer_nav);
	}

	protected function load_ld($node) {
		$res = array();

		$this->view->set("ld", $res);
	}
	
	protected function add_subscriber($name, $email) {
		if (!$this->data->subscribers->test_email($email, 0)) {
			return false;
		}

		$object = new subscriber();
		$object->name = $name;
		$object->email = $email;
		$object->time = conv_date_string();
		$object->ip_address = $_SERVER['REMOTE_ADDR'];
		$object->activation_key = my_random_string(32);
		$object->is_active = 1;
		$this->data->add($object);

		$model = array(
			"app_domain" => conf("app_domain"),
		);

		$subject = $this->diction->get("newsletter.subject");
		$this->create_email_send("{$this->lng->code}/newsletter", $model, $subject, $object->email, "html");

		return true;
	}
}
