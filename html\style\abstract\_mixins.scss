//=========================================================
// Transitions
//=========================================================

@mixin transition-lg($delay: 0, $bezier: false) {
	transition: all 1000ms ease-in-out #{$delay}ms;
	@if $bezier {
		transition-timing-function: cubic-bezier(0.75,0,0.5,1);
	}
}
@mixin transition-xm($delay: 0, $bezier: false) {
	transition: all 800ms cubic-bezier(0.75,0,0.5,1) #{$delay}ms;
	@if $bezier == false {
		transition-timing-function: ease-in-out;
	}
}
@mixin transition-md($delay: 0, $bezier: false) {
	transition: all 500ms cubic-bezier(0.75,0,0.5,1) #{$delay}ms;
	@if $bezier == false {
		transition-timing-function: ease-in-out;
	}
}
@mixin transition-sm($delay: 0, $bezier: false) {
	transition: all 250ms cubic-bezier(0.75,0,0.5,1) #{$delay}ms;
	@if $bezier == false {
		transition-timing-function: ease-in-out;
	}
}
@mixin transition-xs($delay: 0, $bezier: false) {
	transition: all 100ms cubic-bezier(0.75,0,0.5,1) #{$delay}ms;
	@if $bezier == false {
		transition-timing-function: ease-in-out;
	}
}



//================================================
// Gradients
//================================================




//================================================
// Helpers
//================================================

@mixin vertical-align {
	position: relative;
	&::before { content: ''; display: inline-block; height: 100%; vertical-align: middle; }
	img { vertical-align: middle; }
}

// Create a circle
@mixin circle($size) {
	align-items: center;
	border-radius: 50%;
	display: inline-flex;
	height: #{$size}px;
	justify-content: center;
	width: #{$size}px;
}

// Add vendor prefixes
@mixin vendor($property, $value: 'none') {
	#{$property}: #{$value};
	-moz-#{$property}: #{$value};
	-webkit-#{$property}: #{$value};
	-o-#{$property}: #{$value};
	-ms-#{$property}: #{$value};
}

// SVG icons
@mixin icon($name, $width: false, $height: false) {
	background-image: url('#{$img-dir}/icon/#{$name}.svg');
	@if $height {
		height: #{$height}px;
	}
	@if $width {
		width: #{$width}px;
	}
}

// Animated link underline
@mixin underline($bottom: 0, $bg: 'white', $bgHover: 'white') {
	@include transition-xs;
	display: inline-block;
	position: relative;

	&::after {
		@include transition-xs;
		background-color: #{$bg};
		height: 1px;
		bottom: #{$bottom}px;
		content: '';
		left: 0;
		position: absolute;
		width: 0;
	}

	&:hover {
		&::after {
			background-color: #{$bgHover};
			width: 100%;
		}
	}
}


/* Container covering its entire parent */
@mixin fullscreen {
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}