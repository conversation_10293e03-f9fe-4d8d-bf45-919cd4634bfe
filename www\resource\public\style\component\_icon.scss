/**********************************************************
 * Icon styles
**********************************************************/

.icon {
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    flex-shrink: 0;

    /* Space between icon/label */
    &--spaced {
        vertical-align: middle;

        &:first-child:not(:last-child) {
            margin-right: 1rem;
        }

        &:last-child:not(:first-child) {
            margin-left: 1rem;
        }
    }


    /*  Alphabetical list of SVG icons
        Format: @include icon('name', width, height)
        See abstract/mixins.scss for the icon mixin
    */
    
    &--arrow-left { @include icon('arrow-left', 22, 15); }
    &--arrow-right { @include icon('arrow-right', 22, 15); }
    &--arrow-left-gray { @include icon('arrow-left-gray', 34, 15); }
    &--arrow-right-gray { @include icon('arrow-right-gray', 34, 15); }
    &--caret { @include icon('caret', 12, 6); }
    &--check-sm { @include icon('check-sm', 12, 10); }
    &--nav-open { @include icon('nav-open', 32, 15); }
    &--nav-close { @include icon('nav-close', 24, 24); }
    &--num-minus { @include icon('num-minus', 11, 3); }
    &--num-plus { @include icon('num-plus', 11, 11); }
    &--page-prev { @include icon('page-prev', 12, 12); }
    &--page-next { @include icon('page-next', 12, 12); }
    &--play { @include icon('play', 33, 33); }
    &--handshake { @include icon('handshake', 24, 24); }
    &--chat { @include icon('chat', 24, 24); }
    &--chat-white { @include icon('chat-white', 24, 24); }
    &--phone { @include icon('phone', 24, 24); }
    &--arrow-btn-primary { @include icon('arrow-btn-primary', 24, 24); }
    &--arrow-btn-white { @include icon('arrow-btn-white', 24, 24); }
    &--arrow-btn-black { @include icon('arrow-btn-black', 24, 24); }
    &--info { @include icon('info', 24, 24); }
    &--share { @include icon('share', 24, 24); }
    &--roadmap { @include icon('roadmap', 14, 14); }
    &--arrow-btn-left { @include icon('arrow-btn-left', 25, 25); }

}