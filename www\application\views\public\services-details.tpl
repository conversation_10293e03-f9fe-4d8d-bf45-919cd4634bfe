{extends "./base.tpl"}

{$wrapper_css="g-bg-light-gray"}
{$header_cta_css="g-pin-sign--right"}
{$footer_cta=true}

{block "content"}
	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<a class="btn g-mb-20" href="{$node_services|path}">
				<i class="icon icon--arrow-btn-left"></i>
				<span class="underline">{@misc.services_all}</span>
			</a>
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					{$node->title}
				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					{$node->feat2 nofilter}
				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						{$node->text|pt nofilter}
					</p>
				</div>
				<ul>
					<li>
						<a class="btn btn--md btn--primary" href="{$node_contacts|path}">
							{@misc.free_consult}
							<i class="icon icon--chat-white"></i>
						</a>
					</li>
				</ul>
			</div>
		</div>
	</section>
	{include "./_partial/feat-1.tpl" node=$feat show_link=true show_title=false}
	<section class="g-padded--sm">
		<div class="g-wrap">
			<h2 class="g-title g-title--md js-anim">
				{$params->title}
			</h2>
			<div class="g-row">
				{include "./_partial/feat-4.tpl" list=$params_feat}
			</div>
		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-bg">
				<div class="g-padded--md g-wrap">
					<div class="g-head">
						<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
							{$types->title}
						</p>
						<h1 class="g-title g-title--md">
							{$types->subtitle}
						</h1>
					</div>
					<div class="services services--inner">
						{foreach $textblocks as $i}
						<div class="services__item js-anim">
							<div class="services__col">
								<div class="services__content">
									<a {if $i->feat2}href="{$i->feat2|url}"{/if} class="g-title g-title--sm g-mb-0 g-color-primary">
										{$i->title}
									</a>
									<a class="services__img-box is-hidden--md" {if $i->feat2}href="{$i->feat2|url}"{/if}>
										<img class="services__img" src="{$i->image|image:feat2}" alt="">
									</a>
									<div class="g-editor g-opacity-60">
										<p>
											{$i->text|pt nofilter}
										</p>
									</div>
									{if $i->feat2}
									<ul>
										<li>
											<a class="btn btn--md btn--primary" href="{$i->feat2|url}">
												{$i->feat1}
												<i class="icon icon--arrow-btn-white"></i>
											</a>
										</li>
									</ul>
									{/if}
								</div>
							</div>
							<a class="services__img-box is-hidden is-block--md" {if $i->feat2}href="{$i->feat2|url}"{/if}>
								<img class="services__img" src="{$i->image|image:feat2}" alt="">
							</a>
						</div>
						{/foreach}
					</div>
				</div>
			</div>
		</div>
	</section>
	{include "./_partial/feat-2.tpl" node=$client}
	{include "./_partial/benefits.tpl"}
	{include "./_partial/faq.tpl" node=$faq}
	{include "./_partial/contacts.tpl"}
{/block}
