<?php

class Info extends CI_Controller {
	public function __construct() {
		parent::__construct();

		if (!in_array($this->input->ip_address(), conf("service_allow_ip"))) {
			die("not allowed");
		}
	}

	public function index() {
		$host = php_uname("n");
		//$host = gethostname();
		echo "<pre>";
		echo "HOST:\t" . $host . "\n";
		echo "IP:\t" . gethostbyname($host) . "\n";
		echo "USER:\t". exec("whoami") . "\n";
		echo "DIR:\t" . getcwd() . "\n";
		echo "</pre>";
		phpinfo();
	}
}