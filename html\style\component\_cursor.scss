/**********************************************************
 * 
**********************************************************/

$parent: '.cursor';

#{$parent} {
    @include circle(74);
    align-items: center;
    background-color: rgba(gray, 0.75);
    display: flex;
    font-size: $font-body-sm;
    justify-content: center;
    left: 0;
    line-height: 1em;
    opacity: 0;
    pointer-events: none;
    position: fixed;
    top: 0;
    transition: opacity 0.2s, visibility 0.2s;
    visibility: hidden;
    z-index: 10;
    
    
    &.-show {
        opacity: 1;
        visibility: visible;
    }

    &.-ignore {
        opacity: 0 !important;
        visibility: hidden;
    }

    &::after {
        @include circle(100);
        animation: 30s linear infinite rotateRight;
        border: 1px dashed white;
        content: '';
        left: 50%;
        pointer-events: none;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        transform-origin: center;
    }
}