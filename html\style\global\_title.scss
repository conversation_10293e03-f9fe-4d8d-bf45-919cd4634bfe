/**********************************************************
 * Headings
**********************************************************/

.g-title {
	display: block;
	line-height: 1em;
	margin-bottom: 2rem;
	font-family: $font-title;

	/* Size */
	&--xl {
		font-size: clamp(3.2rem, 6vw, $font-title-xl);
	}

	&--lg {
		font-size: clamp(3.2rem, 5vw, $font-title-lg);
	}

	&--xm {
		font-size: clamp($font-title-xs, 3.6vw, $font-title-xm);
	}

	&--m {
		font-size: clamp($font-body-xs, 3vw, $font-title-m);
	}

	&--md {
		font-size: clamp($font-body-lg, 3vw, $font-title-md);
	}

	&--sm {
		font-size: clamp($font-body-lg, 2vw, $font-title-sm);
		// line-height: 1.4em;
	}

	&--xs {
		font-size: clamp($font-body-xm, 1.5vw, $font-title-xs);
		// line-height: 1.4em;
	}

	&--sub {
		display: inline-flex;
		gap: .5rem;
		align-items: center;
		&:before {
			content: '';
			width: 9px;
			min-width: 9px;
			height: 9px;
			background-color: currentColor;
			position: relative;
			border-radius: 50%;
		}
	}


	/* Variations */
	&--color {
		span {
			color: $color-primary !important;
		}
	}

	&--color-black {
		span {
			color: $color-black !important;
		}
	}

	&--inline {
		display: inline-block;
	}

	&--center {
		align-items: center;
		text-align: center;
	}

	&--no-bottom {
		margin-bottom: 0 !important;
	}

	&--underline {
		line-height: 1.2em;

		span {
			position: relative;
			display: inline-flex;
			flex-direction: column;

			&::after {
				content: '';
				position: relative;
				width: 100%;
				height: 0.3em;
				background: url('#{$img-dir}/underline-sm.svg') no-repeat top center;
				object-fit: contain;
			}
		}
	}

	&--underline-primary {
		line-height: 1.2em;

		span {
			position: relative;
			display: inline-flex;
			flex-direction: column;
			color: $color-primary;

			&::after {
				content: '';
				position: relative;
				width: 100%;
				height: 0.3em;
				background: url('#{$img-dir}/underline-primary.svg') no-repeat top center;
				object-fit: contain;
			}
		}
	}




	@include media(xm) {
		margin-bottom: 3rem;
	}
}