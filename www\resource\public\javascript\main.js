$(document).ready(function () {

	autosize($('textarea'));

	new VenoBox({
		selector: '.-venobox',
		fitView: true
	});

	var $slickCarousels = $('.facts__carousel, .audience-panel__carousel, .related__carousel, .related__img-slider, .article-carousel, .review-carousel, .accorion-slider, .near__vertical, .near__bg, .accordion__team-img-box');

	$slickCarousels.on('init', function (e) {
		Waypoint.refreshAll();
		ScrollTrigger.refresh();
	});

	(function () {
		var lastScroll = $(window).scrollTop();
		var lenis = window.lenis || null;

		function makeSticky() {
			var $header = $('.header');
			var $nav = $header.find('.nav');

			if ($('body').hasClass('-no-scroll')) return;

			if ($(window).scrollTop() > 50) {
				$header.addClass('-sticky');

				if (lastScroll < $(window).scrollTop()) {
					$header.addClass('-hidden');
				} else {
					$header.removeClass('-hidden');
				}

				lastScroll = $(window).scrollTop();
			} else {
				$header.removeClass('-sticky -hidden');
				$nav.removeClass('-open');
			}
		}

		$(window).on('scroll', makeSticky);

		$('.header__nav-trigger').on('click', function (e) {
			e.preventDefault();
			e.stopPropagation();
			$('.header').toggleClass('-active');
			$('.header').removeClass('-hidden');
			$('.nav').toggleClass('-active');
			$('body').toggleClass('-no-scroll');

			if ($('body').hasClass('-no-scroll')) {
				$('html').css('overflow', 'hidden');
			} else {
				$('html').css('overflow', '');
			}
		});

		$('.nav__link').on('click', function (e) {
			e.preventDefault();
			e.stopPropagation();
			$('.header').removeClass('-active');
			$('.nav').removeClass('-active');
			$('body').removeClass('-no-scroll');
			$('html').css('overflow', '');
		});

	})();


	(function () {
		function animateScroll(target) {
			if ($(target).length > 0) {
				var offset = $(target).offset().top - 50;
				$('html, body').animate({ scrollTop: offset }, 800);
			}
		}

		$('.js-scroll-to').on('click', function (e) {
			e.preventDefault();

			var target = $(this).attr('href');

			setTimeout(function () {
				animateScroll(target);
			}, 100);
		});
	})();


	$(".calculator__input-price").ionRangeSlider({
		min: 5000,
		max: 300000,
		from: 5000,
		onChange: function (data) {
			$('.price-current').text(data.from_pretty);
		}
	});
	$(".calculator__input-years").ionRangeSlider({
		min: 2,
		max: 20,
		from: 2,
		onChange: function (data) {
			$('.years-current').text(data.from_pretty);
		}
	});

	(function () {
		$('.chart__tab').on('click', function (e) {
			let dataId = $(this).attr("data-id");
			$('.chart__tab').removeClass("-current");
			$(this).addClass("-current");
			$('.chart__table').removeClass("-visible");
			$(".chart__table").eq(dataId).addClass("-visible");
		});
	})();

	$('.facts').each(function () {
		var $t = $(this);

		$t.find('.facts__carousel').slick({
			arrows: true,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 1000,
			infinite: true,
			variableWidth: false,
			autoplay: true,
			autoplaySpeed: 1000,
			responsive: [
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 4,
					}
				}
			]

		});
	});

	$('.audience-panel').each(function () {
		var $t = $(this);
		var $apartment = $(this).find('.audience-panel__carousel');
		var $tab = $(this).find('.audience-panel__tab');
		$tab.on("click", function (e) {
			e.preventDefault();
			$tab.removeClass("-current");
			$(this).addClass("-current");
			var dataId = $(this).attr("data-id");
			$apartment.slick('slickGoTo', dataId);
		});

		$apartment.slick({
			arrows: false,
			prevArrow: null,
			infinite: true,
			dots: false,
			slidesToShow: 1,
			slidesToScroll: 1,
			mobileFirst: true,
			adaptiveHeight: true,
			fade: true,
		}).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
			var $currentTab = $t.find('.audience-panel__tab').filter(function (item) {
				return nextSlide === $(this).data('id');
			});

			$t.find(".audience-panel__tab").removeClass("-current");
			$currentTab.addClass('-current');
		});
	});

	$('.related').each(function () {
		var $t = $(this);
		var $mainSlider = $t.find('.related__carousel');
		var $imgSlider = $t.find('.related__img-slider')

		$mainSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			prevArrow: $t.find('.related__btn--prev'),
			nextArrow: $t.find('.related__btn--next'),
			fade: true,
			asNavFor: $imgSlider,
			mobileFirst: true,
			infinite: true,
		});
		$imgSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			asNavFor: $mainSlider,
			dots: false,
			focusOnSelect: true,
			arrows: false,
			variableWidth: false,
			infinite: true,
			mobileFirst: true,
		});
	});

	$('.article-carousel').each(function () {
		var $t = $(this);

		$t.slick({
			arrows: false,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			responsive: [
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 3,
					}
				}
			]
		});
	});

	$('.review-carousel').each(function () {
		var $t = $(this);

		$t.slick({
			prevArrow: ('.review-carousel__btn--prev'),
			nextArrow: ('.review-carousel__btn--next'),
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			responsive: [
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 2,
					}
				}
			]
		});
	});

	$('.accorion-slider').each(function () {
		var $t = $(this);
		var $carousel = $t.find('.accordion__team-img-box');
		var $tab = $t.find('.accordion__item');
		$tab.on("click", function (e) {
			e.preventDefault();
			var dataId = $(this).attr("data-id");
			$carousel.slick('slickGoTo', dataId);
		});

		$carousel.slick({
			arrows: false,
			prevArrow: null,
			infinite: true,
			dots: false,
			slidesToShow: 1,
			slidesToScroll: 1,
			mobileFirst: true,
			adaptiveHeight: true,
			fade: true,
		}).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
			var $currentTab = $t.find('.accordion__item').filter(function (item) {
				return nextSlide === $(this).data('id');
			});
		});
	});

	$('.near').each(function () {
		var $t = $(this);
		var $mainSlider = $t.find('.near__vertical');
		var $imgSlider = $t.find('.near__bg')

		$mainSlider.slick({
			arrows: true,
			slidesToShow: 3,
			slidesToScroll: 1,
			arrows: false,
			fade: false,
			asNavFor: $imgSlider,
			mobileFirst: true,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			vertical: true,
			verticalSwiping: true,
			useTransform: true,
			cssEase: 'cubic-bezier(0.645, 0.045, 0.355, 1.000)',
			adaptiveHeight: true,
			centerMode: true,
			centerPadding: '60px',
		});
		$imgSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			asNavFor: $mainSlider,
			dots: false,
			focusOnSelect: true,
			arrows: false,
			variableWidth: false,
			infinite: true,
			mobileFirst: true,
			autoplay: true,
			autoplaySpeed: 3000,
			fade: false,
		});
	});

	$('.steps').each(function () {
		var $t = $(this);

		$t.slick({
			// prevArrow: ('.review-carousel__btn--prev'),
			// nextArrow: ('.review-carousel__btn--next'),
			arrows: true,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: false,
			responsive: [
				{
					breakpoint: 450,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 3,
					}
				},
				{
					breakpoint: 768,
					settings: {
						slidesToShow: 4,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 5,
					}
				},
				{
					breakpoint: 1280,
					settings: "unslick"
				}
			]
		});
	});


	$('.accordion').each(function () {
		var button = $(this).find('.accordion__control');
		if (button.hasClass("-active-control")) {
			$(".-active-control").next(".accordion__panel").slideDown(400);
		}
		$(this).on('click', '.accordion__control', function (e) {
			e.preventDefault();
			var $t = $(this);

			if (!$t.hasClass("-active-control")) {
				$(".accordion__panel").slideUp(400);
				$(".accordion__control").removeClass("-active-control");
			}

			$t.toggleClass("-active-control");
			$t.next().slideToggle();
			$('.accordion__slider').slick('refresh');
		});

		$("accordion__panel:visible").each(function () {
			console.log($t.attr('id'));
		});
	});
	$('.partners-accordion').each(function () {
		const $accordion = $(this);

		$accordion.find('.partners-accordion__header.-open').each(function () {
			$(this).next('.partners-accordion__content').slideDown(0);
			$(this).closest('.partners-accordion__item').addClass('-active');
		});

		$accordion.on('click', '.partners-accordion__header', function (e) {
			e.preventDefault();

			const $header = $(this);
			const $item = $header.closest('.partners-accordion__item');
			const $content = $header.next('.partners-accordion__content');

			if (!$header.hasClass('-open')) {
				$accordion.find('.partners-accordion__header').removeClass('-open');
				$accordion.find('.partners-accordion__item').removeClass('-active');
				$accordion.find('.partners-accordion__content').slideUp(400);
			}

			$header.toggleClass('-open');
			$item.toggleClass('-active');
			$content.stop(true, true).slideToggle(400);
		});
	});

	$('.consent').each(function () {
		var $t = $(this);

		if (!localStorage.getItem('cookieConsent')) {
			$t.addClass('-is-shown');
		}

		$t.find('.consent__btn').on('click', function (e) {
			e.preventDefault();

			localStorage.setItem('cookieConsent', true);
			$t.removeClass('-is-shown');
		});
	});

	if (app.vars.message) {
		app.alert(app.vars.message);
	}

	if (app.vars.error) {
		app.error(app.vars.error);
	}

	$(".googlemap").each(function () {
		new Googlemap($(this));
	});


	$("body").on("submit", "form.ajax", function (e) {
		e.preventDefault();
		var t = $(this);
		if (t.data("submit")) {
			return false;
		}

		var submit = function () {
			t.data("submit", 1);
			app.loader(" ");
			$.ajax({
				url: t.attr("action"),
				data: new FormData(t[0]),
				cache: false,
				contentType: false,
				processData: false,
				type: "POST",
				success: function (res) {
					app.loader();
					if (res.success) {
						if (res.message) {
							app.alert(res.message);
						}
						if (res.location) {
							window.location = res.location;
						}
						if (res.refresh) {
							window.location.reload();
						}
					} else {
						e.stopImmediatePropagation();
						if (res.message instanceof Array) {
							app.errors(res.message);
						} else {
							app.error(res.message);
						}
						if (res.errors) {
							t.find(":input").removeClass("error");
							$.each(res.errors, function (index, el) {
								t.find(":input[name=" + el + "]").addClass("error");
							});
						}
					}
				},
				complete: function () {
					t.data("submit", 0);
				}
			});
		}
		submit();
	});

	$("form.calculator").each(function() {
		const t = $(this);
		const interest = 0.027;

		function calc() {
			const years = parseInt(t.find("[name=years]").val());
			const amount = parseInt(t.find("[name=amount]").val());
			const terms = years * 12;
			const monthlyRate = interest / 12;
			const numerator = monthlyRate * Math.pow(1 + monthlyRate, terms);
			const denominator = Math.pow(1 + monthlyRate, terms) - 1;
			const res = amount * (numerator / denominator);
			t.find(".monthly_res").text(res.toLocaleString(app.vars.locale, { maximumFractionDigits: 2 }));
			$(".total_res").text((res * terms).toLocaleString(app.vars.locale, { maximumFractionDigits: 2 }));
		}
		t.find("input").on("change", function() {
			calc();
		});
		calc();
	});



});