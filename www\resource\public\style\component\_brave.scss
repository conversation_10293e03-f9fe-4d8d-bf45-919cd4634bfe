.brave {
    background-color: rgba($color-white, .6);
    border-radius: 3px;
    backdrop-filter: blur(20px);
    border: 1px solid $color-gray-light;
    padding: 5rem 2rem;

    &__text {
        display: none;
    }

    &__container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 0;
        margin-top: 5rem;
    }

    &__circle {
        position: relative;
        width: 12rem;
        height: 12rem;
        min-width: 12rem;

        &--primary {
            fill: $color-primary;
        }
    }

    &__logo {
        width: 10rem;
    }

    &__icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 38%;
    }

    &__middle {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    &__line {
        position: relative;
        width: 1px;
        height: 10rem;
        background-color: $color-gray;
    }

    @include media(768px) {
        &__container {
            flex-direction: row;
            margin-top: 0;
            gap: 3rem;
        }

        &__text {
            display: block;
        }

        &__circle {
            width: 6vw;
            height: 6vw;
            min-width: 6vw;
        }

        &__logo {
            width: 5vw;
        }

        &__line {
            width: 100%;
            height: 0.1rem;

            &:after {
                content: '';
                position: absolute;
                width: 5px;
                height: 5px;
                background-color: $color-gray;
                border-radius: 50%;
                top: 50%;
                transform: translateY(-50%);
            }

            &--left {
                &:after {
                    left: 0;
                }
            }

            &--right {
                &:after {
                    right: 0;
                }
            }
        }
    }
}