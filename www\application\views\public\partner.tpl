{extends "./base.tpl"}

{$header_cta_css="g-pin-sign--right"}
{$footer_cta=true}

{block "content"}
	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					{$node->title}
				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					{$node->subtitle nofilter}
				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						{$node->text|pt nofilter}
					</p>
				</div>
			</div>
		</div>
	</section>
	{include "./_partial/feat-1.tpl" node=$node show_link=false show_title=false}
	{include "./_partial/feat-5.tpl" node=$node_partner_become}
	<section class="g-padded--sm">
		<div class="g-wrap">
			<div class="g-head">
				<h2 class="g-title g-title--md">
					{$node_partner_exp->text|pt nofilter}
				</h2>
			</div>
			<div class="g-row">
				{foreach $exp_feat as $i}
					<div class="col-1 col-md-1-2 col-xm-1-3">
						<div class="box">
							<img class="box__icon" src="{$i->image2|image:icon2}" alt="">
							<div class="box__content">
								<p class="g-bold g-mb-10 g-opacity-60">
									{$i->title}
								</p>
								<div class="g-editor g-opacity-60">
									<p>
										{$i->text|pt nofilter}
									</p>
								</div>
							</div>
						</div>
					</div>
					{if $i@index && ($i@index+1)%2==0}
						<div class="col-1 col-md-1-2 col-xm-1-3"></div>
						<div class="col-1 col-md-1-2 col-xm-1-3"></div>
					{/if}
				{/foreach}
			</div>
		</div>
	</section>
	{include "./_partial/feat-2.tpl" node=$node_partner_process}
	{include "./_partial/benefits.tpl"}
	{include "./_partial/faq.tpl" node=$node_partner_faq}
	{include "./_partial/reviews.tpl" node=$node_partner_reviews}
	{include "./_partial/contacts.tpl"}
{/block}