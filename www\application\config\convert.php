<?php 

$config["convert"]["months"] = array(
		"bg" => array("януари", "февруари", "март", "април", "май", "юни", "юли", "август", "септември", "октомври", "ноември", "декември"),
		"bg_short" => array("яну", "фев", "мар", "апр", "май", "юни", "юли", "авг", "сеп", "окт", "ное", "дек"),
		"en" => array("january", "february", "march", "april", "may", "june", "july", "august", "september", "october", "november", "december"),
		"de" => array("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktober", "November", "Dezember"),
		"ru" => array("январь", "февраль", "март", "апрель", "май", "июнь", "июль", "август", "сентябрь", "октябрь", "ноябрь", "декабрь"),
		"gr" => array("ιανουάριος", "φεβρουάριος", "μάρτιος", "απρίλιος", "μάιος", "ιούνιος", "ιούλιος", "αυγούστου", "σεπτέμβριος", "οκτώβριος", "νοέμβριος", "δεκέμβριος"),
);

$config["convert"]["weekdays"] = array(
		"bg" => array("понеделник", "вторник", "сряда", "четвъртък", "петък", "събота", "неделя"),
		"bg_short" => array("пон", "вто", "сря", "чет", "пет", "съб", "нед"),
		"en" => array("monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"),
);

$config["convert"]["dt_formats"] = array(
		"datetime" => array(
				"iso" => "Y-m-d H:i:s",
				"iso_full" => DATE_ISO8601,
				"bg" => "d.m.Y г. H:i:s",
				"en" => "m/d/y H:i A",
				"rss" => "D, d M Y H:i:s O",
		),
		"date" => array(
				"iso" => "Y-m-d",
				"bg" => "d.m.Y г.",
				"ru" => "d.m.Y г.",
				"en" => "m/d/y",
				"de" => "m/d/y",
				"gr" => "m/d/y",
				"de" => "m/d/y",
				"month" => "m / Y",
				"bg_long" => "{d} {bg_month} {Y}",
				"en_long" => "{d} {en_month} {Y}",
				"bg_short" => "{d} {bg_month}",
				"en_short" => "{d} {en_month}",
				"ru_short" => "{d} {ru_month}",
		),
		"time" => array(
				"iso" => "H:i:s",
				"bg" => "H:i:s",
				"en" => "H:i A",
				"short" => "H:i",
				"hm" => "{tot_h}:{min}",
		),
);

$config["convert"]["price_formats"] = array(
		"int" => array(0, ".", " "),
		"def" => array(2, ".", " "),
		"bg" => array(2, ",", " "),
		"en" => array(2, ".", " "),
		"dec" => array(2, ".", ""),
);
