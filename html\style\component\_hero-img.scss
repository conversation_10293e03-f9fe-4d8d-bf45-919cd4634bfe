.hero-img {
    position: relative;
    width: 100%;

    &__box {
        position: relative;
        width: 100%;
        border-radius: 3px;
        overflow: hidden;
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); // Semi-transparent overlay
        }
    }

    &__content {
        position: absolute;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        left: 0;
        bottom: 0;
        padding: 2.5vw 6vw;
        color: $color-white;
    }

    &__title {
        line-height: 1.2em;
    }

    /* 1024 down */
    @include media(xm-down) {
        &__content {
            padding: 3rem 2rem;
        }
    }
}