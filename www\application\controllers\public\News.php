<?php

require_once __DIR__ . "/../Public_base.php";

class News extends Public_base {

	private $categories;

	public function __construct() {
		parent::__construct();
		
		$this->categories = $this->data->nodes->get_list_loc($this->lng->id, array(
			"parent_id" => $this->node()->id,
			"kinds" => node::KIND_CATEGORY,
		));
		$this->view->set("categories", $this->categories);
	}
	public function index($page=1) {
		$this->get_list("", $page);
	}

	public function category($slug, $page=1) {
		$this->get_list($slug, $page);
	}

	private function get_list($slug, $page) {
		$node = $this->node();
		
		$filter = array(
			"kinds" => node::KIND_NEWS,
			"start" => nodeid("news"),
			"min_length" => 2,
			"max_length" => 2,
		);

		if ($slug) {
			$node = __::find($this->categories, function($o) use ($slug) {
				return $o->slug == urldecode($slug);
			});
		}
		
		if (!$node) {
			$this->_404();
			return;
		}

		if ($slug) {
			$filter["parent_id"] = $node->id;
		}

		$featured = $this->data->nodes->get_first_loc($this->lng->id, array_merge($filter, array("is_featured"=>1)), "rand");
		$total = $this->data->nodes->get_count_loc($this->lng->id, $filter);
		
		$count = $this->data->nodes->get_count_loc($this->lng->id, $filter);
		$paging = new Paging($count, 9999, $page, 2);
		
		$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "desc", $paging->offset(), $paging->size());
		$this->set_paging_meta($paging);
		
		$this->view->set("total", $total);
		$this->view->set("count", $count);
		$this->view->set("paging", $paging);
		$this->view->set("list", $list);
		$this->view->set("categories", $this->categories);
		$this->view->set("featured", $featured);

		$this->render_node("public/news-list", $node);
	}

	public function details($slug1, $slug2) {
		$node = $this->node();

		$node = $this->find_node($node, array($slug1, $slug2));
		
		if (!$node) {
			$this->_404();
			return;
		}

		$this->view->set("images", $this->load_assets($node, AssetType::IMAGE));

		$filter = array(
			"start" => $node->top()->id,
			"min_length" => 2,
			"max_length" => 2,
			"not_id" => $node->id,
		);
		$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "desc", 0, 6);
		$this->view->set("list", $list);
		
		$this->render_node("public/news-details", $node);
	}
}
