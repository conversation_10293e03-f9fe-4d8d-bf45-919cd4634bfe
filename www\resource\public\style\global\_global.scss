/**********************************************************
 * Global elements
**********************************************************/

* {
	box-sizing: border-box;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

*:focus {}

::selection {}

html,
body {
	min-height: 100%;
	height: 100%;
}

html {
	font-size: 62.5%;
}

// This way 1rem = 10px

body,
input,
textarea,
select,
button {
	color: $color-black;
	font-family: $font-family;
	font-size: $font-body-sm;
	line-height: 1.3em;

	@include media(xm) {
		font-size: $font-body-md;
		line-height: 1.5em;
	}
}

/* No scrolling when mobile menu is open */
body.-no-scroll {
	overflow: hidden;
}

a {
	color: inherit;
	text-decoration: none;
}

img {
	max-width: 100%;
}

button {
	background: none;
	border: none;
	cursor: pointer;
	padding: 0;
}

button::-moz-focus-inner {
	padding: 0;
	border: 0;
}

sup {
	font-size: 50%;
	vertical-align: super;
}

sub,
small {
	font-size: 75%;
	line-height: 1em;
}

small {
	vertical-align: text-bottom;
}


/**********************************************************
 * Global styles
 **********************************************************/

/* Ordinary link */
.g-link {
	align-items: center;
	display: inline-flex;
	text-decoration: underline;
	text-underline-offset: 2px;

	.icon {
		position: relative;
		top: 1px;

		&:first-child:not(:last-child) {
			margin-right: 1rem;
		}

		&:last-child:not(:first-child) {
			margin-left: 1rem;
		}
	}
}

.g-img {
	display: block;
	width: 100%;
}

.g-bg {
	background-color: white;
}

.g-bg-light-gray {
	background-color: $color-gray-light;
}

.g-gray {
	color: #939393;
}

.g-white {
	color: white;
}

.g-color-primary {
	color: $color-primary;
}

.g-block {
	display: block;
}

.g-overflow {
	overflow: hidden;
}

.g-bold {
	font-weight: bold;
}

.g-left {
	text-align: left;
}

.g-right {
	text-align: right;
}

.g-bottom {
	align-items: flex-end;
}

.g-hr {
	background-color: gray;
	border: none;
	display: block;
	height: 1px;
	margin-bottom: 30px;
	margin-top: 15px;

	@include media(lg) {
		margin-bottom: 40px;
		margin-top: 20px;
	}
}

.g-ontop {
	position: relative;
	z-index: 1;
}

.g-ontoper {
	position: relative;
	z-index: 3 !important;
}

.g-relative {
	position: relative;
}

.g-absolute {
	position: absolute;
}

.g-no-pointer {
	pointer-events: none;
}

.g-transition-xs {
	@include transition-xs;
}

.g-transition-sm {
	@include transition-sm;
}

.g-transition-md {
	@include transition-md;
}

/* Background covering its entire parent */
.g-fullscreen {
	@include fullscreen;
}

/* Margins */
.g-mb-0 {
	margin-bottom: 0 !important;
}

.g-mb-5 {
	margin-bottom: 0.5rem !important;
}

.g-mb-10 {
	margin-bottom: 1rem !important;
}

.g-mb-15 {
	margin-bottom: 1.5rem !important;
}

.g-mb-20 {
	margin-bottom: 2rem !important;
}

.g-mb-30 {
	margin-bottom: 3rem !important;
}

.g-mb-40 {
	margin-bottom: 4rem !important;
}

.g-mt-0 {
	margin-top: 0 !important;
}

.g-mt-5 {
	margin-top: 0.5rem !important;
}

.g-mt-10 {
	margin-top: 1rem !important;
}

.g-mt-15 {
	margin-top: 1.5rem !important;
}

.g-mt-20 {
	margin-top: 2rem !important;
}

.g-mt-30 {
	margin-top: 3rem !important;
}

.g-mt-40 {
	margin-top: 4rem !important;
}

.g-ml-0 {
	margin-left: 0 !important;
}

.g-ml-5 {
	margin-left: 0.5rem !important;
}

.g-ml-10 {
	margin-left: 1rem !important;
}

.g-ml-15 {
	margin-left: 1.5rem !important;
}

.g-ml-20 {
	margin-left: 2rem !important;
}

.g-ml-30 {
	margin-left: 3rem !important;
}

.g-ml-40 {
	margin-left: 4rem !important;
}

.g-mr-0 {
	margin-right: 0 !important;
}

.g-mr-5 {
	margin-right: 0.5rem !important;
}

.g-mr-10 {
	margin-right: 1rem !important;
}

.g-mr-15 {
	margin-right: 1.5rem !important;
}

.g-mr-20 {
	margin-right: 2rem !important;
}

.g-mr-30 {
	margin-right: 3rem !important;
}

.g-mr-40 {
	margin-right: 4rem !important;
}


/* Vertically aligned flex rows from a breakpoint up */

.g-vertical {
	align-items: center;
}

.g-start {
	align-items: flex-start;
}

.g-end {
	align-items: flex-end;
}

@include media(sm) {
	.g-vertical-sm {
		align-items: center;
	}
}

@include media(md) {
	.g-vertical-md {
		align-items: center;
	}
}

@include media(xm) {
	.g-vertical-xm {
		align-items: center;
	}
}

@include media(lg) {
	.g-vertical-lg {
		align-items: center;
	}
}

@include media(xl) {
	.g-vertical-xl {
		align-items: center;
	}
}

.g-vertical-self {
	align-self: center;
}


/* Center from a breakpoint up */

.g-center {
	text-align: center;
}

@include media(sm) {
	.g-center-sm {
		text-align: center;
	}
}

@include media(md) {
	.g-center-md {
		text-align: center;
	}
}

@include media(xm) {
	.g-center-xm {
		text-align: center;
	}
}

@include media(lg) {
	.g-center-lg {
		text-align: center;
	}
}

@include media(xl) {
	.g-center-xl {
		text-align: center;
	}
}
.g-max-1400 {
	max-width: 1400px;
}

.g-max-1200 {
	max-width: 1200px;
}

.g-max-900 {
	max-width: 900px;
}

.g-max-700 {
	max-width: 700px;
}

.g-uppercase {
	text-transform: uppercase;
}

.g-font-title {
	font-family: $font-title;
}

.g-opacity-60 {
	opacity: 0.6;
}

.g-font-15 {
	font-size: 1.5rem;
}

.g-img-spacer {
	display: block;
	width: 100%;
	height: auto;
	min-height: 35rem;
	object-fit: cover;
	object-position: center;
}

.g-radius-3 {
	border-radius: 3px;
}

.g-pin-sign {
	position: fixed;
	width: 50vw;
	height: 50vw;
	opacity: 0.2;

	z-index: -1;

	&--center {
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	&--right {
		top: 50%;
		right: 0;
		transform: translateY(-50%);
	}

	&.switched {
		.g-pin-sign {
			&__logo {
				&.filled {
					opacity: 1;
				}

				&.outline {
					opacity: 0;
				}
			}
		}
	}

	&__logo {
		transition: opacity 0.6s ease;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;

		&.filled {
			opacity: 0;
		}

		&.outline {
			opacity: 1;
		}
	}
}

.g-map {
	display: block;
	width: 100%;
	height: 100%;
	min-height: 35rem;

	&__map {
		display: block;
		width: 100%;
		height: 100%;
	}
}

.g-border-primary {
	border: 1px solid $color-primary;
}