/*!
Pure v1.0.0
Copyright 2013 Yahoo!
Licensed under the BSD License.
https://github.com/yahoo/pure/blob/master/LICENSE.md
*/
/*csslint regex-selectors:false, known-properties:false, duplicate-properties:false*/

.g-row {
    letter-spacing: -0.31em; /* Webkit: collapse white-space between units */
    *letter-spacing: normal; /* reset IE < 8 */
    *word-spacing: -0.43em; /* IE < 8: collapse white-space between units */
    max-width: 100%;
    text-rendering: optimizespeed; /* Webkit: fixes text-rendering: optimizeLegibility */
    width: 100%;

    /* Use flexbox when possible to avoid `letter-spacing` side-effects. */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
        -ms-flex-flow: row wrap;
            flex-flow: row wrap;

    /* Prevents distributing space between rows */
    -webkit-align-content: flex-start;
        -ms-flex-line-pack: start;
            align-content: flex-start;
}

/* IE10 display: -ms-flexbox (and display: flex in IE 11) does not work inside a table; fall back to block and rely on font hack */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
	table .g-row {
		display: block;
	}
}

/* Opera as of 12 on Windows needs word-spacing.
   The ".opera-only" selector is used to prevent actual prefocus styling
   and is not required in markup.
*/
.opera-only :-o-prefocus,
.g-row {
    word-spacing: -0.43em;
}


.col {
    display: inline-block;
    *display: inline; /* IE < 8: fake inline-block */
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto;
}

.col-1,
.col-1-1,
.col-1-2,
.col-1-3,
.col-2-3,
.col-1-4,
.col-3-4,
.col-1-5,
.col-2-5,
.col-3-5,
.col-4-5,
.col-5-5,
.col-1-6,
.col-5-6,
.col-1-8,
.col-3-8,
.col-5-8,
.col-7-8,
.col-1-12,
.col-5-12,
.col-7-12,
.col-11-12,
.col-1-24,
.col-2-24,
.col-3-24,
.col-4-24,
.col-5-24,
.col-6-24,
.col-7-24,
.col-8-24,
.col-9-24,
.col-10-24,
.col-11-24,
.col-12-24,
.col-13-24,
.col-14-24,
.col-15-24,
.col-16-24,
.col-17-24,
.col-18-24,
.col-19-24,
.col-20-24,
.col-21-24,
.col-22-24,
.col-23-24,
.col-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto;
}

.col-1-24 {
    width: 4.1667%;
    *width: 4.1357%;
}

.col-1-12,
.col-2-24 {
    width: 8.3333%;
    *width: 8.3023%;
}

.col-1-8,
.col-3-24 {
    width: 12.5000%;
    *width: 12.4690%;
}

.col-1-6,
.col-4-24 {
    width: 16.6667%;
    *width: 16.6357%;
}

.col-1-5 {
    width: 20%;
    *width: 19.9690%;
}

.col-5-24 {
    width: 20.8333%;
    *width: 20.8023%;
}

.col-1-4,
.col-6-24 {
    width: 25%;
    *width: 24.9690%;
}

.col-7-24 {
    width: 29.1667%;
    *width: 29.1357%;
}

.col-1-3,
.col-8-24 {
    width: 33.3333%;
    *width: 33.3023%;
}

.col-3-8,
.col-9-24 {
    width: 37.5000%;
    *width: 37.4690%;
}

.col-2-5 {
    width: 40%;
    *width: 39.9690%;
}

.col-5-12,
.col-10-24 {
    width: 41.6667%;
    *width: 41.6357%;
}

.col-11-24 {
    width: 45.8333%;
    *width: 45.8023%;
}

.col-1-2,
.col-12-24 {
    width: 50%;
    *width: 49.9690%;
}

.col-13-24 {
    width: 54.1667%;
    *width: 54.1357%;
}

.col-7-12,
.col-14-24 {
    width: 58.3333%;
    *width: 58.3023%;
}

.col-3-5 {
    width: 60%;
    *width: 59.9690%;
}

.col-5-8,
.col-15-24 {
    width: 62.5000%;
    *width: 62.4690%;
}

.col-2-3,
.col-16-24 {
    width: 66.6667%;
    *width: 66.6357%;
}

.col-17-24 {
    width: 70.8333%;
    *width: 70.8023%;
}

.col-3-4,
.col-18-24 {
    width: 75%;
    *width: 74.9690%;
}

.col-19-24 {
    width: 79.1667%;
    *width: 79.1357%;
}

.col-4-5 {
    width: 80%;
    *width: 79.9690%;
}

.col-5-6,
.col-20-24 {
    width: 83.3333%;
    *width: 83.3023%;
}

.col-7-8,
.col-21-24 {
    width: 87.5000%;
    *width: 87.4690%;
}

.col-11-12,
.col-22-24 {
    width: 91.6667%;
    *width: 91.6357%;
}

.col-23-24 {
    width: 95.8333%;
    *width: 95.8023%;
}

.col-1,
.col-1-1,
.col-5-5,
.col-24-24 {
    width: 100%;
}