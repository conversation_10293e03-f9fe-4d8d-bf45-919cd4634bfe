/**********************************************************
 * 
**********************************************************/

$parent: '.consent';

#{$parent} {
	@include transition-sm;
	background-color: white;
	bottom: 0;
	color: black;
	font-size: $font-body-sm;
	left: 0;
	position: fixed;
	text-align: center;
	transform: translateY(100%);
	width: 100%;
	z-index: 5;

	&.-is-shown {
		transform: translateY(0);
	}

	&__inner {
		padding: 1rem 2rem;
	}

	&__text {
		a {
			text-decoration: underline;
		}
	}

	&__btn {
		margin-top: 1rem;
	}


	@include media(md) {
		&__inner {
			align-items: center;
			display: flex;
			justify-content: center;
		}

		&__text {
			margin-right: 3rem;
		}

		&__btn {
			margin-top: 0;
		}
	}
}