/**********************************************************
 * Form elements - input, textarea, label
**********************************************************/

$parent: '.form';

#{$parent} {
	text-align: left;

	&--contact {
		#{$parent}__row {
			margin-bottom: 2rem !important;
			gap: 2rem !important;
		}
		#{$parent}__item {
			width: auto;
			flex: 1 1 40%;
		}
	}

	&--cta {
		#{$parent}__row {
			display: flex;
			align-items: flex-end;
			flex-wrap: wrap;
			gap: 2.2vw;
			margin-bottom: 0;
		}
		#{$parent}__item {
			margin-bottom: 0;
			width: 16vw;
			min-width: 22rem;
		}

		#{$parent}__input {
			min-height: 5.6rem;
			background-color: #FAFAFA99;
			border: 1px solid #A8886199;
		}

		#{$parent}__label {
			font-weight: 300;
		}
	}

	&--vertical {
		#{$parent}__row {
			gap: 1rem;
			width: 100%;
		}
		#{$parent}__item {
			width: 100%;
		}
	}

	&__item {
		line-height: 1em;
		margin-bottom: 2rem;

		&:last-child {
			margin-bottom: 0;
		}
	}

	&__row {
		margin-bottom: 1rem;
	}

	&__label {
		display: block;
		font-weight: bold;
		margin-bottom: 1rem;

		&--spaced {
			margin-bottom: 1.5rem;
		}
	}

	&__input {
		@include transition-xs;
		background: white;
		border: 1px solid gray;
		border-radius: 0.5rem;
		display: block;
		font-size: $font-body-md;
		padding: 0.9rem 1rem;
		width: 100%;
		
		&--lg {
			padding-bottom: 1.5rem;
			padding-top: 1.5rem;
		}

		&--textarea {
			line-height: 1.3em;
			min-height: 15rem;
		}

		&::placeholder {
			color: gray;
		}

		&:focus {
			outline: none;
		}
	}

	&__submit {
		margin-top: 2rem;
	}
	

	@include media(sm) {
		&__submit {
			width: 100%;
		}
	}

	@include media(xm) {
		&__item {
			margin-bottom: 2.5rem;
		}

		&__label {
			margin-bottom: 1rem;

			&--spaced {
				margin-bottom: 2rem;
			}
		}
	}
}


