/* compiled by scssphp v0.8.4 on Fri, 04 Jul 2025 10:52:44 +0000 (0.994s) */

/* Container covering its entire parent */
a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del, details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, hgroup, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  font-size: 100%;
  vertical-align: baseline; }

html {
  line-height: 1; }

ol, ul {
  list-style: none; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

caption, td, th {
  text-align: left;
  font-weight: 400;
  vertical-align: middle; }

blockquote, q {
  quotes: none; }

blockquote::after, blockquote:before, q::after, q:before {
  content: "";
  content: none; }

a img {
  border: none; }

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block; }
/* Slider */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent; }

.slick-list {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0;
  padding: 0; }
  .slick-list:focus {
    outline: none; }
  .slick-list.dragging {
    cursor: pointer;
    cursor: hand; }

.slick-slider .slick-track, .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0); }

.slick-track {
  position: relative;
  left: 0;
  top: 0;
  display: block; }
  .slick-track::before, .slick-track::after {
    content: "";
    display: table; }
  .slick-track::after {
    clear: both; }
  .slick-loading .slick-track {
    visibility: hidden; }

.slick-slide {
  float: left;
  height: 100%;
  min-height: 1px;
  display: none; }
  [dir="rtl"] .slick-slide {
    float: right; }
  .slick-slide img {
    display: block; }
  .slick-slide.slick-loading img {
    display: none; }
  .slick-slide.dragging img {
    pointer-events: none; }
  .slick-initialized .slick-slide {
    display: block; }
  .slick-loading .slick-slide {
    visibility: hidden; }
  .slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent; }

.slick-arrow.slick-hidden {
  display: none; }

.vbox-overlay {
  --vbox-tools-color: #fff;
  --vbox-title-background: #101010;
  --vbox-title-width: 'auto';
  --vbox-title-radius: 0;
  --vbox-share-background: #101010;
  --vbox-share-width: 'auto';
  --vbox-share-radius: 0;
  --vbox-padding: 0; }

.vbox-overlay *, .vbox-overlay :after, .vbox-overlay :before {
  -webkit-backface-visibility: hidden;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.vbox-overlay * {
  -webkit-backface-visibility: visible;
  backface-visibility: visible; }

.vbox-overlay {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  align-items: center;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 999999; }

.vbox-share, .vbox-title {
  line-height: 1;
  position: fixed;
  z-index: 98;
  text-align: center;
  margin: 0 auto;
  color: var(--vbox-tools-color); }

.vbox-title {
  font-size: 12px;
  background-color: var(--vbox-title-background);
  width: var(--vbox-title-width);
  border-radius: var(--vbox-title-radius);
  padding: 12px 54px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block; }

.vbox-share {
  font-size: 24px;
  padding: 0 0.35em;
  background-color: var(--vbox-share-background);
  width: var(--vbox-share-width);
  border-radius: var(--vbox-share-radius); }

.vbox-link-btn, button.vbox-link-btn, button.vbox-link-btn:active, button.vbox-link-btn:focus, button.vbox-link-btn:hover {
  border: none !important;
  background: 0 0 !important;
  box-shadow: none !important;
  color: inherit !important;
  padding: 6px 12px;
  outline: 0;
  display: inline-block;
  cursor: pointer; }

.vbox-share a {
  color: inherit !important;
  padding: 6px 12px;
  display: inline-block; }

.vbox-share svg {
  z-index: 10;
  vertical-align: middle; }

.vbox-close {
  cursor: pointer;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99;
  padding: 6px 15px;
  color: #000;
  color: var(--vbox-tools-color);
  border: 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  opacity: 0.8;
  transition: opacity 0.2s; }

.vbox-close:hover {
  opacity: 1; }

.vbox-left-corner {
  cursor: pointer;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  line-height: 1;
  font-size: 12px;
  z-index: 99;
  display: flex;
  align-items: center;
  color: var(--vbox-tools-color); }

.vbox-num {
  display: inline-block;
  padding: 12px 15px; }

.vbox-left {
  left: 0; }

.vbox-right {
  right: 0; }

.vbox-top {
  top: 0; }

.vbox-bottom {
  bottom: 0; }

.vbox-next, .vbox-prev {
  position: fixed;
  top: 50%;
  margin-top: -15px;
  overflow: hidden;
  cursor: pointer;
  display: block;
  width: 45px;
  height: 45px;
  z-index: 99;
  opacity: 0.8;
  transition: opacity 0.2s; }

.vbox-next:hover, .vbox-prev:hover {
  opacity: 1; }

.vbox-next span, .vbox-prev span {
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top-color: var(--vbox-tools-color);
  border-right-color: var(--vbox-tools-color);
  text-indent: -100px;
  position: absolute;
  top: 8px;
  display: block; }

.vbox-prev {
  left: 15px; }

.vbox-next {
  right: 15px; }

.vbox-prev span {
  left: 10px;
  -ms-transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg); }

.vbox-next span {
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  right: 10px; }

.vbox-inline, .venoratio {
  position: relative;
  width: 100%;
  margin: 0 auto; }

.venoratio::before {
  display: block;
  padding-top: var(--vb-aspect-ratio);
  content: ""; }

.venoratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: var(--vbox-padding); }

.venoratio-1x1 {
  --vb-aspect-ratio: 100%; }

.venoratio-4x3 {
  --vb-aspect-ratio: calc(3 / 4 * 100%); }

.venoratio-16x9 {
  --vb-aspect-ratio: calc(9 / 16 * 100%); }

.venoratio-21x9 {
  --vb-aspect-ratio: calc(9 / 21 * 100%); }

.venoratio-full {
  --vb-aspect-ratio: calc(100vh - 100px); }

.vbox-child.vbox-inline, .vbox-child.venoratio {
  max-width: 100%; }

.vbox-open {
  overflow: hidden; }

.vbox-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: scroll;
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
  z-index: 20;
  max-height: 100%;
  padding: 30px 0; }

.vbox-content {
  opacity: 0; }

.vbox-content {
  text-align: center;
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px 4%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%; }

.vbox-container img {
  max-width: 100%;
  height: auto; }

.vbox-child {
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  max-width: 100%;
  text-align: initial;
  padding: var(--vbox-padding); }

.vbox-child img {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  display: block; }

.vbox-grab .vbox-child img {
  cursor: grab; }

.vbox-child > iframe {
  border: none !important; }

.vbox-content.swipe-left {
  margin-left: -200px !important; }

.vbox-content.swipe-right {
  margin-left: 200px !important; }

.vbox-preloader {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0); }

.vbox-preloader .vbox-preloader-inner {
  opacity: 1;
  transition: opacity 0.2s; }

.vbox-hidden {
  display: none; }

.vbox-preloader.vbox-hidden .vbox-preloader-inner {
  opacity: 0; }

.vbox-backdrop {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: translateZ(-1px);
  -moz-transform: translateZ(-1px);
  -o-transform: translateZ(-1px);
  transform: translateZ(-1px);
  z-index: 0; }

.vbox-tooltip {
  position: relative;
  display: inline-block; }

.vbox-tooltip .vbox-tooltip-text {
  visibility: hidden;
  color: #fff;
  text-align: center;
  padding: 0;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 0;
  opacity: 0;
  transition: opacity 0.3s;
  margin-bottom: 2px;
  font-family: sans-serif; }

.vbox-top .vbox-tooltip .vbox-tooltip-text {
  bottom: auto;
  top: 100%;
  margin-bottom: 0;
  margin-top: 2px; }

.vbox-tooltip-inner {
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 6px;
  font-size: 10px; }

.vbox-tooltip:hover .vbox-tooltip-text {
  visibility: visible;
  opacity: 1; }

.vbox-overlay {
  --sk-size: 40px;
  --sk-color: #333; }

.sk-center {
  margin: auto; }

.sk-plane {
  width: var(--sk-size);
  height: var(--sk-size);
  background-color: var(--sk-color);
  animation: sk-plane 1.2s infinite ease-in-out; }

@keyframes sk-plane {
  0% {
    transform: perspective(120px) rotateX(0) rotateY(0); }

  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0); }

  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg); } }

.sk-chase {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative;
  animation: sk-chase 2.5s infinite linear both; }

.sk-chase-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  animation: sk-chase-dot 2s infinite ease-in-out both; }

.sk-chase-dot:before {
  content: '';
  display: block;
  width: 25%;
  height: 25%;
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-chase-dot-before 2s infinite ease-in-out both; }

.sk-chase-dot:nth-child(1) {
  animation-delay: -1.1s; }

.sk-chase-dot:nth-child(2) {
  animation-delay: -1s; }

.sk-chase-dot:nth-child(3) {
  animation-delay: -0.9s; }

.sk-chase-dot:nth-child(4) {
  animation-delay: -0.8s; }

.sk-chase-dot:nth-child(5) {
  animation-delay: -0.7s; }

.sk-chase-dot:nth-child(6) {
  animation-delay: -0.6s; }

.sk-chase-dot:nth-child(1):before {
  animation-delay: -1.1s; }

.sk-chase-dot:nth-child(2):before {
  animation-delay: -1s; }

.sk-chase-dot:nth-child(3):before {
  animation-delay: -0.9s; }

.sk-chase-dot:nth-child(4):before {
  animation-delay: -0.8s; }

.sk-chase-dot:nth-child(5):before {
  animation-delay: -0.7s; }

.sk-chase-dot:nth-child(6):before {
  animation-delay: -0.6s; }

@keyframes sk-chase {
  100% {
    transform: rotate(360deg); } }

@keyframes sk-chase-dot {
  100%, 80% {
    transform: rotate(360deg); } }

@keyframes sk-chase-dot-before {
  50% {
    transform: scale(0.4); }

  0%, 100% {
    transform: scale(1); } }

.sk-bounce {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative; }

.sk-bounce-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--sk-color);
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: sk-bounce 2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955); }

.sk-bounce-dot:nth-child(2) {
  animation-delay: -1s; }

@keyframes sk-bounce {
  0%, 100% {
    transform: scale(0); }

  45%, 55% {
    transform: scale(1); } }

.sk-wave {
  width: var(--sk-size);
  height: var(--sk-size);
  display: flex;
  justify-content: space-between; }

.sk-wave-rect {
  background-color: var(--sk-color);
  height: 100%;
  width: 15%;
  animation: sk-wave 1.2s infinite ease-in-out; }

.sk-wave-rect:nth-child(1) {
  animation-delay: -1.2s; }

.sk-wave-rect:nth-child(2) {
  animation-delay: -1.1s; }

.sk-wave-rect:nth-child(3) {
  animation-delay: -1s; }

.sk-wave-rect:nth-child(4) {
  animation-delay: -0.9s; }

.sk-wave-rect:nth-child(5) {
  animation-delay: -0.8s; }

@keyframes sk-wave {
  0%, 100%, 40% {
    transform: scaleY(0.4); }

  20% {
    transform: scaleY(1); } }

.sk-pulse {
  width: var(--sk-size);
  height: var(--sk-size);
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-pulse 1.2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955); }

@keyframes sk-pulse {
  0% {
    transform: scale(0); }

  100% {
    transform: scale(1);
    opacity: 0; } }

.sk-flow {
  width: calc(var(--sk-size) * 1.3);
  height: calc(var(--sk-size) * 1.3);
  display: flex;
  justify-content: space-between; }

.sk-flow-dot {
  width: 25%;
  height: 25%;
  background-color: var(--sk-color);
  border-radius: 50%;
  animation: sk-flow 1.4s cubic-bezier(0.455, 0.03, 0.515, 0.955) 0s infinite both; }

.sk-flow-dot:nth-child(1) {
  animation-delay: -0.3s; }

.sk-flow-dot:nth-child(2) {
  animation-delay: -0.15s; }

@keyframes sk-flow {
  0%, 100%, 80% {
    transform: scale(0.3); }

  40% {
    transform: scale(1); } }

.sk-swing {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative;
  animation: sk-swing 1.8s infinite linear; }

.sk-swing-dot {
  width: 45%;
  height: 45%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-swing-dot 2s infinite ease-in-out; }

.sk-swing-dot:nth-child(2) {
  top: auto;
  bottom: 0;
  animation-delay: -1s; }

@keyframes sk-swing {
  100% {
    transform: rotate(360deg); } }

@keyframes sk-swing-dot {
  0%, 100% {
    transform: scale(0.2); }

  50% {
    transform: scale(1); } }

.sk-circle {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative; }

.sk-circle-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }

.sk-circle-dot:before {
  content: '';
  display: block;
  width: 15%;
  height: 15%;
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-circle 1.2s infinite ease-in-out both; }

.sk-circle-dot:nth-child(1) {
  transform: rotate(30deg); }

.sk-circle-dot:nth-child(2) {
  transform: rotate(60deg); }

.sk-circle-dot:nth-child(3) {
  transform: rotate(90deg); }

.sk-circle-dot:nth-child(4) {
  transform: rotate(120deg); }

.sk-circle-dot:nth-child(5) {
  transform: rotate(150deg); }

.sk-circle-dot:nth-child(6) {
  transform: rotate(180deg); }

.sk-circle-dot:nth-child(7) {
  transform: rotate(210deg); }

.sk-circle-dot:nth-child(8) {
  transform: rotate(240deg); }

.sk-circle-dot:nth-child(9) {
  transform: rotate(270deg); }

.sk-circle-dot:nth-child(10) {
  transform: rotate(300deg); }

.sk-circle-dot:nth-child(11) {
  transform: rotate(330deg); }

.sk-circle-dot:nth-child(1):before {
  animation-delay: -1.1s; }

.sk-circle-dot:nth-child(2):before {
  animation-delay: -1s; }

.sk-circle-dot:nth-child(3):before {
  animation-delay: -0.9s; }

.sk-circle-dot:nth-child(4):before {
  animation-delay: -0.8s; }

.sk-circle-dot:nth-child(5):before {
  animation-delay: -0.7s; }

.sk-circle-dot:nth-child(6):before {
  animation-delay: -0.6s; }

.sk-circle-dot:nth-child(7):before {
  animation-delay: -0.5s; }

.sk-circle-dot:nth-child(8):before {
  animation-delay: -0.4s; }

.sk-circle-dot:nth-child(9):before {
  animation-delay: -0.3s; }

.sk-circle-dot:nth-child(10):before {
  animation-delay: -0.2s; }

.sk-circle-dot:nth-child(11):before {
  animation-delay: -0.1s; }

@keyframes sk-circle {
  0%, 100%, 80% {
    transform: scale(0); }

  40% {
    transform: scale(1); } }

.sk-circle-fade {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative; }

.sk-circle-fade-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }

.sk-circle-fade-dot:before {
  content: '';
  display: block;
  width: 15%;
  height: 15%;
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-circle-fade 1.2s infinite ease-in-out both; }

.sk-circle-fade-dot:nth-child(1) {
  transform: rotate(30deg); }

.sk-circle-fade-dot:nth-child(2) {
  transform: rotate(60deg); }

.sk-circle-fade-dot:nth-child(3) {
  transform: rotate(90deg); }

.sk-circle-fade-dot:nth-child(4) {
  transform: rotate(120deg); }

.sk-circle-fade-dot:nth-child(5) {
  transform: rotate(150deg); }

.sk-circle-fade-dot:nth-child(6) {
  transform: rotate(180deg); }

.sk-circle-fade-dot:nth-child(7) {
  transform: rotate(210deg); }

.sk-circle-fade-dot:nth-child(8) {
  transform: rotate(240deg); }

.sk-circle-fade-dot:nth-child(9) {
  transform: rotate(270deg); }

.sk-circle-fade-dot:nth-child(10) {
  transform: rotate(300deg); }

.sk-circle-fade-dot:nth-child(11) {
  transform: rotate(330deg); }

.sk-circle-fade-dot:nth-child(1):before {
  animation-delay: -1.1s; }

.sk-circle-fade-dot:nth-child(2):before {
  animation-delay: -1s; }

.sk-circle-fade-dot:nth-child(3):before {
  animation-delay: -0.9s; }

.sk-circle-fade-dot:nth-child(4):before {
  animation-delay: -0.8s; }

.sk-circle-fade-dot:nth-child(5):before {
  animation-delay: -0.7s; }

.sk-circle-fade-dot:nth-child(6):before {
  animation-delay: -0.6s; }

.sk-circle-fade-dot:nth-child(7):before {
  animation-delay: -0.5s; }

.sk-circle-fade-dot:nth-child(8):before {
  animation-delay: -0.4s; }

.sk-circle-fade-dot:nth-child(9):before {
  animation-delay: -0.3s; }

.sk-circle-fade-dot:nth-child(10):before {
  animation-delay: -0.2s; }

.sk-circle-fade-dot:nth-child(11):before {
  animation-delay: -0.1s; }

@keyframes sk-circle-fade {
  0%, 100%, 39% {
    opacity: 0;
    transform: scale(0.6); }

  40% {
    opacity: 1;
    transform: scale(1); } }

.sk-grid {
  width: var(--sk-size);
  height: var(--sk-size); }

.sk-grid-cube {
  width: 33.33%;
  height: 33.33%;
  background-color: var(--sk-color);
  float: left;
  animation: sk-grid 1.3s infinite ease-in-out; }

.sk-grid-cube:nth-child(1) {
  animation-delay: 0.2s; }

.sk-grid-cube:nth-child(2) {
  animation-delay: 0.3s; }

.sk-grid-cube:nth-child(3) {
  animation-delay: 0.4s; }

.sk-grid-cube:nth-child(4) {
  animation-delay: 0.1s; }

.sk-grid-cube:nth-child(5) {
  animation-delay: 0.2s; }

.sk-grid-cube:nth-child(6) {
  animation-delay: 0.3s; }

.sk-grid-cube:nth-child(7) {
  animation-delay: 0s; }

.sk-grid-cube:nth-child(8) {
  animation-delay: 0.1s; }

.sk-grid-cube:nth-child(9) {
  animation-delay: 0.2s; }

@keyframes sk-grid {
  0%, 100%, 70% {
    transform: scale3D(1, 1, 1); }

  35% {
    transform: scale3D(0, 0, 1); } }

.sk-fold {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative;
  transform: rotateZ(45deg); }

.sk-fold-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  transform: scale(1.1); }

.sk-fold-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--sk-color);
  animation: sk-fold 2.4s infinite linear both;
  transform-origin: 100% 100%; }

.sk-fold-cube:nth-child(2) {
  transform: scale(1.1) rotateZ(90deg); }

.sk-fold-cube:nth-child(4) {
  transform: scale(1.1) rotateZ(180deg); }

.sk-fold-cube:nth-child(3) {
  transform: scale(1.1) rotateZ(270deg); }

.sk-fold-cube:nth-child(2):before {
  animation-delay: 0.3s; }

.sk-fold-cube:nth-child(4):before {
  animation-delay: 0.6s; }

.sk-fold-cube:nth-child(3):before {
  animation-delay: 0.9s; }

@keyframes sk-fold {
  0%, 10% {
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0; }

  25%, 75% {
    transform: perspective(140px) rotateX(0);
    opacity: 1; }

  100%, 90% {
    transform: perspective(140px) rotateY(180deg);
    opacity: 0; } }

.sk-wander {
  width: var(--sk-size);
  height: var(--sk-size);
  position: relative; }

.sk-wander-cube {
  background-color: var(--sk-color);
  width: 20%;
  height: 20%;
  position: absolute;
  top: 0;
  left: 0;
  --sk-wander-distance: calc(var(--sk-size) * 0.75);
  animation: sk-wander 2s ease-in-out -2s infinite both; }

.sk-wander-cube:nth-child(2) {
  animation-delay: -0.5s; }

.sk-wander-cube:nth-child(3) {
  animation-delay: -1s; }

@keyframes sk-wander {
  0% {
    transform: rotate(0); }

  25% {
    transform: translateX(var(--sk-wander-distance)) rotate(-90deg) scale(0.6); }

  50% {
    transform: translateX(var(--sk-wander-distance)) translateY(var(--sk-wander-distance)) rotate(-179deg); }

  50.1% {
    transform: translateX(var(--sk-wander-distance)) translateY(var(--sk-wander-distance)) rotate(-180deg); }

  75% {
    transform: translateX(0) translateY(var(--sk-wander-distance)) rotate(-270deg) scale(0.6); }

  100% {
    transform: rotate(-360deg); } }

.irs {
  position: relative;
  display: block;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 12px;
  font-family: Arial, sans-serif; }

.irs-line {
  position: relative;
  display: block;
  overflow: hidden;
  outline: none !important; }

.irs-bar {
  position: absolute;
  display: block;
  left: 0;
  width: 0; }

.irs-shadow {
  position: absolute;
  display: none;
  left: 0;
  width: 0; }

.irs-handle {
  position: absolute;
  display: block;
  box-sizing: border-box;
  cursor: default;
  z-index: 1; }

.irs-handle.type_last {
  z-index: 2; }

.irs-min, .irs-max {
  position: absolute;
  display: block;
  cursor: default; }

.irs-min {
  left: 0; }

.irs-max {
  right: 0; }

.irs-from, .irs-to, .irs-single {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  cursor: default;
  white-space: nowrap; }

.irs-grid {
  position: absolute;
  display: none;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px; }

.irs-with-grid .irs-grid {
  display: block; }

.irs-grid-pol {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 8px;
  background: #000; }

.irs-grid-pol.small {
  height: 4px; }

.irs-grid-text {
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  text-align: center;
  font-size: 9px;
  line-height: 9px;
  padding: 0 3px;
  color: #000; }

.irs-disable-mask {
  position: absolute;
  display: block;
  top: 0;
  left: -1%;
  width: 102%;
  height: 100%;
  cursor: default;
  background: rgba(0, 0, 0, 0);
  z-index: 2; }

.lt-ie9 .irs-disable-mask {
  background: #000;
  filter: alpha(opacity=0);
  cursor: not-allowed; }

.irs-disabled {
  opacity: 0.4; }

.irs-hidden-input {
  position: absolute !important;
  display: block !important;
  top: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 0 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden;
  outline: none !important;
  z-index: -9999 !important;
  background: none !important;
  border-style: solid !important;
  border-color: transparent !important; }

.irs--flat {
  height: 40px; }

.irs--flat.irs-with-grid {
  height: 60px; }

.irs--flat .irs-line {
  top: 25px;
  height: 12px;
  background-color: #e1e4e9;
  border-radius: 4px; }

.irs--flat .irs-bar {
  top: 25px;
  height: 12px;
  background-color: #ed5565; }

.irs--flat .irs-bar--single {
  border-radius: 4px 0 0 4px; }

.irs--flat .irs-shadow {
  height: 1px;
  bottom: 16px;
  background-color: #e1e4e9; }

.irs--flat .irs-handle {
  top: 22px;
  width: 16px;
  height: 18px;
  background-color: transparent; }

.irs--flat .irs-handle > i:first-child {
  position: absolute;
  display: block;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  margin-left: -1px;
  background-color: #da4453; }

.irs--flat .irs-handle.state_hover > i:first-child, .irs--flat .irs-handle:hover > i:first-child {
  background-color: #a43540; }

.irs--flat .irs-min, .irs--flat .irs-max {
  top: 0;
  padding: 1px 3px;
  color: #999;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  background-color: #e1e4e9;
  border-radius: 4px; }

.irs--flat .irs-from, .irs--flat .irs-to, .irs--flat .irs-single {
  color: white;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background-color: #ed5565;
  border-radius: 4px; }

.irs--flat .irs-from:before, .irs--flat .irs-to:before, .irs--flat .irs-single:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #ed5565; }

.irs--flat .irs-grid-pol {
  background-color: #e1e4e9; }

.irs--flat .irs-grid-text {
  color: #999; }

.irs--big {
  height: 55px; }

.irs--big.irs-with-grid {
  height: 70px; }

.irs--big .irs-line {
  top: 33px;
  height: 12px;
  background-color: white;
  background: linear-gradient(to bottom, #ddd -50%, white 150%);
  border: 1px solid #ccc;
  border-radius: 12px; }

.irs--big .irs-bar {
  top: 33px;
  height: 12px;
  background-color: #92bce0;
  border: 1px solid #428bca;
  background: linear-gradient(to bottom, #fff 0%, #428bca 30%, #b9d4ec 100%);
  box-shadow: inset 0 0 1px 1px rgba(255, 255, 255, 0.5); }

.irs--big .irs-bar--single {
  border-radius: 12px 0 0 12px; }

.irs--big .irs-shadow {
  height: 1px;
  bottom: 16px;
  background-color: rgba(66, 139, 202, 0.5); }

.irs--big .irs-handle {
  top: 25px;
  width: 30px;
  height: 30px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  background-color: #cbcfd5;
  background: linear-gradient(to bottom, white 0%, #b4b9be 30%, white 100%);
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2), inset 0 0 3px 1px white;
  border-radius: 30px; }

.irs--big .irs-handle.state_hover, .irs--big .irs-handle:hover {
  border-color: rgba(0, 0, 0, 0.45);
  background-color: #939ba7;
  background: linear-gradient(to bottom, white 0%, #919ba5 30%, white 100%); }

.irs--big .irs-min, .irs--big .irs-max {
  top: 0;
  padding: 1px 5px;
  color: white;
  text-shadow: none;
  background-color: #9f9f9f;
  border-radius: 3px; }

.irs--big .irs-from, .irs--big .irs-to, .irs--big .irs-single {
  color: white;
  text-shadow: none;
  padding: 1px 5px;
  background-color: #428bca;
  background: linear-gradient(to bottom, #428bca 0%, #3071a9 100%);
  border-radius: 3px; }

.irs--big .irs-grid-pol {
  background-color: #428bca; }

.irs--big .irs-grid-text {
  color: #428bca; }

.irs--modern {
  height: 55px; }

.irs--modern.irs-with-grid {
  height: 55px; }

.irs--modern .irs-line {
  top: 25px;
  height: 5px;
  background-color: #d1d6e0;
  background: linear-gradient(to bottom, #e0e4ea 0%, #d1d6e0 100%);
  border: 1px solid #a3adc1;
  border-bottom-width: 0;
  border-radius: 5px; }

.irs--modern .irs-bar {
  top: 25px;
  height: 5px;
  background: #20b426;
  background: linear-gradient(to bottom, #20b426 0%, #18891d 100%); }

.irs--modern .irs-bar--single {
  border-radius: 5px 0 0 5px; }

.irs--modern .irs-shadow {
  height: 1px;
  bottom: 21px;
  background-color: rgba(209, 214, 224, 0.5); }

.irs--modern .irs-handle {
  top: 37px;
  width: 12px;
  height: 13px;
  border: 1px solid #a3adc1;
  border-top-width: 0;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 3px 3px; }

.irs--modern .irs-handle > i:nth-child(1) {
  position: absolute;
  display: block;
  top: -4px;
  left: 1px;
  width: 6px;
  height: 6px;
  border: 1px solid #a3adc1;
  background: white;
  transform: rotate(45deg); }

.irs--modern .irs-handle > i:nth-child(2) {
  position: absolute;
  display: block;
  box-sizing: border-box;
  top: 0;
  left: 0;
  width: 10px;
  height: 12px;
  background: #e9e6e6;
  background: linear-gradient(to bottom, white 0%, #e9e6e6 100%);
  border-radius: 0 0 3px 3px; }

.irs--modern .irs-handle > i:nth-child(3) {
  position: absolute;
  display: block;
  box-sizing: border-box;
  top: 3px;
  left: 3px;
  width: 4px;
  height: 5px;
  border-left: 1px solid #a3adc1;
  border-right: 1px solid #a3adc1; }

.irs--modern .irs-handle.state_hover, .irs--modern .irs-handle:hover {
  border-color: #7685a2;
  background: #c3c7cd;
  background: linear-gradient(to bottom, #fff 0%, #919ba5 30%, #fff 100%); }

.irs--modern .irs-handle.state_hover > i:nth-child(1), .irs--modern .irs-handle:hover > i:nth-child(1) {
  border-color: #7685a2; }

.irs--modern .irs-handle.state_hover > i:nth-child(3), .irs--modern .irs-handle:hover > i:nth-child(3) {
  border-color: #48536a; }

.irs--modern .irs-min, .irs--modern .irs-max {
  top: 0;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  color: white;
  background-color: #d1d6e0;
  border-radius: 5px; }

.irs--modern .irs-from, .irs--modern .irs-to, .irs--modern .irs-single {
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background-color: #20b426;
  color: white;
  border-radius: 5px; }

.irs--modern .irs-from:before, .irs--modern .irs-to:before, .irs--modern .irs-single:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #20b426; }

.irs--modern .irs-grid {
  height: 25px; }

.irs--modern .irs-grid-pol {
  background-color: #dedede; }

.irs--modern .irs-grid-text {
  color: silver;
  font-size: 13px; }

.irs--sharp {
  height: 50px;
  font-size: 12px;
  line-height: 1; }

.irs--sharp.irs-with-grid {
  height: 57px; }

.irs--sharp .irs-line {
  top: 30px;
  height: 2px;
  background-color: black;
  border-radius: 2px; }

.irs--sharp .irs-bar {
  top: 30px;
  height: 2px;
  background-color: #ee22fa; }

.irs--sharp .irs-bar--single {
  border-radius: 2px 0 0 2px; }

.irs--sharp .irs-shadow {
  height: 1px;
  bottom: 21px;
  background-color: rgba(0, 0, 0, 0.5); }

.irs--sharp .irs-handle {
  top: 25px;
  width: 10px;
  height: 10px;
  background-color: #a804b2; }

.irs--sharp .irs-handle > i:first-child {
  position: absolute;
  display: block;
  top: 100%;
  left: 0;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-top-color: #a804b2; }

.irs--sharp .irs-handle.state_hover, .irs--sharp .irs-handle:hover {
  background-color: black; }

.irs--sharp .irs-handle.state_hover > i:first-child, .irs--sharp .irs-handle:hover > i:first-child {
  border-top-color: black; }

.irs--sharp .irs-min, .irs--sharp .irs-max {
  color: white;
  font-size: 14px;
  line-height: 1;
  top: 0;
  padding: 3px 4px;
  opacity: 0.4;
  background-color: #a804b2;
  border-radius: 2px; }

.irs--sharp .irs-from, .irs--sharp .irs-to, .irs--sharp .irs-single {
  font-size: 14px;
  line-height: 1;
  text-shadow: none;
  padding: 3px 4px;
  background-color: #a804b2;
  color: white;
  border-radius: 2px; }

.irs--sharp .irs-from:before, .irs--sharp .irs-to:before, .irs--sharp .irs-single:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #a804b2; }

.irs--sharp .irs-grid {
  height: 25px; }

.irs--sharp .irs-grid-pol {
  background-color: #dedede; }

.irs--sharp .irs-grid-text {
  color: silver;
  font-size: 13px; }

.irs--round {
  height: 50px; }

.irs--round.irs-with-grid {
  height: 65px; }

.irs--round .irs-line {
  top: 36px;
  height: 4px;
  background-color: #dee4ec;
  border-radius: 4px; }

.irs--round .irs-bar {
  top: 36px;
  height: 4px;
  background-color: #006cfa; }

.irs--round .irs-bar--single {
  border-radius: 4px 0 0 4px; }

.irs--round .irs-shadow {
  height: 4px;
  bottom: 21px;
  background-color: rgba(222, 228, 236, 0.5); }

.irs--round .irs-handle {
  top: 26px;
  width: 24px;
  height: 24px;
  border: 4px solid #006cfa;
  background-color: white;
  border-radius: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 255, 0.3); }

.irs--round .irs-handle.state_hover, .irs--round .irs-handle:hover {
  background-color: #f0f6ff; }

.irs--round .irs-min, .irs--round .irs-max {
  color: #333;
  font-size: 14px;
  line-height: 1;
  top: 0;
  padding: 3px 5px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px; }

.irs--round .irs-from, .irs--round .irs-to, .irs--round .irs-single {
  font-size: 14px;
  line-height: 1;
  text-shadow: none;
  padding: 3px 5px;
  background-color: #006cfa;
  color: white;
  border-radius: 4px; }

.irs--round .irs-from:before, .irs--round .irs-to:before, .irs--round .irs-single:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #006cfa; }

.irs--round .irs-grid {
  height: 25px; }

.irs--round .irs-grid-pol {
  background-color: #dedede; }

.irs--round .irs-grid-text {
  color: silver;
  font-size: 13px; }

.irs--square {
  height: 50px; }

.irs--square.irs-with-grid {
  height: 60px; }

.irs--square .irs-line {
  top: 31px;
  height: 4px;
  background-color: #dedede; }

.irs--square .irs-bar {
  top: 31px;
  height: 4px;
  background-color: black; }

.irs--square .irs-shadow {
  height: 2px;
  bottom: 21px;
  background-color: #dedede; }

.irs--square .irs-handle {
  top: 25px;
  width: 16px;
  height: 16px;
  border: 3px solid black;
  background-color: white;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg); }

.irs--square .irs-handle.state_hover, .irs--square .irs-handle:hover {
  background-color: #f0f6ff; }

.irs--square .irs-min, .irs--square .irs-max {
  color: #333;
  font-size: 14px;
  line-height: 1;
  top: 0;
  padding: 3px 5px;
  background-color: rgba(0, 0, 0, 0.1); }

.irs--square .irs-from, .irs--square .irs-to, .irs--square .irs-single {
  font-size: 14px;
  line-height: 1;
  text-shadow: none;
  padding: 3px 5px;
  background-color: black;
  color: white; }

.irs--square .irs-grid {
  height: 25px; }

.irs--square .irs-grid-pol {
  background-color: #dedede; }

.irs--square .irs-grid-text {
  color: silver;
  font-size: 11px; }
/**********************************************************
 * Overwrite default styles for plugins
 **********************************************************/
.steps .slick-arrow.slick-prev {
  left: -1rem; }
  .steps .slick-arrow.slick-next {
    right: -1rem; }

.slick-arrow {
  transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  align-items: center;
  border-radius: 50%;
  display: inline-flex;
  height: 60px;
  justify-content: center;
  width: 60px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: white;
  font-size: 0;
  opacity: 1;
  position: absolute;
  top: 50%;
  transform: translateY(-50%) scale(0.75);
  visibility: visible;
  z-index: 2; }
  .slick-arrow.slick-prev {
    background-image: url("../image/icon/arrow-left.svg");
    height: 60px;
    width: 60px;
    left: 1rem; }
  .slick-arrow.slick-next {
    background-image: url("../image/icon/arrow-right.svg");
    height: 60px;
    width: 60px;
    right: 1rem; }
  .slick-arrow.slick-disabled {
    opacity: 0;
    visibility: hidden; }
  .slick-arrow.slick-hidden {
    display: none !important; }
  @media only screen and (min-width: 1281px) {
  .slick-arrow {
    transform: translateY(-50%); } }

.slick-dots {
  display: flex;
  justify-content: center;
  margin: 2rem 0; }
  .slick-dots li {
    line-height: 1px;
    margin: 0 0.5rem; }
  .slick-dots button {
    transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
    transition-timing-function: ease-in-out;
    background-color: transparent;
    border: 1px solid black;
    border-radius: 0.5rem;
    font-size: 0;
    height: 1rem;
    width: 1rem; }
  .slick-dots button:focus {
    outline: none; }
  .slick-dots .slick-active button {
    background-color: black; }
/* Venobox */
.vbox-close {
  display: none; }
/* youtube modal */
.yu2fvl {
  z-index: 9999;
  top: 0; }
  .yu2fvl-iframe {
    display: block;
    height: 100%;
    width: 100%;
    border: 0; }
  .yu2fvl-overlay {
    z-index: 9998;
    background: rgba(0, 0, 0, 0.7); }

.yu2fvl-close {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  border: none;
  border-radius: 2rem;
  background: white url(../image/icon/close.svg) no-repeat center !important;
  box-shadow: 0 1rem 1rem 0 rgba(0, 0, 0, 0.15);
  cursor: pointer;
  height: 4rem;
  padding: 0;
  position: absolute;
  right: 1rem !important;
  text-indent: -999rem;
  top: -2rem !important;
  width: 4rem !important; }
  .yu2fvl-close:focus {
    outline: none;
    top: -1.8rem !important; }
  @media only screen and (min-width: 641px) {
  .yu2fvl-close {
    right: -2rem !important; } }
/**********************************************************
 * Fade-in animations
 **********************************************************/
.word {
  display: inline-block;
  white-space: nowrap; }

.letter {
  display: inline-block;
  opacity: 0; }

.js-anim, .js-anim-rev, .js-stagger-item {
  opacity: 0; }
/**********************************************************
 * Dealing with display:none transition nightmares
 **********************************************************/
@keyframes display {
  0% {
    display: none;
    opacity: 0; }

  1% {
    display: block;
    opacity: 0; }

  100% {
    display: block;
    opacity: 1; } }

@keyframes display-flex {
  0% {
    display: none;
    opacity: 0; }

  1% {
    display: flex;
    opacity: 0; }

  100% {
    display: flex;
    opacity: 1; } }
/**********************************************************
 * Force an element to contain its children
 **********************************************************/
.g-clearfix::after, .g-grid::after {
  clear: both;
  content: " ";
  display: block;
  font-size: 0;
  height: 0;
  visibility: hidden; }

* html .g-clearfix, * html .g-grid {
  zoom: 1; }
/* IE6 */
*:first-child + html .g-clearfix, *:first-child + html .g-grid {
  zoom: 1; }
/* IE7 */
/**********************************************************
 * Global elements
 **********************************************************/
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

html, body {
  min-height: 100%;
  height: 100%; }

html {
  font-size: 62.5%; }

body, input, textarea, select, button {
  color: #071b34;
  font-family: "Montserrat", Arial, sans-serif;
  font-size: 1.4rem;
  line-height: 1.3em; }
  @media only screen and (min-width: 1025px) {
  body, input, textarea, select, button {
    font-size: 1.6rem;
    line-height: 1.5em; } }
/* No scrolling when mobile menu is open */
body.-no-scroll {
  overflow: hidden; }

a {
  color: inherit;
  text-decoration: none; }

img {
  max-width: 100%; }

button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0; }

button::-moz-focus-inner {
  padding: 0;
  border: 0; }

sup {
  font-size: 50%;
  vertical-align: super; }

sub, small {
  font-size: 75%;
  line-height: 1em; }

small {
  vertical-align: text-bottom; }
/**********************************************************
 * Global styles
 **********************************************************/
/* Ordinary link */
.g-link {
  align-items: center;
  display: inline-flex;
  text-decoration: underline;
  text-underline-offset: 2px; }
  .g-link .icon {
    position: relative;
    top: 1px; }
  .g-link .icon:first-child:not(:last-child) {
    margin-right: 1rem; }
  .g-link .icon:last-child:not(:first-child) {
    margin-left: 1rem; }

.g-img {
  display: block;
  width: 100%; }

.g-bg {
  background-color: white; }

.g-bg-light-gray {
  background-color: #f7f7f7; }

.g-gray {
  color: #939393; }

.g-white {
  color: white; }

.g-color-primary {
  color: #a88861; }

.g-block {
  display: block; }

.g-overflow {
  overflow: hidden; }

.g-bold {
  font-weight: bold; }

.g-left {
  text-align: left; }

.g-right {
  text-align: right; }

.g-bottom {
  align-items: flex-end; }

.g-hr {
  background-color: gray;
  border: none;
  display: block;
  height: 1px;
  margin-bottom: 30px;
  margin-top: 15px; }
  @media only screen and (min-width: 1281px) {
  .g-hr {
    margin-bottom: 40px;
    margin-top: 20px; } }

.g-ontop {
  position: relative;
  z-index: 1; }

.g-ontoper {
  position: relative;
  z-index: 3 !important; }

.g-relative {
  position: relative; }

.g-absolute {
  position: absolute; }

.g-no-pointer {
  pointer-events: none; }

.g-transition-xs {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out; }

.g-transition-sm {
  transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out; }

.g-transition-md {
  transition: all 500ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out; }
/* Background covering its entire parent */
.g-fullscreen {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%; }
/* Margins */
.g-mb-0 {
  margin-bottom: 0 !important; }

.g-mb-5 {
  margin-bottom: 0.5rem !important; }

.g-mb-10 {
  margin-bottom: 1rem !important; }

.g-mb-15 {
  margin-bottom: 1.5rem !important; }

.g-mb-20 {
  margin-bottom: 2rem !important; }

.g-mb-30 {
  margin-bottom: 3rem !important; }

.g-mb-40 {
  margin-bottom: 4rem !important; }

.g-mt-0 {
  margin-top: 0 !important; }

.g-mt-5 {
  margin-top: 0.5rem !important; }

.g-mt-10 {
  margin-top: 1rem !important; }

.g-mt-15 {
  margin-top: 1.5rem !important; }

.g-mt-20 {
  margin-top: 2rem !important; }

.g-mt-30 {
  margin-top: 3rem !important; }

.g-mt-40 {
  margin-top: 4rem !important; }

.g-ml-0 {
  margin-left: 0 !important; }

.g-ml-5 {
  margin-left: 0.5rem !important; }

.g-ml-10 {
  margin-left: 1rem !important; }

.g-ml-15 {
  margin-left: 1.5rem !important; }

.g-ml-20 {
  margin-left: 2rem !important; }

.g-ml-30 {
  margin-left: 3rem !important; }

.g-ml-40 {
  margin-left: 4rem !important; }

.g-mr-0 {
  margin-right: 0 !important; }

.g-mr-5 {
  margin-right: 0.5rem !important; }

.g-mr-10 {
  margin-right: 1rem !important; }

.g-mr-15 {
  margin-right: 1.5rem !important; }

.g-mr-20 {
  margin-right: 2rem !important; }

.g-mr-30 {
  margin-right: 3rem !important; }

.g-mr-40 {
  margin-right: 4rem !important; }
/* Vertically aligned flex rows from a breakpoint up */
.g-vertical {
  align-items: center; }

.g-start {
  align-items: flex-start; }

.g-end {
  align-items: flex-end; }

@media only screen and (min-width: 481px) {
  .g-vertical-sm {
    align-items: center; } }

@media only screen and (min-width: 641px) {
  .g-vertical-md {
    align-items: center; } }

@media only screen and (min-width: 1025px) {
  .g-vertical-xm {
    align-items: center; } }

@media only screen and (min-width: 1281px) {
  .g-vertical-lg {
    align-items: center; } }

@media only screen and (min-width: 1601px) {
  .g-vertical-xl {
    align-items: center; } }

.g-vertical-self {
  align-self: center; }
/* Center from a breakpoint up */
.g-center {
  text-align: center; }

@media only screen and (min-width: 481px) {
  .g-center-sm {
    text-align: center; } }

@media only screen and (min-width: 641px) {
  .g-center-md {
    text-align: center; } }

@media only screen and (min-width: 1025px) {
  .g-center-xm {
    text-align: center; } }

@media only screen and (min-width: 1281px) {
  .g-center-lg {
    text-align: center; } }

@media only screen and (min-width: 1601px) {
  .g-center-xl {
    text-align: center; } }

.g-max-1400 {
  max-width: 1400px; }

.g-max-1200 {
  max-width: 1200px; }

.g-max-900 {
  max-width: 900px; }

.g-max-700 {
  max-width: 700px; }

.g-uppercase {
  text-transform: uppercase; }

.g-font-title {
  font-family: "Prosto One", Arial, sans-serif; }

.g-opacity-60 {
  opacity: 0.6; }

.g-font-15 {
  font-size: 1.5rem; }

.g-img-spacer {
  display: block;
  width: 100%;
  height: auto;
  min-height: 35rem;
  object-fit: cover;
  object-position: center; }

.g-radius-3 {
  border-radius: 3px; }

.g-pin-sign {
  position: fixed;
  width: 50vw;
  height: 50vw;
  opacity: 0.2;
  z-index: -1; }
  .g-pin-sign--center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); }
  .g-pin-sign--right {
    top: 50%;
    right: 0;
    transform: translateY(-50%); }
  .g-pin-sign.switched .g-pin-sign__logo.filled {
    opacity: 1; }
    .g-pin-sign.switched .g-pin-sign__logo.outline {
      opacity: 0; }
  .g-pin-sign__logo {
    transition: opacity 0.6s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }
  .g-pin-sign__logo.filled {
    opacity: 0; }
  .g-pin-sign__logo.outline {
    opacity: 1; }

.g-map {
  display: block;
  width: 100%;
  height: 100%;
  min-height: 35rem; }
  .g-map__map {
    display: block;
    width: 100%;
    height: 100%; }

.g-border-primary {
  border: 1px solid #a88861; }
/**********************************************************
 * CMS-entered text (from WYSIWYG editor)
 **********************************************************/
.g-editor {
  font-size: clamp(1.4rem, 2vw, 1.6rem);
  /* Headings */
  /* Element styles */
  /* Image with caption */ }
  .g-editor--sm {
    font-size: 1.4rem; }
  .g-editor--md {
    font-size: clamp(1.6rem, 2vw, 1.8rem); }
  .g-editor--lg {
    font-size: clamp(1.6rem, 2vw, 2rem); }
  .g-editor h1, .g-editor h2, .g-editor h3, .g-editor h4, .g-editor h5, .g-editor h6 {
    line-height: 1.3em;
    margin-bottom: 2rem;
    margin-top: 4rem; }
  .g-editor h1:first-child, .g-editor h2:first-child, .g-editor h3:first-child, .g-editor h4:first-child, .g-editor h5:first-child, .g-editor h6:first-child {
    margin-top: 0; }
  .g-editor h2 {
    font-size: clamp(3rem, 4vw, 3.8rem); }
  .g-editor h3, .g-editor h4 {
    font-size: clamp(1.8rem, 3vw, 3rem); }
  .g-editor h5, .g-editor h6 {
    font-size: clamp(1.8rem, 2vw, 2.4rem); }
  .g-editor a {
    transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
    transition-timing-function: ease-in-out;
    text-decoration: underline;
    text-decoration-color: inherit;
    text-underline-offset: 2px; }
  .g-editor p {
    margin-bottom: 1rem;
    /* No spacing for last paragraph */ }
  .g-editor p:last-child, .g-editor p:first-child:last-child {
    margin-bottom: 0; }
  .g-editor strong {
    font-weight: bold; }
  .g-editor em {
    font-style: italic; }
  .g-editor ul {
    list-style: disc inside;
    margin: 2rem 0; }
  .g-editor ul li {
    margin-bottom: 1.2rem; }
    .g-editor ul li:last-child {
      margin-bottom: 0; }
  .g-editor ol {
    list-style: decimal inside;
    margin: 2rem 0; }
  .g-editor ol li {
    line-height: 1.4em;
    margin-bottom: 1.2rem;
    position: relative; }
  .g-editor img {
    display: block;
    margin-bottom: 2rem;
    margin-top: 1rem; }
  .g-editor figure {
    margin: 2rem 0; }
  .g-editor figure img {
    display: block;
    margin-bottom: 0;
    width: 100%; }
  .g-editor figure img + figcaption {
    color: black;
    font-size: 1.4rem;
    line-height: 1.2em;
    margin-bottom: 3rem;
    padding: 1rem 0; }
  .g-editor table {
    border-spacing: 1px;
    border-collapse: separate;
    font-size: 1.4rem;
    table-layout: fixed; }
  .g-editor th {
    background-color: black;
    border-right: 1px solid transparent;
    color: white;
    font-size: 1.4rem;
    font-weight: bold;
    padding: 1rem;
    text-align: center; }
  .g-editor td {
    border-bottom: 1px solid #ddd;
    min-width: 20rem;
    padding: 1.5rem 1rem;
    vertical-align: top; }
  .g-editor td ul, .g-editor td ol {
    margin-top: 0;
    margin-left: 0; }
  .g-editor tr:last-child td {
    border-bottom: none; }
  @media only screen and (min-width: 1025px) {
  .g-editor {
    /* Image with caption */ }
    .g-editor p {
      margin-bottom: 2rem; }

    .g-editor figure {
      margin: 3rem 0; }

    .g-editor img {
      margin-bottom: 3rem;
      margin-top: 3rem; } }
  @media only screen and (min-width: 1281px) {
  .g-editor--lg p {
    margin-bottom: 3rem; }
    .g-editor ul, .g-editor ol {
      margin: 4rem 0; } }
  @media only screen and (min-width: 1601px) {
  .g-editor h1, .g-editor h2, .g-editor h3, .g-editor h4, .g-editor h5, .g-editor h6 {
    margin-bottom: 3rem;
    margin-top: 6rem; } }
/**********************************************************
 * Wrapper for section headings
 **********************************************************/
.g-head {
  margin-bottom: 2rem; }
  .g-head--vertical {
    display: flex;
    flex-direction: column;
    gap: 2.5rem; }
  .g-head--inner {
    position: relative; }
  .g-head--inner:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -1rem;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, #939393 0%, rgba(45, 45, 45, 0) 41.35%); }
  @media only screen and (min-width: 641px) {
  .g-head--spaced {
    margin-bottom: 3rem; } }
  @media only screen and (min-width: 1025px) {
  .g-head {
    margin-bottom: 2rem; }
    .g-head--spaced {
      margin-bottom: 4rem; } }
  @media only screen and (min-width: 1281px) {
  .g-head {
    margin-bottom: 4rem; }
    .g-head--spaced {
      margin-bottom: 6rem; } }
  @media only screen and (min-width: 1601px) {
  .g-head {
    margin-bottom: 5rem; }
    .g-head--spaced {
      margin-bottom: 8rem; } }
/**********************************************************
 * Show/hide elements from a breakpoint up
 **********************************************************/
.is-hidden {
  display: none !important; }

@media only screen and (min-width: 481px) {
  .is-inline--sm {
    display: inline-block !important; }
    .is-block--sm {
      display: block !important; }
    .is-flex--sm {
      display: flex !important; }
    .is-inline-flex--sm {
      display: inline-flex !important; }
    .is-hidden--sm {
      display: none !important; } }

@media only screen and (min-width: 641px) {
  .is-inline--md {
    display: inline-block !important; }
    .is-block--md {
      display: block !important; }
    .is-flex--md {
      display: flex !important; }
    .is-inline-flex--md {
      display: inline-flex !important; }
    .is-hidden--md {
      display: none !important; } }

@media only screen and (min-width: 1025px) {
  .is-inline--xm {
    display: inline-block !important; }
    .is-block--xm {
      display: block !important; }
    .is-flex--xm {
      display: flex !important; }
    .is-inline-flex--xm {
      display: inline-flex !important; }
    .is-hidden--xm {
      display: none !important; } }

@media only screen and (min-width: 1281px) {
  .is-inline--lg {
    display: inline-block !important; }
    .is-block--lg {
      display: block !important; }
    .is-flex--lg {
      display: flex !important; }
    .is-inline-flex--lg {
      display: inline-flex !important; }
    .is-hidden--lg {
      display: none !important; } }
/**********************************************************
 * Padded sections
 **********************************************************/
.g-padded--xl {
  padding-bottom: 7rem;
  padding-top: 7rem; }
  .g-padded--lg {
    padding-bottom: 6rem;
    padding-top: 6rem; }
  .g-padded--xm {
    padding-bottom: 5rem;
    padding-top: 5rem;
    position: relative; }
  .g-padded--md {
    padding-bottom: 5rem;
    padding-top: 5rem;
    position: relative;
    z-index: 1; }
  .g-padded--sm {
    padding-bottom: 3rem;
    padding-top: 3rem;
    position: relative; }
  .g-padded--no-top {
    padding-top: 0 !important; }
  .g-padded--no-bottom {
    padding-bottom: 0 !important; }
  @media only screen and (min-width: 1025px) {
  .g-padded--xl {
    padding-bottom: 15rem;
    padding-top: 15rem; }
    .g-padded--lg {
      padding-bottom: 10rem;
      padding-top: 10rem; }

    .g-padded--xm {
      padding-bottom: 8rem;
      padding-top: 8rem; }

    .g-padded--md {
      padding-bottom: 6rem;
      padding-top: 6rem; }

    .g-padded--sm {
      padding-bottom: 3rem;
      padding-top: 3rem; }

    .g-padded--xs {
      padding-bottom: 2rem;
      padding-top: 2rem; } }
  @media only screen and (min-width: 1281px) {
  .g-padded--xl {
    padding-bottom: 20rem;
    padding-top: 20rem; }
    .g-padded--lg {
      padding-bottom: 15rem;
      padding-top: 15rem; }

    .g-padded--xm {
      padding-bottom: 12rem;
      padding-top: 12rem; }

    .g-padded--md {
      padding-bottom: 8rem;
      padding-top: 8rem; }

    .g-padded--sm {
      padding-bottom: 5rem;
      padding-top: 5rem; }

    .g-padded--xs {
      padding-bottom: 3rem;
      padding-top: 3rem; } }
/*!
 Pure v1.0.0
 Copyright 2013 Yahoo!
 Licensed under the BSD License.
 https://github.com/yahoo/pure/blob/master/LICENSE.md
 */
/*csslint regex-selectors:false, known-properties:false, duplicate-properties:false*/
.g-row {
  letter-spacing: -0.31em;
  /* Webkit: collapse white-space between units */
  *letter-spacing: normal;
  /* reset IE < 8 */
  *word-spacing: -0.43em;
  /* IE < 8: collapse white-space between units */
  max-width: 100%;
  text-rendering: optimizespeed;
  /* Webkit: fixes text-rendering: optimizeLegibility */
  width: 100%;
  /* Use flexbox when possible to avoid `letter-spacing` side-effects. */
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-flow: row wrap;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  /* Prevents distributing space between rows */
  -webkit-align-content: flex-start;
  -ms-flex-line-pack: start;
  align-content: flex-start; }
/* IE10 display: -ms-flexbox (and display: flex in IE 11) does not work inside a table; fall back to block and rely on font hack */
@media (-ms-high-contrast: none), (-ms-high-contrast: active) {
  table .g-row {
    display: block; } }
/* Opera as of 12 on Windows needs word-spacing.
 The ".opera-only" selector is used to prevent actual prefocus styling
 and is not required in markup.
 */
.opera-only :-o-prefocus, .g-row {
  word-spacing: -0.43em; }

.col {
  display: inline-block;
  *display: inline;
  /* IE < 8: fake inline-block */
  zoom: 1;
  letter-spacing: normal;
  word-spacing: normal;
  vertical-align: top;
  text-rendering: auto; }

.col-1, .col-1-1, .col-1-2, .col-1-3, .col-2-3, .col-1-4, .col-3-4, .col-1-5, .col-2-5, .col-3-5, .col-4-5, .col-5-5, .col-1-6, .col-5-6, .col-1-8, .col-3-8, .col-5-8, .col-7-8, .col-1-12, .col-5-12, .col-7-12, .col-11-12, .col-1-24, .col-2-24, .col-3-24, .col-4-24, .col-5-24, .col-6-24, .col-7-24, .col-8-24, .col-9-24, .col-10-24, .col-11-24, .col-12-24, .col-13-24, .col-14-24, .col-15-24, .col-16-24, .col-17-24, .col-18-24, .col-19-24, .col-20-24, .col-21-24, .col-22-24, .col-23-24, .col-24-24 {
  display: inline-block;
  *display: inline;
  zoom: 1;
  letter-spacing: normal;
  word-spacing: normal;
  vertical-align: top;
  text-rendering: auto; }

.col-1-24 {
  width: 4.1667%;
  *width: 4.1357%; }

.col-1-12, .col-2-24 {
  width: 8.3333%;
  *width: 8.3023%; }

.col-1-8, .col-3-24 {
  width: 12.5%;
  *width: 12.469%; }

.col-1-6, .col-4-24 {
  width: 16.6667%;
  *width: 16.6357%; }

.col-1-5 {
  width: 20%;
  *width: 19.969%; }

.col-5-24 {
  width: 20.8333%;
  *width: 20.8023%; }

.col-1-4, .col-6-24 {
  width: 25%;
  *width: 24.969%; }

.col-7-24 {
  width: 29.1667%;
  *width: 29.1357%; }

.col-1-3, .col-8-24 {
  width: 33.3333%;
  *width: 33.3023%; }

.col-3-8, .col-9-24 {
  width: 37.5%;
  *width: 37.469%; }

.col-2-5 {
  width: 40%;
  *width: 39.969%; }

.col-5-12, .col-10-24 {
  width: 41.6667%;
  *width: 41.6357%; }

.col-11-24 {
  width: 45.8333%;
  *width: 45.8023%; }

.col-1-2, .col-12-24 {
  width: 50%;
  *width: 49.969%; }

.col-13-24 {
  width: 54.1667%;
  *width: 54.1357%; }

.col-7-12, .col-14-24 {
  width: 58.3333%;
  *width: 58.3023%; }

.col-3-5 {
  width: 60%;
  *width: 59.969%; }

.col-5-8, .col-15-24 {
  width: 62.5%;
  *width: 62.469%; }

.col-2-3, .col-16-24 {
  width: 66.6667%;
  *width: 66.6357%; }

.col-17-24 {
  width: 70.8333%;
  *width: 70.8023%; }

.col-3-4, .col-18-24 {
  width: 75%;
  *width: 74.969%; }

.col-19-24 {
  width: 79.1667%;
  *width: 79.1357%; }

.col-4-5 {
  width: 80%;
  *width: 79.969%; }

.col-5-6, .col-20-24 {
  width: 83.3333%;
  *width: 83.3023%; }

.col-7-8, .col-21-24 {
  width: 87.5%;
  *width: 87.469%; }

.col-11-12, .col-22-24 {
  width: 91.6667%;
  *width: 91.6357%; }

.col-23-24 {
  width: 95.8333%;
  *width: 95.8023%; }

.col-1, .col-1-1, .col-5-5, .col-24-24 {
  width: 100%; }

@media only screen and (min-width: 321px) {
  .col-xs-1, .col-xs-1-1, .col-xs-1-2, .col-xs-1-3, .col-xs-2-3, .col-xs-1-4, .col-xs-3-4, .col-xs-1-5, .col-xs-2-5, .col-xs-3-5, .col-xs-4-5, .col-xs-5-5, .col-xs-1-6, .col-xs-5-6, .col-xs-1-8, .col-xs-3-8, .col-xs-5-8, .col-xs-7-8, .col-xs-1-12, .col-xs-5-12, .col-xs-7-12, .col-xs-11-12, .col-xs-1-24, .col-xs-2-24, .col-xs-3-24, .col-xs-4-24, .col-xs-5-24, .col-xs-6-24, .col-xs-7-24, .col-xs-8-24, .col-xs-9-24, .col-xs-10-24, .col-xs-11-24, .col-xs-12-24, .col-xs-13-24, .col-xs-14-24, .col-xs-15-24, .col-xs-16-24, .col-xs-17-24, .col-xs-18-24, .col-xs-19-24, .col-xs-20-24, .col-xs-21-24, .col-xs-22-24, .col-xs-23-24, .col-xs-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-xs-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-xs-1-12, .col-xs-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-xs-1-8, .col-xs-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-xs-1-6, .col-xs-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-xs-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-xs-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-xs-1-4, .col-xs-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-xs-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-xs-1-3, .col-xs-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-xs-3-8, .col-xs-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-xs-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-xs-5-12, .col-xs-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-xs-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-xs-1-2, .col-xs-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-xs-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-xs-7-12, .col-xs-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-xs-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-xs-5-8, .col-xs-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-xs-2-3, .col-xs-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-xs-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-xs-3-4, .col-xs-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-xs-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-xs-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-xs-5-6, .col-xs-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-xs-7-8, .col-xs-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-xs-11-12, .col-xs-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-xs-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-xs-1, .col-xs-1-1, .col-xs-5-5, .col-xs-24-24 {
      width: 100%; } }

@media only screen and (min-width: 481px) {
  .col-sm-1, .col-sm-1-1, .col-sm-1-2, .col-sm-1-3, .col-sm-2-3, .col-sm-1-4, .col-sm-3-4, .col-sm-1-5, .col-sm-2-5, .col-sm-3-5, .col-sm-4-5, .col-sm-5-5, .col-sm-1-6, .col-sm-5-6, .col-sm-1-8, .col-sm-3-8, .col-sm-5-8, .col-sm-7-8, .col-sm-1-12, .col-sm-5-12, .col-sm-7-12, .col-sm-11-12, .col-sm-1-24, .col-sm-2-24, .col-sm-3-24, .col-sm-4-24, .col-sm-5-24, .col-sm-6-24, .col-sm-7-24, .col-sm-8-24, .col-sm-9-24, .col-sm-10-24, .col-sm-11-24, .col-sm-12-24, .col-sm-13-24, .col-sm-14-24, .col-sm-15-24, .col-sm-16-24, .col-sm-17-24, .col-sm-18-24, .col-sm-19-24, .col-sm-20-24, .col-sm-21-24, .col-sm-22-24, .col-sm-23-24, .col-sm-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-sm-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-sm-1-12, .col-sm-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-sm-1-8, .col-sm-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-sm-1-6, .col-sm-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-sm-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-sm-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-sm-1-4, .col-sm-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-sm-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-sm-1-3, .col-sm-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-sm-3-8, .col-sm-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-sm-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-sm-5-12, .col-sm-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-sm-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-sm-1-2, .col-sm-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-sm-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-sm-7-12, .col-sm-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-sm-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-sm-5-8, .col-sm-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-sm-2-3, .col-sm-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-sm-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-sm-3-4, .col-sm-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-sm-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-sm-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-sm-5-6, .col-sm-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-sm-7-8, .col-sm-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-sm-11-12, .col-sm-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-sm-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-sm-1, .col-sm-1-1, .col-sm-5-5, .col-sm-24-24 {
      width: 100%; } }

@media only screen and (min-width: 641px) {
  .col-md-1, .col-md-1-1, .col-md-1-2, .col-md-1-3, .col-md-2-3, .col-md-1-4, .col-md-3-4, .col-md-1-5, .col-md-2-5, .col-md-3-5, .col-md-4-5, .col-md-5-5, .col-md-1-6, .col-md-5-6, .col-md-1-8, .col-md-3-8, .col-md-5-8, .col-md-7-8, .col-md-1-12, .col-md-5-12, .col-md-7-12, .col-md-11-12, .col-md-1-24, .col-md-2-24, .col-md-3-24, .col-md-4-24, .col-md-5-24, .col-md-6-24, .col-md-7-24, .col-md-8-24, .col-md-9-24, .col-md-10-24, .col-md-11-24, .col-md-12-24, .col-md-13-24, .col-md-14-24, .col-md-15-24, .col-md-16-24, .col-md-17-24, .col-md-18-24, .col-md-19-24, .col-md-20-24, .col-md-21-24, .col-md-22-24, .col-md-23-24, .col-md-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-md-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-md-1-12, .col-md-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-md-1-8, .col-md-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-md-1-6, .col-md-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-md-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-md-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-md-1-4, .col-md-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-md-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-md-1-3, .col-md-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-md-3-8, .col-md-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-md-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-md-5-12, .col-md-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-md-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-md-1-2, .col-md-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-md-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-md-7-12, .col-md-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-md-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-md-5-8, .col-md-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-md-2-3, .col-md-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-md-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-md-3-4, .col-md-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-md-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-md-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-md-5-6, .col-md-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-md-7-8, .col-md-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-md-11-12, .col-md-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-md-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-md-1, .col-md-1-1, .col-md-5-5, .col-md-24-24 {
      width: 100%; } }

@media only screen and (min-width: 1025px) {
  .col-xm-1, .col-xm-1-1, .col-xm-1-2, .col-xm-1-3, .col-xm-2-3, .col-xm-1-4, .col-xm-3-4, .col-xm-1-5, .col-xm-2-5, .col-xm-3-5, .col-xm-4-5, .col-xm-5-5, .col-xm-1-6, .col-xm-5-6, .col-xm-1-8, .col-xm-3-8, .col-xm-5-8, .col-xm-7-8, .col-xm-1-12, .col-xm-5-12, .col-xm-7-12, .col-xm-11-12, .col-xm-1-24, .col-xm-2-24, .col-xm-3-24, .col-xm-4-24, .col-xm-5-24, .col-xm-6-24, .col-xm-7-24, .col-xm-8-24, .col-xm-9-24, .col-xm-10-24, .col-xm-11-24, .col-xm-12-24, .col-xm-13-24, .col-xm-14-24, .col-xm-15-24, .col-xm-16-24, .col-xm-17-24, .col-xm-18-24, .col-xm-19-24, .col-xm-20-24, .col-xm-21-24, .col-xm-22-24, .col-xm-23-24, .col-xm-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-xm-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-xm-1-12, .col-xm-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-xm-1-8, .col-xm-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-xm-1-6, .col-xm-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-xm-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-xm-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-xm-1-4, .col-xm-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-xm-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-xm-1-3, .col-xm-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-xm-3-8, .col-xm-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-xm-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-xm-5-12, .col-xm-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-xm-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-xm-1-2, .col-xm-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-xm-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-xm-7-12, .col-xm-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-xm-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-xm-5-8, .col-xm-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-xm-2-3, .col-xm-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-xm-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-xm-3-4, .col-xm-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-xm-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-xm-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-xm-5-6, .col-xm-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-xm-7-8, .col-xm-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-xm-11-12, .col-xm-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-xm-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-xm-1, .col-xm-1-1, .col-xm-5-5, .col-xm-24-24 {
      width: 100%; } }

@media only screen and (min-width: 1281px) {
  .col-lg-1, .col-lg-1-1, .col-lg-1-2, .col-lg-1-3, .col-lg-2-3, .col-lg-1-4, .col-lg-3-4, .col-lg-1-5, .col-lg-2-5, .col-lg-3-5, .col-lg-4-5, .col-lg-5-5, .col-lg-1-6, .col-lg-5-6, .col-lg-1-8, .col-lg-3-8, .col-lg-5-8, .col-lg-7-8, .col-lg-1-12, .col-lg-5-12, .col-lg-7-12, .col-lg-11-12, .col-lg-1-24, .col-lg-2-24, .col-lg-3-24, .col-lg-4-24, .col-lg-5-24, .col-lg-6-24, .col-lg-7-24, .col-lg-8-24, .col-lg-9-24, .col-lg-10-24, .col-lg-11-24, .col-lg-12-24, .col-lg-13-24, .col-lg-14-24, .col-lg-15-24, .col-lg-16-24, .col-lg-17-24, .col-lg-18-24, .col-lg-19-24, .col-lg-20-24, .col-lg-21-24, .col-lg-22-24, .col-lg-23-24, .col-lg-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-lg-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-lg-1-12, .col-lg-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-lg-1-8, .col-lg-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-lg-1-6, .col-lg-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-lg-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-lg-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-lg-1-4, .col-lg-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-lg-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-lg-1-3, .col-lg-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-lg-3-8, .col-lg-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-lg-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-lg-5-12, .col-lg-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-lg-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-lg-1-2, .col-lg-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-lg-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-lg-7-12, .col-lg-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-lg-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-lg-5-8, .col-lg-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-lg-2-3, .col-lg-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-lg-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-lg-3-4, .col-lg-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-lg-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-lg-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-lg-5-6, .col-lg-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-lg-7-8, .col-lg-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-lg-11-12, .col-lg-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-lg-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-lg-1, .col-lg-1-1, .col-lg-5-5, .col-lg-24-24 {
      width: 100%; } }

@media only screen and (min-width: 1601px) {
  .col-xl-1, .col-xl-1-1, .col-xl-1-2, .col-xl-1-3, .col-xl-2-3, .col-xl-1-4, .col-xl-3-4, .col-xl-1-5, .col-xl-2-5, .col-xl-3-5, .col-xl-4-5, .col-xl-5-5, .col-xl-1-6, .col-xl-5-6, .col-xl-1-8, .col-xl-3-8, .col-xl-5-8, .col-xl-7-8, .col-xl-1-12, .col-xl-5-12, .col-xl-7-12, .col-xl-11-12, .col-xl-1-24, .col-xl-2-24, .col-xl-3-24, .col-xl-4-24, .col-xl-5-24, .col-xl-6-24, .col-xl-7-24, .col-xl-8-24, .col-xl-9-24, .col-xl-10-24, .col-xl-11-24, .col-xl-12-24, .col-xl-13-24, .col-xl-14-24, .col-xl-15-24, .col-xl-16-24, .col-xl-17-24, .col-xl-18-24, .col-xl-19-24, .col-xl-20-24, .col-xl-21-24, .col-xl-22-24, .col-xl-23-24, .col-xl-24-24 {
    display: inline-block;
    *display: inline;
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto; }
    .col-xl-1-24 {
      width: 4.1667%;
      *width: 4.1357%; }
    .col-xl-1-12, .col-xl-2-24 {
      width: 8.3333%;
      *width: 8.3023%; }
    .col-xl-1-8, .col-xl-3-24 {
      width: 12.5%;
      *width: 12.469%; }
    .col-xl-1-6, .col-xl-4-24 {
      width: 16.6667%;
      *width: 16.6357%; }
    .col-xl-1-5 {
      width: 20%;
      *width: 19.969%; }
    .col-xl-5-24 {
      width: 20.8333%;
      *width: 20.8023%; }
    .col-xl-1-4, .col-xl-6-24 {
      width: 25%;
      *width: 24.969%; }
    .col-xl-7-24 {
      width: 29.1667%;
      *width: 29.1357%; }
    .col-xl-1-3, .col-xl-8-24 {
      width: 33.3333%;
      *width: 33.3023%; }
    .col-xl-3-8, .col-xl-9-24 {
      width: 37.5%;
      *width: 37.469%; }
    .col-xl-2-5 {
      width: 40%;
      *width: 39.969%; }
    .col-xl-5-12, .col-xl-10-24 {
      width: 41.6667%;
      *width: 41.6357%; }
    .col-xl-11-24 {
      width: 45.8333%;
      *width: 45.8023%; }
    .col-xl-1-2, .col-xl-12-24 {
      width: 50%;
      *width: 49.969%; }
    .col-xl-13-24 {
      width: 54.1667%;
      *width: 54.1357%; }
    .col-xl-7-12, .col-xl-14-24 {
      width: 58.3333%;
      *width: 58.3023%; }
    .col-xl-3-5 {
      width: 60%;
      *width: 59.969%; }
    .col-xl-5-8, .col-xl-15-24 {
      width: 62.5%;
      *width: 62.469%; }
    .col-xl-2-3, .col-xl-16-24 {
      width: 66.6667%;
      *width: 66.6357%; }
    .col-xl-17-24 {
      width: 70.8333%;
      *width: 70.8023%; }
    .col-xl-3-4, .col-xl-18-24 {
      width: 75%;
      *width: 74.969%; }
    .col-xl-19-24 {
      width: 79.1667%;
      *width: 79.1357%; }
    .col-xl-4-5 {
      width: 80%;
      *width: 79.969%; }
    .col-xl-5-6, .col-xl-20-24 {
      width: 83.3333%;
      *width: 83.3023%; }
    .col-xl-7-8, .col-xl-21-24 {
      width: 87.5%;
      *width: 87.469%; }
    .col-xl-11-12, .col-xl-22-24 {
      width: 91.6667%;
      *width: 91.6357%; }
    .col-xl-23-24 {
      width: 95.8333%;
      *width: 95.8023%; }
    .col-xl-1, .col-xl-1-1, .col-xl-5-5, .col-xl-24-24 {
      width: 100%; } }
/**********************************************************
 * Gutters between columns
 **********************************************************/
.g-gap-0 {
  margin-left: -0vw;
  margin-right: -0vw;
  max-width: none;
  width: auto; }
  .g-gap-0 > [class^="col-"], .g-gap-0 > [class*=" col-"] {
    padding-left: 0vw;
    padding-right: 0vw; }

.g-gap-1 {
  margin-left: -1vw;
  margin-right: -1vw;
  max-width: none;
  width: auto; }
  .g-gap-1 > [class^="col-"], .g-gap-1 > [class*=" col-"] {
    padding-left: 1vw;
    padding-right: 1vw; }

.g-gap-2 {
  margin-left: -2vw;
  margin-right: -2vw;
  max-width: none;
  width: auto; }
  .g-gap-2 > [class^="col-"], .g-gap-2 > [class*=" col-"] {
    padding-left: 2vw;
    padding-right: 2vw; }

.g-gap-3 {
  margin-left: -3vw;
  margin-right: -3vw;
  max-width: none;
  width: auto; }
  .g-gap-3 > [class^="col-"], .g-gap-3 > [class*=" col-"] {
    padding-left: 3vw;
    padding-right: 3vw; }

.g-gap-4 {
  margin-left: -4vw;
  margin-right: -4vw;
  max-width: none;
  width: auto; }
  .g-gap-4 > [class^="col-"], .g-gap-4 > [class*=" col-"] {
    padding-left: 4vw;
    padding-right: 4vw; }

.g-gap-5 {
  margin-left: -5vw;
  margin-right: -5vw;
  max-width: none;
  width: auto; }
  .g-gap-5 > [class^="col-"], .g-gap-5 > [class*=" col-"] {
    padding-left: 5vw;
    padding-right: 5vw; }

.g-gap-v-0 > [class^="col-"], .g-gap-v-0 > [class*=" col-"] {
  margin-bottom: 0rem; }

.g-gap-v-1 > [class^="col-"], .g-gap-v-1 > [class*=" col-"] {
  margin-bottom: 2rem; }

.g-gap-v-2 > [class^="col-"], .g-gap-v-2 > [class*=" col-"] {
  margin-bottom: 4rem; }

.g-gap-v-3 > [class^="col-"], .g-gap-v-3 > [class*=" col-"] {
  margin-bottom: 6rem; }

.g-gap-v-4 > [class^="col-"], .g-gap-v-4 > [class*=" col-"] {
  margin-bottom: 8rem; }

.g-gap-v-5 > [class^="col-"], .g-gap-v-5 > [class*=" col-"] {
  margin-bottom: 10rem; }

@media only screen and (min-width: 321px) {
    .g-gap-xs-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-xs-0 > [class^="col-"], .g-gap-xs-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-xs-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-xs-1 > [class^="col-"], .g-gap-xs-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-xs-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-xs-2 > [class^="col-"], .g-gap-xs-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-xs-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-xs-3 > [class^="col-"], .g-gap-xs-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-xs-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-xs-4 > [class^="col-"], .g-gap-xs-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-xs-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-xs-5 > [class^="col-"], .g-gap-xs-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-xs-0 > [class^="col-"], .g-gap-v-xs-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-xs-1 > [class^="col-"], .g-gap-v-xs-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-xs-2 > [class^="col-"], .g-gap-v-xs-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-xs-3 > [class^="col-"], .g-gap-v-xs-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-xs-4 > [class^="col-"], .g-gap-v-xs-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-xs-5 > [class^="col-"], .g-gap-v-xs-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

@media only screen and (min-width: 481px) {
    .g-gap-sm-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-sm-0 > [class^="col-"], .g-gap-sm-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-sm-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-sm-1 > [class^="col-"], .g-gap-sm-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-sm-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-sm-2 > [class^="col-"], .g-gap-sm-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-sm-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-sm-3 > [class^="col-"], .g-gap-sm-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-sm-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-sm-4 > [class^="col-"], .g-gap-sm-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-sm-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-sm-5 > [class^="col-"], .g-gap-sm-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-sm-0 > [class^="col-"], .g-gap-v-sm-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-sm-1 > [class^="col-"], .g-gap-v-sm-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-sm-2 > [class^="col-"], .g-gap-v-sm-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-sm-3 > [class^="col-"], .g-gap-v-sm-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-sm-4 > [class^="col-"], .g-gap-v-sm-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-sm-5 > [class^="col-"], .g-gap-v-sm-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

@media only screen and (min-width: 641px) {
    .g-gap-md-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-md-0 > [class^="col-"], .g-gap-md-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-md-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-md-1 > [class^="col-"], .g-gap-md-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-md-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-md-2 > [class^="col-"], .g-gap-md-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-md-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-md-3 > [class^="col-"], .g-gap-md-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-md-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-md-4 > [class^="col-"], .g-gap-md-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-md-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-md-5 > [class^="col-"], .g-gap-md-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-md-0 > [class^="col-"], .g-gap-v-md-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-md-1 > [class^="col-"], .g-gap-v-md-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-md-2 > [class^="col-"], .g-gap-v-md-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-md-3 > [class^="col-"], .g-gap-v-md-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-md-4 > [class^="col-"], .g-gap-v-md-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-md-5 > [class^="col-"], .g-gap-v-md-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

@media only screen and (min-width: 1025px) {
    .g-gap-xm-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-xm-0 > [class^="col-"], .g-gap-xm-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-xm-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-xm-1 > [class^="col-"], .g-gap-xm-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-xm-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-xm-2 > [class^="col-"], .g-gap-xm-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-xm-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-xm-3 > [class^="col-"], .g-gap-xm-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-xm-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-xm-4 > [class^="col-"], .g-gap-xm-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-xm-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-xm-5 > [class^="col-"], .g-gap-xm-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-xm-0 > [class^="col-"], .g-gap-v-xm-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-xm-1 > [class^="col-"], .g-gap-v-xm-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-xm-2 > [class^="col-"], .g-gap-v-xm-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-xm-3 > [class^="col-"], .g-gap-v-xm-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-xm-4 > [class^="col-"], .g-gap-v-xm-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-xm-5 > [class^="col-"], .g-gap-v-xm-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

@media only screen and (min-width: 1281px) {
    .g-gap-lg-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-lg-0 > [class^="col-"], .g-gap-lg-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-lg-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-lg-1 > [class^="col-"], .g-gap-lg-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-lg-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-lg-2 > [class^="col-"], .g-gap-lg-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-lg-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-lg-3 > [class^="col-"], .g-gap-lg-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-lg-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-lg-4 > [class^="col-"], .g-gap-lg-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-lg-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-lg-5 > [class^="col-"], .g-gap-lg-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-lg-0 > [class^="col-"], .g-gap-v-lg-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-lg-1 > [class^="col-"], .g-gap-v-lg-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-lg-2 > [class^="col-"], .g-gap-v-lg-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-lg-3 > [class^="col-"], .g-gap-v-lg-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-lg-4 > [class^="col-"], .g-gap-v-lg-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-lg-5 > [class^="col-"], .g-gap-v-lg-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

@media only screen and (min-width: 1601px) {
    .g-gap-xl-0 {
      margin-left: -0vw;
      margin-right: -0vw;
      max-width: none;
      width: auto; }
      .g-gap-xl-0 > [class^="col-"], .g-gap-xl-0 > [class*=" col-"] {
        padding-left: 0vw;
        padding-right: 0vw; }
      .g-gap-xl-1 {
        margin-left: -1vw;
        margin-right: -1vw;
        max-width: none;
        width: auto; }
      .g-gap-xl-1 > [class^="col-"], .g-gap-xl-1 > [class*=" col-"] {
        padding-left: 1vw;
        padding-right: 1vw; }
      .g-gap-xl-2 {
        margin-left: -2vw;
        margin-right: -2vw;
        max-width: none;
        width: auto; }
      .g-gap-xl-2 > [class^="col-"], .g-gap-xl-2 > [class*=" col-"] {
        padding-left: 2vw;
        padding-right: 2vw; }
      .g-gap-xl-3 {
        margin-left: -3vw;
        margin-right: -3vw;
        max-width: none;
        width: auto; }
      .g-gap-xl-3 > [class^="col-"], .g-gap-xl-3 > [class*=" col-"] {
        padding-left: 3vw;
        padding-right: 3vw; }
      .g-gap-xl-4 {
        margin-left: -4vw;
        margin-right: -4vw;
        max-width: none;
        width: auto; }
      .g-gap-xl-4 > [class^="col-"], .g-gap-xl-4 > [class*=" col-"] {
        padding-left: 4vw;
        padding-right: 4vw; }
      .g-gap-xl-5 {
        margin-left: -5vw;
        margin-right: -5vw;
        max-width: none;
        width: auto; }
      .g-gap-xl-5 > [class^="col-"], .g-gap-xl-5 > [class*=" col-"] {
        padding-left: 5vw;
        padding-right: 5vw; }
      .g-gap-v-xl-0 > [class^="col-"], .g-gap-v-xl-0 > [class*=" col-"] {
        margin-bottom: 0rem; }
      .g-gap-v-xl-1 > [class^="col-"], .g-gap-v-xl-1 > [class*=" col-"] {
        margin-bottom: 2rem; }
      .g-gap-v-xl-2 > [class^="col-"], .g-gap-v-xl-2 > [class*=" col-"] {
        margin-bottom: 4rem; }
      .g-gap-v-xl-3 > [class^="col-"], .g-gap-v-xl-3 > [class*=" col-"] {
        margin-bottom: 6rem; }
      .g-gap-v-xl-4 > [class^="col-"], .g-gap-v-xl-4 > [class*=" col-"] {
        margin-bottom: 8rem; }
      .g-gap-v-xl-5 > [class^="col-"], .g-gap-v-xl-5 > [class*=" col-"] {
        margin-bottom: 10rem; } }

.g-gap--keep {
  margin-left: 0;
  margin-right: 0; }
/**********************************************************
 * Reverse column order up to a breakpoint
 **********************************************************/
.g-invert, .g-invert--sm, .g-invert--md, .g-invert--xm, .g-invert--lg, .g-invert--xl {
  flex-direction: column-reverse; }

@media only screen and (min-width: 481px) {
  .g-invert--sm {
    flex-direction: row; } }

@media only screen and (min-width: 641px) {
  .g-invert--md {
    flex-direction: row; } }

@media only screen and (min-width: 1025px) {
  .g-invert--xm {
    flex-direction: row; } }

@media only screen and (min-width: 1281px) {
  .g-invert--lg {
    flex-direction: row; } }

@media only screen and (min-width: 1601px) {
  .g-invert--xl {
    flex-direction: row; } }
/**********************************************************
 * Spaced sections
 **********************************************************/
.g-section--xl {
  margin-bottom: 7rem;
  margin-top: 7rem; }

.g-section--lg {
  margin-bottom: 6rem;
  margin-top: 6rem; }

.g-section--xm {
  margin-bottom: 5rem;
  margin-top: 5rem;
  position: relative; }

.g-section--md {
  margin-bottom: 4rem;
  margin-top: 4rem;
  position: relative;
  z-index: 1; }

.g-section--sm {
  margin-bottom: 3rem;
  margin-top: 3rem;
  position: relative; }

.g-section--xs {
  margin-bottom: 1.5rem;
  margin-top: 1.5rem;
  position: relative; }

.g-section--no-top {
  margin-top: 0 !important; }

.g-section--no-bottom {
  margin-bottom: 0 !important; }

@media only screen and (min-width: 1025px) {
    .g-section--xl {
      margin-bottom: 15rem;
      margin-top: 15rem; }
      .g-section--lg {
        margin-bottom: 10rem;
        margin-top: 10rem; }
      .g-section--xm {
        margin-bottom: 8rem;
        margin-top: 8rem; }
      .g-section--md {
        margin-bottom: 6rem;
        margin-top: 6rem; }
      .g-section--sm {
        margin-bottom: 3rem;
        margin-top: 3rem; }
      .g-section--xs {
        margin-bottom: 2rem;
        margin-top: 2rem; } }

@media only screen and (min-width: 1281px) {
    .g-section--xl {
      margin-bottom: 20rem;
      margin-top: 20rem; }
      .g-section--lg {
        margin-bottom: 15rem;
        margin-top: 15rem; }
      .g-section--xm {
        margin-bottom: 12rem;
        margin-top: 12rem; }
      .g-section--md {
        margin-bottom: 8rem;
        margin-top: 8rem; }
      .g-section--sm {
        margin-bottom: 5rem;
        margin-top: 5rem; }
      .g-section--xs {
        margin-bottom: 3rem;
        margin-top: 3rem; } }
/**********************************************************
 * Headings
 **********************************************************/
.g-title {
  display: block;
  line-height: 1em;
  margin-bottom: 2rem;
  font-family: "Prosto One", Arial, sans-serif;
  /* Size */
  /* Variations */ }

.g-title--xl {
  font-size: clamp(3.2rem, 6vw, 9.6rem); }

.g-title--lg {
  font-size: clamp(3.2rem, 5vw, 7.4rem); }

.g-title--xm {
  font-size: clamp(2.4rem, 3.6vw, 6rem); }

.g-title--m {
  font-size: clamp(1.2rem, 3vw, 4.8rem); }

.g-title--md {
  font-size: clamp(2rem, 3vw, 3.8rem); }

.g-title--sm {
  font-size: clamp(2rem, 2vw, 3rem); }

.g-title--xs {
  font-size: clamp(1.8rem, 1.5vw, 2.4rem); }

.g-title--sub {
  display: inline-flex;
  gap: 0.5rem;
  align-items: center; }

.g-title--sub:before {
  content: '';
  width: 9px;
  min-width: 9px;
  height: 9px;
  background-color: currentColor;
  position: relative;
  border-radius: 50%; }

.g-title--color span {
  color: #a88861 !important; }

.g-title--color-black span {
  color: #071b34 !important; }

.g-title--inline {
  display: inline-block; }

.g-title--center {
  align-items: center;
  text-align: center; }

.g-title--no-bottom {
  margin-bottom: 0 !important; }

.g-title--underline {
  line-height: 1.2em; }

.g-title--underline span {
  position: relative;
  display: inline-flex;
  flex-direction: column; }
  .g-title--underline span::after {
    content: '';
    position: relative;
    width: 100%;
    height: 0.3em;
    background: url("../image/underline-sm.svg") no-repeat top center;
    object-fit: contain; }

.g-title--underline-primary {
  line-height: 1.2em; }

.g-title--underline-primary span {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  color: #a88861; }
  .g-title--underline-primary span::after {
    content: '';
    position: relative;
    width: 100%;
    height: 0.3em;
    background: url("../image/underline-primary.svg") no-repeat top center;
    object-fit: contain; }

@media only screen and (min-width: 1025px) {
    .g-title {
      margin-bottom: 3rem; } }
/**********************************************************
 * Wrap containers
 **********************************************************/
.g-wrap {
  /* Limited width */
  margin-left: auto;
  margin-right: auto;
  max-width: 1440px;
  padding-left: 20px;
  padding-right: 20px;
  width: 100%;
  /* Global wrapper - prevents horizontal scroll */ }

.g-wrap--main {
  clear: both;
  min-height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 3; }

.g-wrap--spaced {
  padding-left: 20px;
  padding-right: 20px; }

.g-wrap--xxl {
  max-width: 1780px; }

.g-wrap--xl {
  max-width: 1660px; }

.g-wrap--lg {
  max-width: 1140px; }

.g-wrap--xm {
  max-width: 1000px; }

.g-wrap--md {
  max-width: 740px; }

.g-wrap--sm {
  max-width: 600px; }

@media only screen and (min-width: 641px) {
    .g-wrap {
      padding-left: 30px;
      padding-right: 30px; } }

@media only screen and (min-width: 1601px) {
    .g-wrap--spaced {
      padding: 0 5rem; } }
/**********************************************************
 * 
 **********************************************************/
.btn {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  font-family: "Prosto One", Arial, sans-serif;
  align-items: center;
  display: inline-flex;
  cursor: pointer;
  background: none;
  border: none;
  justify-content: center;
  line-height: 1em;
  pointer-events: all;
  position: relative;
  white-space: nowrap;
  border-radius: 3px;
  gap: 1rem;
  /* Wrap 2 buttons together */ }

.btn:focus {
  outline: none; }

.btn--md {
  padding: 1rem 1rem 1rem 1.5rem; }

.btn--lg {
  padding: 1.5rem 2rem; }

.btn--full {
  width: 100%; }

.btn--black {
  background-color: #071b34;
  color: #fff;
  border: 1px solid #071b34; }

.btn--black:hover {
  background-color: transparent;
  color: #071b34; }
  .btn--black:hover i {
    filter: invert(1); }

.btn--white {
  background-color: #fff;
  color: #071b34;
  border: 1px solid #fff; }

.btn--white:hover {
  background-color: #a88861;
  color: #fff; }
  .btn--white:hover i {
    background-image: url("../image/icon/arrow-btn-white.svg");
    height: 24px;
    width: 24px; }

.btn--primary {
  background-color: #a88861;
  color: #fff;
  border: 1px solid #a88861; }

.btn--primary:hover {
  background-color: #fff;
  color: #a88861; }
  .btn--primary:hover i {
    background-image: url("../image/icon/arrow-btn-primary.svg");
    height: 24px;
    width: 24px; }

.btn--cta {
  border: 1px solid #a88861;
  color: #a88861;
  font-size: clamp(1.4rem, 1vw, 1.6rem); }

.btn--cta:hover {
  background-color: #a88861;
  color: #fff; }
  .btn--cta:hover i {
    background-image: url("../image/icon/chat-white.svg");
    height: 24px;
    width: 24px; }

.btn--partner {
  font-size: clamp(1.4rem, 1vw, 1.6rem); }

.btn .icon, .btn__icon {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  display: inline-block; }

.btn__wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem; }
/**********************************************************
 * Icon styles
 **********************************************************/
.icon {
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  display: inline-block;
  flex-shrink: 0;
  /* Space between icon/label */
  /*  Alphabetical list of SVG icons
   Format: @include icon('name', width, height)
   See abstract/mixins.scss for the icon mixin
   */ }

.icon--spaced {
  vertical-align: middle; }

.icon--spaced:first-child:not(:last-child) {
  margin-right: 1rem; }

.icon--spaced:last-child:not(:first-child) {
  margin-left: 1rem; }

.icon--arrow-left {
  background-image: url("../image/icon/arrow-left.svg");
  height: 15px;
  width: 22px; }

.icon--arrow-right {
  background-image: url("../image/icon/arrow-right.svg");
  height: 15px;
  width: 22px; }

.icon--arrow-left-gray {
  background-image: url("../image/icon/arrow-left-gray.svg");
  height: 15px;
  width: 34px; }

.icon--arrow-right-gray {
  background-image: url("../image/icon/arrow-right-gray.svg");
  height: 15px;
  width: 34px; }

.icon--caret {
  background-image: url("../image/icon/caret.svg");
  height: 6px;
  width: 12px; }

.icon--check-sm {
  background-image: url("../image/icon/check-sm.svg");
  height: 10px;
  width: 12px; }

.icon--nav-open {
  background-image: url("../image/icon/nav-open.svg");
  height: 15px;
  width: 32px; }

.icon--nav-close {
  background-image: url("../image/icon/nav-close.svg");
  height: 24px;
  width: 24px; }

.icon--num-minus {
  background-image: url("../image/icon/num-minus.svg");
  height: 3px;
  width: 11px; }

.icon--num-plus {
  background-image: url("../image/icon/num-plus.svg");
  height: 11px;
  width: 11px; }

.icon--page-prev {
  background-image: url("../image/icon/page-prev.svg");
  height: 12px;
  width: 12px; }

.icon--page-next {
  background-image: url("../image/icon/page-next.svg");
  height: 12px;
  width: 12px; }

.icon--play {
  background-image: url("../image/icon/play.svg");
  height: 33px;
  width: 33px; }

.icon--handshake {
  background-image: url("../image/icon/handshake.svg");
  height: 24px;
  width: 24px; }

.icon--chat {
  background-image: url("../image/icon/chat.svg");
  height: 24px;
  width: 24px; }

.icon--chat-white {
  background-image: url("../image/icon/chat-white.svg");
  height: 24px;
  width: 24px; }

.icon--phone {
  background-image: url("../image/icon/phone.svg");
  height: 24px;
  width: 24px; }

.icon--arrow-btn-primary {
  background-image: url("../image/icon/arrow-btn-primary.svg");
  height: 24px;
  width: 24px; }

.icon--arrow-btn-white {
  background-image: url("../image/icon/arrow-btn-white.svg");
  height: 24px;
  width: 24px; }

.icon--arrow-btn-black {
  background-image: url("../image/icon/arrow-btn-black.svg");
  height: 24px;
  width: 24px; }

.icon--info {
  background-image: url("../image/icon/info.svg");
  height: 24px;
  width: 24px; }

.icon--share {
  background-image: url("../image/icon/share.svg");
  height: 24px;
  width: 24px; }

.icon--roadmap {
  background-image: url("../image/icon/roadmap.svg");
  height: 14px;
  width: 14px; }

.icon--arrow-btn-left {
  background-image: url("../image/icon/arrow-btn-left.svg");
  height: 25px;
  width: 25px; }
/**********************************************************
 * Hide native checkbox, use label before/after
 **********************************************************/
.checkbox {
  margin-bottom: 0.5rem;
  min-height: 2.5rem; }

.checkbox--primary .checkbox__label::before {
  border-color: #a88861; }
  .checkbox--primary .checkbox__label::after {
    background-color: #a88861; }

.checkbox--absolute {
  position: absolute;
  bottom: -1.5rem;
  left: 0;
  transform: translateY(100%); }

.checkbox--white .checkbox__label::before {
  border-color: white; }
  .checkbox--white .checkbox__label::after {
    background-color: white; }

.checkbox__input {
  display: none;
  /* Selected states */ }

.checkbox__input:checked + .checkbox__label::before {
  border-color: black; }
  .checkbox__input:checked + .checkbox__label::after {
    opacity: 1;
    visibility: visible; }

.checkbox__label {
  cursor: pointer;
  display: inline-block;
  line-height: 1.2em;
  padding-left: 2.5rem;
  position: relative;
  text-align: left;
  font-size: 1.5rem;
  /* Radio buttons */
  /* Prevent selection upon fast clicking */ }

.checkbox__label::before, .checkbox__label::after {
  content: '';
  display: inline-block;
  left: 0;
  position: absolute;
  top: 3px;
  border-radius: 1rem; }

.checkbox__label::before {
  border: 1px solid black;
  height: 1rem;
  width: 1rem;
  border-radius: 50%; }

.checkbox__label::after {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  background: black url("../image/icon/check-sm-white.svg") no-repeat center;
  background-size: 50%;
  display: inline-block;
  height: 1.2rem;
  opacity: 0;
  visibility: hidden;
  width: 1.2rem; }

.checkbox__label--radio::before, .checkbox__label--radio::after {
  border-radius: 1rem; }

.checkbox__label::selection {
  background-color: none; }

.checkbox__label a {
  text-decoration: underline; }
  .checkbox__label a:hover {
    opacity: 0.6; }
/**********************************************************
 * 
 **********************************************************/
.consent {
  transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  background-color: white;
  bottom: 0;
  color: black;
  font-size: 1.4rem;
  left: 0;
  position: fixed;
  text-align: center;
  transform: translateY(100%);
  width: 100%;
  z-index: 5; }

.consent.-is-shown {
  transform: translateY(0); }

.consent__inner {
  padding: 1rem 2rem; }

.consent__text a {
  text-decoration: underline; }

.consent__btn {
  margin-top: 1rem; }

@media only screen and (min-width: 641px) {
    .consent__inner {
      align-items: center;
      display: flex;
      justify-content: center; }
      .consent__text {
        margin-right: 3rem; }
      .consent__btn {
        margin-top: 0; } }
/**********************************************************
 * 
 **********************************************************/
.footer {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-bottom: 2rem;
  /* 1024 up */ }

.footer__bg-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; }

.footer__bg-box:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2); }

.footer__bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; }

.footer__newsletter {
  display: flex;
  justify-content: center;
  padding: 5rem 2rem 8rem 2rem;
  background: linear-gradient(131.72deg, #f8e2ae 12.6%, #a88861 46.32%, #e6ce9d 84.43%);
  color: #fff;
  border-radius: 3px; }

.footer__newsletter-inner {
  display: flex;
  flex-direction: column;
  gap: 3vw;
  width: 100%; }

.footer__logo-box {
  width: 15vw;
  min-width: 20rem; }

.footer__wrap {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  border-radius: 3px; }

.footer__container {
  display: flex;
  flex-direction: column;
  gap: 2rem; }

.footer__nav {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  margin-bottom: 6rem; }

.footer__nav-list {
  display: flex;
  flex-direction: column;
  gap: 2.5rem; }

.footer__nav-link {
  font-size: clamp(1.6rem, 1.5vw, 2rem);
  font-family: "Prosto One", Arial, sans-serif; }

.footer__cta {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  max-width: 55rem; }

.footer__end {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
  font-size: 1.4rem;
  padding-bottom: 4rem;
  padding-top: 4rem;
  color: #899597; }

.footer__end-item {
  line-height: 1.2em;
  padding: 0 1rem; }

.footer__end-link {
  display: inline-flex;
  align-items: center;
  gap: 1rem; }

.footer__end-link:hover {
  text-decoration: underline; }

.footer__end-logo {
  display: inline-block;
  vertical-align: 0.7rem; }

.footer__end-logo:hover {
  opacity: 0.7; }

@media only screen and (min-width: 768px) {
    .footer__container {
      flex-direction: row;
      gap: 2.5vw; } }

@media only screen and (min-width: 1025px) {
    .footer__newsletter-inner {
      max-width: 35rem; } }
/**********************************************************
 * Form elements - input, textarea, label
 **********************************************************/
.form {
  text-align: left; }

.form--contact .form__row {
  margin-bottom: 2rem !important;
  gap: 2rem !important; }

.form--contact .form__item {
  width: auto;
  flex: 1 1 40%; }

.form--cta .form__row {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
  gap: 2.2vw;
  margin-bottom: 0; }

.form--cta .form__item {
  margin-bottom: 0;
  width: 16vw;
  min-width: 22rem; }

.form--cta .form__input {
  min-height: 5.6rem;
  background-color: rgba(250, 250, 250, 0.6);
  border: 1px solid rgba(168, 136, 97, 0.6); }

.form--cta .form__label {
  font-weight: 300; }

.form--vertical .form__row {
  gap: 1rem;
  width: 100%; }

.form--vertical .form__item {
  width: 100%; }

.form__item {
  line-height: 1em;
  margin-bottom: 2rem; }

.form__item:last-child {
  margin-bottom: 0; }

.form__row {
  margin-bottom: 1rem; }

.form__label {
  display: block;
  font-weight: bold;
  margin-bottom: 1rem; }

.form__label--spaced {
  margin-bottom: 1.5rem; }

.form__input {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  background: white;
  border: 1px solid gray;
  border-radius: 0.5rem;
  display: block;
  font-size: 1.6rem;
  padding: 0.9rem 1rem;
  width: 100%; }

.form__input--lg {
  padding-bottom: 1.5rem;
  padding-top: 1.5rem; }

.form__input--textarea {
  line-height: 1.3em;
  min-height: 15rem; }

.form__input::placeholder {
  color: gray; }

.form__input:focus {
  outline: none; }

.form__submit {
  margin-top: 2rem; }

@media only screen and (min-width: 481px) {
    .form__submit {
      width: 100%; } }

@media only screen and (min-width: 1025px) {
    .form__item {
      margin-bottom: 2.5rem; }
      .form__label {
        margin-bottom: 1rem; }
        .form__label--spaced {
          margin-bottom: 2rem; } }
/**********************************************************
 * 
 **********************************************************/
.header {
  position: relative;
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  background-color: #fff;
  z-index: 10;
  /* 480 up */
  /* 640 down */
  /* 1024 up */
  /* 1280 up */
  /* 1600 up */ }

.header.-sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  padding-top: 1rem;
  padding-bottom: 1rem; }

.header.-active .-close {
  display: inline-block; }

.header.-active .-open {
  display: none; }

.header.-active .header__hamburger-line {
  background-color: transparent; }
  .header.-active .header__hamburger-line:before {
    transform: rotate(135deg) translate(0.6rem, -0.5rem); }
  .header.-active .header__hamburger-line:after {
    transform: rotate(-135deg) translate(0.6rem, 0.5rem);
    width: 100%; }

.header__wrap {
  display: flex;
  justify-content: space-between; }

.header__left, .header__right {
  display: flex;
  gap: 2.5vw; }

.header__logo-box {
  width: 9vw;
  min-width: 12rem;
  align-self: center; }

.header__logo {
  display: block;
  width: 100%;
  font-size: clamp(1.3rem, 1vw, 1.5rem); }

.header__nav-trigger {
  display: inline-flex;
  align-items: center;
  color: #071b34;
  cursor: pointer; }

.header__nav-trigger .-close {
  display: none; }

.header__nav-trigger:hover .header__hamburger-line::before, .header__nav-trigger:hover .header__hamburger-line::after {
  width: 100%; }

.header__hamburger {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 5.4rem;
  height: 5.4rem;
  border: 1px solid #d9d9d9;
  background-color: rgba(239, 239, 239, 0.3);
  backdrop-filter: blur(25px);
  padding: 1.5rem;
  border-radius: 3px; }

.header__hamburger-line {
  position: relative;
  width: 100%;
  height: 2px;
  border-radius: 10px;
  background-color: currentColor; }

.header__hamburger-line::before, .header__hamburger-line::after {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  border-radius: 10px;
  background-color: #071b34; }

.header__hamburger-line::before {
  top: -8px;
  left: 0;
  width: 100%; }

.header__hamburger-line::after {
  bottom: -8px;
  right: 0; }

.header__hamburger-txt {
  padding: 1.5rem 0.7vw;
  font-size: clamp(1.4rem, 1vw, 1.6rem);
  font-family: "Prosto One", Arial, sans-serif; }

.header__call {
  display: flex;
  align-items: center;
  gap: 1.5vw;
  padding: 8px 1.5rem;
  background-color: rgba(239, 239, 239, 0.3);
  backdrop-filter: blur(25px);
  border-radius: 3px;
  border: 1px solid #d9d9d9;
  font-family: "Prosto One", Arial, sans-serif;
  line-height: 1em;
  font-size: clamp(1.4rem, 1vw, 1.6rem); }

.header__call-txt {
  display: flex;
  flex-direction: column; }

.header__call-num {
  padding-left: 1rem;
  border-left: 1px solid #d9d9d9;
  padding-top: 1rem;
  padding-bottom: 1rem; }

.header__call-subtxt {
  font-size: clamp(1.1rem, 1vw, 1.3rem);
  font-family: "Montserrat", Arial, sans-serif; }

.header__cta {
  position: fixed;
  display: flex;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  z-index: 9; }

.header__cta .btn {
  flex: 1;
  border-radius: 0;
  border: none; }



@media only screen and (max-width: 640px) {
    .header {
      padding-top: 1rem;
      padding-bottom: 1rem; }
      .header__hamburger, .header__call {
        transform: scale(0.8); } }

@media only screen and (min-width: 1025px) {
    .header {
      padding-top: 2.5rem;
      padding-bottom: 2.5rem; } }




/**********************************************************
 *
 **********************************************************/
/**********************************************************
 * 
 **********************************************************/

.underline {
  align-items: center;
  display: inline-flex;
  gap: 1rem;
  position: relative; }

.underline::after {
  background-color: currentColor;
  content: '';
  bottom: -3px;
  height: 1px;
  left: 0;
  position: absolute;
  transform: scaleX(0);
  transform-origin: center bottom;
  transition: transform 0.2s;
  width: 100%; }

.underline:hover::after {
  transform: scaleX(1); }

.underline--shown::after {
  transform: scaleX(1); }

.underline--shown:hover::after {
  transform: scaleX(0.8); }
/**********************************************************
 * 
 **********************************************************/
@keyframes scrolling {
  0% {
    transform: translateX(0); }

  100% {
    transform: translateX(-100%); } }

.scroll {
  align-items: center;
  display: flex;
  overflow: hidden; }

.scroll--slides {
  align-items: flex-start; }

.scroll--slides .scroll__group {
  align-items: flex-start;
  gap: 0rem;
  animation: scrolling 90s linear infinite; }

.scroll > * {
  flex: 0 0 100%; }

.scroll:hover .scroll__group {
  animation-play-state: paused; }

.scroll__group {
  align-items: center;
  animation: scrolling 30s linear infinite;
  display: flex;
  gap: 2rem;
  will-change: transform;
  padding-right: 2rem; }

.scroll__item {
  align-items: center;
  display: flex;
  width: 100%; }

.scroll__img {
  display: block;
  max-width: none;
  height: 5rem; }




/**********************************************************
 * 
 **********************************************************/
.tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2rem; }

.tabs--news {
  font-family: "Prosto One", Arial, sans-serif;
  font-size: clamp(1.8rem, 1.5vw, 2rem);
  text-transform: uppercase; }

.tabs--news .tabs__item.-current .tabs__link, .tabs--news .tabs__item:hover .tabs__link {
  color: #a88861; }

.tabs--pill .tabs__link {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 2rem;
  border: 1px solid currentColor;
  padding: 1rem 2rem; }
  .tabs--pill .tabs__link:hover {
    color: black; }

.tabs--pill .tabs__item.-current .tabs__link {
  background-color: #000;
  color: #fff; }

.tabs--underline .tabs__item.-current .tabs__link {
  color: black; }
  .tabs--underline .tabs__item.-current .tabs__link::after {
    opacity: 1;
    visibility: visible; }

.tabs--underline .tabs__link {
  display: block;
  position: relative;
  padding-bottom: 1rem; }
  .tabs--underline .tabs__link::after {
    background: gray no-repeat left bottom;
    bottom: 0;
    content: '';
    height: 0.5rem;
    left: 0;
    opacity: 0;
    position: absolute;
    visibility: hidden;
    width: 100%; }
  .tabs--underline .tabs__link:hover {
    color: black; }

.tabs__item {
  margin-bottom: 0.8rem;
  margin-right: 1.5rem; }

.tabs__link {
  color: gray;
  font-weight: bold; }

@media only screen and (min-width: 481px) {
    .tabs {
      margin-bottom: 3rem; }
      .tabs__item {
        margin-bottom: 1rem;
        margin-right: 2rem; }
      .tabs__link {
        padding-bottom: 1rem; } }

@media only screen and (min-width: 1281px) {
    .tabs__item {
      margin-right: 3rem; }
      .tabs__link {
        font-size: 2rem; } }

.video {
  background-color: black;
  display: block;
  overflow: hidden;
  position: relative; }

.video:hover .video__play {
  transform: translate(-50%, -50%) scale(0.95); }

.video:hover .video__preview {
  opacity: 0.6;
  transform: scale(1.05); }

.video__play {
  transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  align-items: center;
  border-radius: 50%;
  display: inline-flex;
  height: 76px;
  justify-content: center;
  width: 76px;
  background-color: white;
  background-size: auto;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2; }

.video__preview {
  transition: all 500ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  display: block;
  transform: scale(1.000001);
  width: 100%; }

.gallery-container {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
  height: 20rem; }

.gallery {
  display: flex;
  height: 100%; }

.gallery__img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; }

.gallery__item {
  width: 60px;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: all 0.4s ease;
  cursor: pointer; }

.gallery__item--active {
  width: 230px; }

.nav {
  transition: all 500ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: 10rem;
  background-color: #fff;
  z-index: 9;
  transform: translateY(-100%);
  overflow-y: scroll !important; }

.nav.-active {
  transform: translateY(0); }

.nav__main {
  padding-top: 5rem;
  padding-bottom: 11vh;
  color: #071b34; }

.nav__label {
  margin-bottom: 3rem;
  font-size: clamp(2rem, 2vw, 3rem);
  opacity: 0.6;
  font-family: "Prosto One", Arial, sans-serif; }

.nav__list {
  display: flex;
  flex-direction: column;
  gap: 2.5rem; }

.nav__link {
  font-size: clamp(2rem, 1.5vw, 2.2rem);
  font-family: "Prosto One", Arial, sans-serif; }

.nav__form {
  background: linear-gradient(131.72deg, #f8e2ae 12.6%, #a88861 46.32%, #e6ce9d 84.43%);
  color: #fff;
  padding-top: 2rem;
  padding-bottom: 5rem; }

.nav__form-wrap {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
  gap: 2rem 5vw; }

.contacts {
  display: flex;
  flex-direction: column;
  gap: 2.5vw; }

.contacts__item {
  max-width: 24rem; }

.contacts__label {
  margin-bottom: 1rem; }

.contacts__text {
  font-size: clamp(1.8rem, 1.5vw, 2rem);
  font-weight: 700;
  line-height: 1em; }

.contacts__socials {
  display: flex;
  gap: 1.3vw;
  align-items: center; }

.contacts__socials-icon {
  width: 2.4rem; }

.facts .slick-list {
  margin-right: -2rem; }

.facts__slide {
  display: inline-flex !important;
  flex-direction: column;
  gap: 1.5rem;
  margin-right: 2rem; }

.facts__img-box {
  position: relative;
  width: 100%;
  height: 40rem;
  border-radius: 3px;
  overflow: hidden; }

.facts__img-box:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2); }

.facts__img {
  display: block;
  object-fit: cover;
  width: 100%;
  height: 100%; }

.facts__content {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.3rem 1.5vw;
  color: #fff;
  font-family: "Prosto One", Arial, sans-serif;
  z-index: 1; }

.facts__stats {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.302);
  padding: 1.5rem 1vw;
  gap: 1.2rem;
  font-size: clamp(1.3rem, 1.5vw, 1.5rem);
  border-radius: 3px;
  backdrop-filter: blur(25px); }

.facts__title {
  font-size: clamp(1.8rem, 1.5vw, 2rem); }

.calculator {
  display: flex;
  flex-direction: column;
  /* 1024 down */ }

.calculator__wrap {
  margin-top: -14vw; }

.calculator__main {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  padding: 3rem 2vw;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(7, 27, 52, 0.2);
  backdrop-filter: blur(20px);
  border-radius: 3px; }

.calculator__head {
  display: flex;
  justify-content: space-between; }

.calculator__label {
  font-size: 1.5rem;
  font-weight: 600; }

.calculator__current-value {
  font-size: clamp(2rem, 3vw, 3.8rem);
  font-family: "Prosto One", Arial, sans-serif; }

.calculator__values {
  display: flex;
  justify-content: space-between;
  font-size: 1.5rem;
  opacity: 0.6; }

.calculator__item {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; }

.calculator__result {
  display: flex;
  gap: 1rem; }

.calculator__list {
  display: flex;
  flex-direction: column;
  max-width: 42rem;
  gap: 1.5rem; }

.calculator__list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: clamp(1.3rem, 1.5vw, 1.5rem);
  gap: 2rem; }

.calculator__list-value {
  display: flex;
  align-items: center;
  gap: 0.5rem; }

.calculator__list-hl {
  width: 100%;
  height: 1px;
  background-color: #f7f7f7; }

.calculator .irs {
  height: 6rem; }

.calculator .irs-min, .calculator .irs-max, .calculator .irs-single {
  display: none; }

.calculator .irs-handle {
  width: 6rem;
  height: 6rem;
  background-color: #fff;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  cursor: pointer; }

.calculator .irs-handle:hover {
  background-color: #a88861; }

.calculator .irs-handle:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2.4rem;
  height: 1.6rem;
  background: url("../image/icon/calculator.svg") no-repeat center center;
  background-size: contain;
  transform: translate(-50%, -50%); }

.calculator .irs-handle i {
  background-color: transparent !important; }

.calculator .irs-line {
  height: 1px;
  background-color: #071b34;
  top: 3rem; }

.calculator .irs-bar--single {
  display: none; }

@media only screen and (max-width: 1024px) {
    .calculator .irs-handle {
      transform: translate(-50%, -50%) scale(0.75); }
      .calculator__main {
        padding: 2rem 3rem; } }

.hero-img {
  position: relative;
  width: 100%;
  /* 1024 down */ }

.hero-img__box {
  position: relative;
  width: 100%;
  border-radius: 3px;
  overflow: hidden; }

.hero-img__box::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2); }

.hero-img__content {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  left: 0;
  bottom: 0;
  padding: 2.5vw 6vw;
  color: #fff; }

.hero-img__title {
  line-height: 1.2em; }

@media only screen and (max-width: 1024px) {
    .hero-img__content {
      padding: 3rem 2rem; } }

.box {
  padding: 2.5vw;
  border: 1px solid #d9d9d9;
  border-radius: 1.5rem;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px); }

.box__icon {
  width: 5.5rem; }

.steps {
  display: flex;
  /* 1280 down */ }

.steps--dark .steps__item {
  background-color: #f7f7f7; }
  .steps--dark .steps__item--bg {
    background-color: #071b34;
    color: #fff; }

.steps--dark .steps__icon-box {
  background-color: #071b34; }

.steps__item {
  position: relative;
  display: flex !important;
  flex-direction: column;
  gap: 12rem;
  flex: 1 1 15%;
  min-height: 29rem;
  border-radius: 1.5rem;
  overflow: hidden;
  padding: 3rem 1.5rem 5rem;
  background-color: #f7f7f7; }

.steps__item--img {
  color: #fff; }

.steps__item--bg {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  background-color: #a88861;
  color: #fff; }
  .steps__item--bg:hover {
    transform: scale(1.02); }
  .steps__item--bg .steps__icon-box {
    background-color: #fff; }

.steps__img-box {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden; }

.steps__img-box:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(7, 27, 52, 0.5); }

.steps__img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover; }

.steps__icon-box {
  position: absolute;
  width: 50%;
  aspect-ratio: 1 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  top: -1.4rem;
  right: -1rem;
  background-color: #a88861;
  border-radius: 50%; }

.steps__icon {
  width: 40%; }

.steps__step {
  font-size: 1.2rem;
  text-transform: uppercase;
  opacity: 0.5; }

.steps__content {
  display: flex;
  flex-direction: column;
  gap: 1rem; }

.steps__time {
  font-size: 1.5rem;
  opacity: 0.5; }

.steps__title {
  font-size: 2rem;
  font-family: clamp(1.6rem, 1.5vw, 2rem); }

.steps__header, .steps__content {
  position: relative; }

.steps__arrow {
  width: 1.5vw; }

@media only screen and (max-width: 1280px) {
    .steps .slick-list {
      margin-right: -2rem; }
      .steps .slick-track {
        display: flex !important; }
      .steps .slick-slide {
        height: inherit !important; }
      .steps__arrow {
        display: none !important; }
      .steps__item {
        margin-right: 2rem;
        flex: none; } }

.audience-panel {
  position: absolute;
  left: 0;
  bottom: 0;
  /* 1024 down */ }

.audience-panel__wrap {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 44rem; }

.audience-panel__tabs {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; }

.audience-panel__tab.-current .audience-panel__tab-btn, .audience-panel__tab:hover .audience-panel__tab-btn {
  background-color: rgba(255, 255, 255, 0.8); }

.audience-panel__tab-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(60px);
  border: 1px solid rgba(255, 255, 255, 0.3); }

.audience-panel__vl {
  align-self: center;
  width: 1px;
  height: 4rem;
  background-color: #a88861; }

.audience-panel__content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(7, 27, 52, 0.3);
  backdrop-filter: blur(20px); }

.audience-panel__carousel {
  max-width: 33rem; }

@media only screen and (max-width: 1024px) {
    .audience-panel {
      position: relative;
      left: unset;
      bottom: unset;
      margin-top: -25rem; } }

.partners-accordion {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; }

.partners-accordion__content {
  display: none;
  margin-top: 2rem; }

.partners-accordion__header {
  cursor: pointer; }

.partners-accordion__icon {
  display: none;
  width: 2.4rem; }

.partners-accordion__item {
  padding: 1.5rem 2rem; }

.partners-accordion__item.-active {
  background-color: #f7f7f7; }
  .partners-accordion__item.-active .partners-accordion__icon {
    display: inline-block; }

.partners-accordion__logo {
  position: absolute;
  width: 11vw;
  min-width: 12rem;
  right: 0;
  bottom: 0;
  transform: translate(50%, 50%); }

.partners-accordion__img-box {
  position: relative;
  width: 100%;
  max-width: 60rem; }

.partners-accordion__img {
  display: block;
  width: 100%; }

.chart {
  gap: 3rem;
  background: linear-gradient(180deg, #f8f8f8 0%, rgba(255, 255, 255, 0.2) 115.66%);
  border: 1px solid #a88861;
  backdrop-filter: blur(10px);
  border-radius: 3px;
  padding-bottom: 6rem;
  /* 640 down */ }

.chart--inner .chart__tab.-current {
  background-color: #f7f7f7; }
  .chart--inner .chart__tab.-current::before {
    background-color: #f7f7f7; }

.chart__tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem; }

.chart__table {
  display: none;
  flex-direction: column; }

.chart__table.-visible {
  display: flex; }

.chart__row {
  display: flex; }

.chart__row:first-child .chart__cell--white {
  border-top: 1px solid #a88861;
  border-radius: 3px 3px 0 0; }

.chart__row:last-child .chart__cell--white {
  border-bottom: 1px solid #a88861;
  border-radius: 0 0 3px 3px; }

.chart__cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 3rem;
  padding-bottom: 3rem;
  width: 23%;
  border-bottom: 1px solid #ededed;
  padding-left: 1rem;
  padding-right: 1rem; }

.chart__cell--left {
  width: 30%;
  justify-content: flex-start;
  padding-right: 2rem;
  font-size: clamp(1.2rem, 1.5vw, 1.6rem);
  padding-left: 0; }

.chart__cell--white {
  background-color: #fff;
  border-left: 1px solid #a88861;
  border-right: 1px solid #a88861;
  border-bottom: none; }
  .chart__cell--white .chart__title {
    color: #071b34; }

.chart__title {
  font-family: "Prosto One", Arial, sans-serif;
  color: #939393;
  font-size: clamp(1.4rem, 1.5vw, 1.7rem);
  text-align: center; }

.chart__title span {
  color: #a88861; }

.chart__tab {
  position: relative;
  cursor: pointer; }

.chart__tab.-current {
  background-color: #fff;
  border: 1px solid #a88861;
  border-top: none;
  border-radius: 0 0 3px 3px; }
  .chart__tab.-current::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #fff; }
  .chart__tab.-current .chart__link {
    color: #a88861;
    text-decoration: underline; }

.chart__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2.4rem 2.5vw;
  font-family: "Prosto One", Arial, sans-serif;
  color: #939393; }

@media only screen and (max-width: 640px) {
    .chart__title {
      transform: rotate(-90deg); } }

.related {
  position: relative;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  border: 1px solid #f7f7f7;
  backdrop-filter: blur(10px);
  /* 1024 down */ }

.related__btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%); }

.related__btn--prev {
  left: 1.5rem; }

.related__btn--next {
  right: 1.5rem; }

.related__slide {
  display: flex !important;
  flex-direction: column;
  gap: 2.5rem; }

.related__header, .related__content {
  display: flex;
  flex-direction: column;
  gap: 2rem; }

.related__paging {
  font-size: clamp(1.8rem, 1.5vw, 2rem);
  color: #939393; }

@media only screen and (max-width: 1024px) {
    .related {
      padding-left: 0;
      padding-right: 0;
      border: none; }
      .related .g-wrap {
        padding-left: 0;
        padding-right: 0; } }

.team-card {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 42rem;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(60px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin-left: 2rem;
  margin-right: 2rem;
  padding: 3rem 2.5rem 3rem 6rem;
  z-index: 1;
  /* 1024 down */
  /* 480 down */
  /* 1024 up */
  /* 1280 up */ }

.team-card--left .team-card__signature {
  right: 2rem;
  bottom: -2.5rem; }

.team-card--right .team-card__signature {
  right: 0;
  bottom: -6rem; }

.team-card__icon, .team-card__signature {
  position: absolute; }

.team-card__icon {
  width: 2.4rem;
  top: 2rem;
  left: 2rem; }

.team-card__signature {
  width: 50%; }

.team-card__name {
  font-size: clamp(2.2rem, 2vw, 2.8rem);
  font-family: "Prosto One", Arial, sans-serif; }

@media only screen and (max-width: 1024px) {
    .team-card--left {
      margin-bottom: -3rem; }
      .team-card--right {
        margin-top: -3rem;
        margin-bottom: 10rem; } }

@media only screen and (max-width: 480px) {
    .team-card {
      max-width: 90vw; } }

@media only screen and (min-width: 1025px) {
    .team-card {
      position: absolute;
      width: 22vw;
      min-width: 25rem;
      padding: 4.7rem 3vw; }
      .team-card--left {
        top: 10%;
        left: 2rem; }
      .team-card--right {
        top: 30%;
        right: 2rem; } }

@media only screen and (min-width: 1281px) {
    .team-card {
      min-width: 35rem; }
      .team-card--left {
        top: 50%;
        left: 7%; }
      .team-card--right {
        top: 60%;
        right: 7%; } }

.frame-box {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem 3rem;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  border: 1px solid #d9d9d9;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(60px); }

.article {
  display: flex !important;
  flex-direction: column;
  gap: 2rem; }

.article--lg .article__title {
  font-size: clamp(3rem, 2vw, 4.8rem);
  line-height: 1em; }

.article--lg .article__content {
  max-width: 120rem; }

.article:hover .article__img {
  transform: scale(1.05); }

.article:hover .btn {
  filter: invert(0.5); }

.article__img-box {
  width: 100%;
  aspect-ratio: 2 !important;
  overflow: hidden; }

.article__img {
  transition: all 500ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  display: block;
  width: 100%;
  object-fit: cover; }

.article__content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding-right: 2rem; }

.article__date {
  font-size: 1.4rem;
  opacity: 0.6; }

.article__title {
  font-family: "Prosto One", Arial, sans-serif; }

.article__title--lg {
  font-size: clamp(3rem, 2vw, 4.8rem);
  line-height: 1em; }

.article__share {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  font-family: "Prosto One", Arial, sans-serif; }

.article__share-label, .article__share-list {
  display: flex;
  align-items: center;
  gap: 1rem; }

.article-carousel .slick-list {
  margin-right: -2rem; }

.article-carousel .article {
  margin-right: 2rem; }

.accordion {
  position: relative;
  width: 100%;
  list-style: none;
  z-index: 3;
  /* 480 up */
  /* 640 up */
  /* 1024 up */
  /* 1280 up */
  /* 1600 up */ }

.accordion--team .-active-control h2 {
  color: #071b34; }

.accordion--team .accordion__control {
  flex-direction: column;
  gap: 0; }
  .accordion--team .accordion__control::after {
    display: none; }

.accordion--team .accordion__item {
  border-bottom: none; }

.accordion--team .accordion__panel {
  color: #071b34; }

.accordion__box {
  position: relative;
  padding: 6rem 7vw;
  background-color: #fff;
  border-radius: 2rem; }

.accordion__box:after {
  content: '';
  position: absolute;
  height: 120%;
  aspect-ratio: 1;
  right: -5%;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: rgba(168, 136, 97, 0.15);
  filter: blur(50px);
  z-index: -1; }

.accordion__item {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-bottom: 1px solid rgba(7, 27, 52, 0.2); }

.accordion__control {
  transition: all 250ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  position: relative;
  display: flex;
  gap: 2.5rem;
  padding: 2.3rem 2.5rem 2.3rem 0;
  font-size: clamp(1.4rem, 1.5vw, 1.6rem);
  color: #071b34;
  text-align: left;
  border: none;
  font-weight: 400 !important;
  cursor: pointer; }

.accordion__control::after {
  transition: all 100ms cubic-bezier(0.75, 0, 0.5, 1) 0ms;
  transition-timing-function: ease-in-out;
  content: "";
  position: absolute;
  width: 17px;
  height: 17px;
  top: 50%;
  right: 0;
  transform: translateY(-50%) rotate(0deg);
  background-image: url(../image/icon/accordion-arrow.svg);
  background-repeat: no-repeat;
  background-position: center; }

.accordion__img-box {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%; }

.accordion__img {
  display: block;
  height: 100%; }

.accordion .-active-control {
  width: 100%;
  color: #a88861;
  font-weight: bold !important; }

.accordion .-active-control::after {
  transform: translateY(-50%) rotate(180deg); }

.accordion__panel {
  display: none;
  width: 100%;
  padding: 2rem 0;
  line-height: 1.5em;
  font-size: clamp(2rem, 1.5vw, 2.4rem);
  color: #a88861; }

.accordion__container {
  width: 100%;
  display: flex;
  gap: 4rem 8rem;
  padding-left: 10rem; }

.accordion__col {
  max-width: 45rem; }

.accordion__list {
  margin-left: 2rem; }

.accordion__list-item {
  list-style: disc; }

.accordion__team-box {
  width: 100%;
  max-width: 40rem;
  border-radius: 1.5rem;
  overflow: hidden;
  position: relative; }

.accordion__logo-box {
  position: absolute;
  top: -1.6rem;
  right: -1.2rem;
  width: 5vw;
  aspect-ratio: 1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff; }

.accordion__logo {
  width: 50%; }











.review {
  position: relative; }

.review__content {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  padding: 2.5rem 2rem;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(7, 27, 52, 0.2);
  backdrop-filter: blur(20px);
  border-radius: 3px;
  max-width: 53rem; }

.review__img-box {
  width: 100%;
  height: 55rem;
  border-radius: 3px;
  overflow: hidden; }

.review__img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover; }

.review-carousel__btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%); }

.review-carousel__btn--prev {
  left: 0; }

.review-carousel__btn--next {
  right: 0; }

.review-carousel .slick-list {
  margin-right: -2rem; }

.review-carousel .review {
  margin-right: 2rem; }

.brave {
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  backdrop-filter: blur(20px);
  border: 1px solid #f7f7f7;
  padding: 5rem 2rem; }

.brave__text {
  display: none; }

.brave__container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 0;
  margin-top: 5rem; }

.brave__circle {
  position: relative;
  width: 12rem;
  height: 12rem;
  min-width: 12rem; }

.brave__circle--primary {
  fill: #a88861; }

.brave__logo {
  width: 10rem; }

.brave__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 38%; }

.brave__middle {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: center; }

.brave__line {
  position: relative;
  width: 1px;
  height: 10rem;
  background-color: #d9d9d9; }

@media only screen and (min-width: 768px) {
    .brave__container {
      flex-direction: row;
      margin-top: 0;
      gap: 3rem; }
      .brave__text {
        display: block; }
      .brave__circle {
        width: 6vw;
        height: 6vw;
        min-width: 6vw; }
      .brave__logo {
        width: 5vw; }
      .brave__line {
        width: 100%;
        height: 0.1rem; }
        .brave__line:after {
          content: '';
          position: absolute;
          width: 5px;
          height: 5px;
          background-color: #d9d9d9;
          border-radius: 50%;
          top: 50%;
          transform: translateY(-50%); }
        .brave__line--left:after {
          left: 0; }
        .brave__line--right:after {
          right: 0; } }

.services {
  display: flex;
  flex-direction: column;
  gap: 5rem;
  /* 640 down */ }

.services--inner .services__item {
  align-items: flex-end;
  padding: 0;
  gap: 2rem; }
  .services--inner .services__item:nth-child(even) {
    flex-direction: row; }
  .services--inner .services__item:nth-child(odd) {
    flex-direction: row-reverse; }

.services--inner .services__col {
  width: 40%;
  max-width: none; }

.services--inner .services__img-box {
  width: 100%;
  max-width: none; }

.services__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
  padding: 5rem 10vw;
  background-color: #fff;
  border-radius: 3px; }

.services__item:nth-child(even) {
  flex-direction: row-reverse; }

.services__col {
  display: flex;
  gap: 6vw;
  flex-direction: column;
  max-width: 56rem; }

.services__content {
  display: flex;
  flex-direction: column;
  gap: 2rem; }

.services__img-box {
  width: 100%;
  max-width: 44rem; }

.services__img {
  display: block;
  width: 100%; }

@media only screen and (max-width: 640px) {
    .services__item {
      flex-direction: column; }
      .services--inner .services__col {
        width: 100%; } }

.near {
  width: 100%;
  color: #fff;
  /* 1024 down */ }

.near__accent {
  position: absolute;
  width: 50%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center; }

.near__accent:before, .near__accent::after {
  content: '';
  position: absolute;
  width: 50%;
  height: 1px;
  background-color: rgba(255, 255, 255, 1);
  right: 0; }

.near__accent:before {
  top: 20%;
  transform: rotate(-45deg); }

.near__accent::after {
  bottom: 20%;
  transform: rotate(45deg); }

.near__accent-icon {
  width: 6rem; }

.near__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden; }

.near__bg:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(7, 27, 52, 0.2);
  z-index: 1; }

.near__bg .slick-list, .near__bg .slick-track {
  height: 100%; }

.near__bg-img {
  position: relative;
  width: 100%;
  height: 100%; }

.near__img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; }

.near__content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5rem;
  width: 100%;
  height: 100%;
  padding-top: 5rem;
  padding-bottom: 2.5rem;
  max-width: 40rem;
  z-index: 2; }

.near__center {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  max-width: 40rem;
  height: 100%;
  z-index: 2; }

.near .g-row {
  width: 100%;
  height: 100%;
  align-items: center; }

.near .col-1 {
  height: 100%; }

.near__vertical {
  height: 20rem;
  overflow: hidden; }

.near__vertical .slick-slide {
  opacity: 0.6;
  margin-bottom: 2.5rem !important; }

.near__vertical .slick-center {
  opacity: 1; }

@media only screen and (max-width: 1024px) {
    .near {
      padding: 3rem 2rem;
      padding-bottom: 0; }
      .near__center {
        max-width: 30rem; }
      .near__content {
        gap: 2rem; }
      .near__accent {
        display: none; } }
