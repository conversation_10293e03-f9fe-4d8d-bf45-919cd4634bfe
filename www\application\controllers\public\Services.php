<?php

require_once __DIR__ . "/../Public_base.php";

class Services extends Public_base {

	public function index() {
		$node = $this->node();

		$benefits = $this->get_nodes("comp_clients", "comp_partners");
		$this->data->nodes->get_list_loc($this->lng->id, array("parent_ids" => array_prop($benefits, "id"), "kinds" => "row"));
		$this->view->set("benefits", $benefits);
		
		$filter = array(
			"parent_id" => nodeid("services"), 
			"kinds" => node::KIND_SERVICE,
		);
		$list = $this->data->nodes->get_list_loc($this->lng->id, $filter);

		$this->view->set("list", $list);

		$this->render_node("public/services-list", $node);
	}

	public function details($slug) {
		$node = $this->node();
		
		$filter = array(
				"parent_id" => $node->id,
				"slug" => urldecode($slug),
		);
		$node = $this->data->nodes->get_first_loc($this->lng->id, $filter);

		if (!$node) {
			$this->_404();
			return;
		}

		$blocks = $this->data->nodes->get_list_loc($this->lng->id, array(
			"parent_id" => $node->id,
			"kinds" => "block",
		));

		$feat = __::find($blocks, function($o) { return $o->slug == "feat"; });
		$params = __::find($blocks, function($o) { return $o->slug == "params"; });
		$types = __::find($blocks, function($o) { return $o->slug == "types"; });
		$client = __::find($blocks, function($o) { return $o->slug == "client"; });
		$faq = __::find($blocks, function($o) { return $o->slug == "faq"; });

		$this->view->set("feat", $feat);
		$this->view->set("params", $params);
		$this->view->set("types", $types);
		$this->view->set("client", $client);
		$this->view->set("faq", $faq);

		$this->view->set("feat_list", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => $feat->id, "kinds" => "feat")));
		$this->view->set("params_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => $params->id, "kinds" => "feat")));
		$this->view->set("textblocks", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => $types->id, "kinds" => "textblock")));
		$this->view->set("client_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => $client->id, "kinds" => "feat")));
		$this->view->set("questions", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => $faq->id, "kinds" => "question")));

		$benefits = $this->get_nodes("comp_clients", "comp_partners");
		$this->data->nodes->get_list_loc($this->lng->id, array("parent_ids" => array_prop($benefits, "id"), "kinds" => "row"));
		$this->view->set("benefits", $benefits);
		
		$this->render_node("public/services-details", $node);
	}
}
