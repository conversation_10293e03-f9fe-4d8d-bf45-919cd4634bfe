<?php

require_once "base/Restricted.php";

class Util extends Restricted {

	public function slug() {
		if ($this->input->is_ajax_request()) {
			$input = $this->input->get("input");
			$lang_id = $this->input->get("lang_id");
			$lang = $this->data->languages->id($lang_id);
			$res = array("status"=>false);
			if ($lang) {
				$res["status"] = true;
				$res["slug"] = slug_auto($input, $lang->code);
			}
			$this->json_response($res);
		}
	}

	public function upload() {
		$gc_prob = conf("filemanager", "tmp_gc_prob");
		if (lcg_value() < $gc_prob) {
			$this->upload_gc();
		}

		$res = array("file"=>null);

		$file = _files(array("files", 0));
		if (Validator::test_file_upload($file)) {
			$filename = $this->filemanager->upload($file, "tmp");

			$file_data = array(
					"name" => $file["name"],
					"size" => $file["size"],
					"type" => $file["type"],
			);

			$current = _session(conf("fileupload_key"), array());
			$current[$filename] = $file_data;
			set_session(conf("fileupload_key"), $current);

			$res["file"] = $filename;

		}
		$this->json_response($res);
	}


	private function upload_gc() {
		$hours = conf("filemanager", "tmp_files_lifespan");

		$this->filemanager->garbage_collector("tmp", $hours);
	}



	public function upload_tinymce_edit() {
		$res = null;
		$file = _files_arr("file");
		if ($file && Validator::test_file_upload($file)) {
			$ext = Filemanager::get_extension($file["name"]);
			$image = Image::from_file($file["tmp_name"], $ext);
			$new_name = Filemanager::get_name(urldecode($file["name"])) . ".jpg";

			my_mkdir($this->filemanager->get_dir("tinymce_edit", true));

			$abs = $this->filemanager->get_file($new_name, "tinymce_edit", true);
			$res = $this->filemanager->get_file($new_name, "tinymce_edit", false);
			$image->save($abs);
			$res = array("location"=>$res);
		}
		$this->json_response($res);
	}
	
	public function session_touch() {
		if ($this->input->is_ajax_request()) {
			$this->json_response($this->usession->time_remain());
		}
	}
}
