<?php
/* Smarty version 3.1.30, created on 2025-07-04 15:12:28
  from "W:\work\dvm_finance\www\application\views\public\faq.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_6867c52c94d400_37624507',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'bd2eaf66b586d95d192c11c32c59bdf8bb5ba8cb' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\faq.tpl',
      1 => 1751631147,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
  ),
),false)) {
function content_6867c52c94d400_37624507 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('wrapper_css', "g-bg-light-gray");
$_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_3342030446867c52c94ce14_34667112', "content");
?>

<?php $_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_3342030446867c52c94ce14_34667112 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
		</div>
		<div class="g-wrap g-wrap--xxl">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['list']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
			<div class="g-padded--xs">
				<div class="g-bg g-radius-3">
					<div class="g-wrap g-wrap--xl g-padded--sm">
						<div class="g-row g-gap-v-1 g-gap-v-xm-0">
							<div class="col-1 col-xm-1-3">
								<h2 class="g-title g-title--md">
									<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>

								</h2>
								<div class="g-editor">
									<p>
										<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

									</p>
								</div>
							</div>
							<div class="col-1 col-xm-1-12"></div>
							<div class="col-1 col-xm-1-2">
								<ul class="accordion accordion--horizontal fade-up">
									<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['i']->value->children(), 'j');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['j']->value) {
?>
									<li class="accordion__item">
										<button class="accordion__control">
											<p><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['j']->value->title, ENT_QUOTES, 'UTF-8');?>
</p>
										</button>
										<div class="accordion__panel">
											<div class="g-editor">
												<p>
													<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['j']->value->text);?>

												</p>
											</div>
										</div>
									</li>
									<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

		</div>
	</section>
<?php
}
}
/* {/block "content"} */
}
