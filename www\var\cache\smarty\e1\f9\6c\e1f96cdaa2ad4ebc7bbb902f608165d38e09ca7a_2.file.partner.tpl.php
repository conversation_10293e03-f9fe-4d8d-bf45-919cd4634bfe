<?php
/* Smarty version 3.1.30, created on 2025-07-05 15:33:19
  from "W:\work\dvm_finance\www\application\views\public\partner.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_68691b8f39a693_40331335',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'e1f96cdaa2ad4ebc7bbb902f608165d38e09ca7a' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\partner.tpl',
      1 => 1751718793,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/feat-1.tpl' => 1,
    'file:./_partial/feat-5.tpl' => 1,
    'file:./_partial/feat-2.tpl' => 1,
    'file:./_partial/benefits.tpl' => 1,
    'file:./_partial/faq.tpl' => 1,
    'file:./_partial/reviews.tpl' => 1,
    'file:./_partial/contacts.tpl' => 1,
  ),
),false)) {
function content_68691b8f39a693_40331335 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_63540080168691b8f39a0b4_59302530', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_63540080168691b8f39a0b4_59302530 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					<?php echo $_smarty_tpl->tpl_vars['node']->value->subtitle;?>

				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node']->value->text);?>

					</p>
				</div>
			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-1.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node']->value,'show_link'=>false,'show_title'=>false), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-5.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_partner_become']->value), 0, false);
?>

	<section class="g-padded--sm">
		<div class="g-wrap">
			<div class="g-head">
				<h2 class="g-title g-title--md">
					<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node_partner_exp']->value->text);?>

				</h2>
			</div>
			<div class="g-row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['exp_feat']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$__foreach_i_0_saved = $_smarty_tpl->tpl_vars['i'];
?>
					<div class="col-1 col-md-1-2 col-xm-1-3">
						<div class="box">
							<img class="box__icon" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image2,'icon2'), ENT_QUOTES, 'UTF-8');?>
" alt="">
							<div class="box__content">
								<p class="g-bold g-mb-10 g-opacity-60">
									<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>

								</p>
								<div class="g-editor g-opacity-60">
									<p>
										<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

									</p>
								</div>
							</div>
						</div>
					</div>
					<?php if ($_smarty_tpl->tpl_vars['i']->index && ($_smarty_tpl->tpl_vars['i']->index+1)%2 == 0) {?>
						<div class="col-1 col-md-1-2 col-xm-1-3"></div>
						<div class="col-1 col-md-1-2 col-xm-1-3"></div>
					<?php }?>
				<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_0_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-2.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_partner_process']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/benefits.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/faq.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_partner_faq']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/reviews.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_partner_reviews']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/contacts.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php
}
}
/* {/block "content"} */
}
