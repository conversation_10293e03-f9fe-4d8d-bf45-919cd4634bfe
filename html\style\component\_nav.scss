.nav {
    @include transition-md;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding-top: 10rem;
    background-color: $color-white;
    z-index: 9;
    transform: translateY(-100%);
    overflow-y: scroll !important;
    &.-active {
        transform: translateY(0);
    }

    &__main {
        padding-top: 5rem;
        padding-bottom: 11vh;
        color: $color-black;
    }

    &__label {
        margin-bottom: 3rem;
        font-size: clamp($font-body-lg, 2vw, $font-title-sm); 
        opacity: .6;
        font-family: $font-title;
    }

    &__list {
        display: flex;
        flex-direction: column;
        gap: 2.5rem;
    }

    &__link {
        font-size: clamp(2rem, 1.5vw, 2.2rem);
        font-family: $font-title;
    }

    &__form {
        background: linear-gradient(131.72deg, #F8E2AE 12.6%, #A88861 46.32%, #E6CE9D 84.43%);
        color: $color-white;
        padding-top: 2rem;
        padding-bottom: 5rem;
    }

    &__form-wrap {
        display: flex;
        align-items: flex-end;
        flex-wrap: wrap;
        gap: 2rem 5vw;
    }
}