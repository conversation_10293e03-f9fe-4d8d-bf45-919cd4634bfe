<?php

require_once "base/Post_edit.php";

class Inquiries extends Post_edit {

	public function __construct() {
		parent::__construct();
	}

	protected function opts() {
		return array(
				"add" => false,
				"edit" => false,
				"delete" => false,
		);
	}

	protected function get_count($filter) {
		return $this->data->inquiries->get_count($filter);
	}
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->inquiries->get_list($filter, $order, $dir, $offset, $count);
	}
	protected function get_object($id) {
		return $this->data->inquiries->get_first(array("id" => $id));
	}
	protected function get_object_title(ci_bean $object) {
		return $object->id;
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"utm" => array("type"=>"text", "label"=>"UTM"),
				"type" => array("type"=>"select", "label"=>"Вид", "opts"=>inquiry::TYPES, "opt_val"=>"#", "opt_text"=>""),
				"time" => array("type"=>"date", "label"=>"Дата", "range"=>1),
		);
	}

	protected function direction() {
		return "desc";
	}

	protected function on_indexing($filter, array $objects) {
		if ($this->post_action == "export") {
			$this->export($objects);
		}
	}

	private function export($objects) {
		$this->classloader->load("ext", "excel_xml");

		$headers = array(
			"ID",
			"вид заявка",
			"дата и час",
			"име за контакт",
			"ЕГН / ЕИК",
			"имейл",
			"телефон",
		);
		$data = array($headers);
		foreach($objects as $o) {
			$campaign = $o->data("campaign");
			$file = "";

			$data[] = array(
				$o->id,
				$o->type(),
				conv_date_string($o->time),
				$o->name,
				$o->data("egn"),
				$o->email,
				$o->phone,
			);
		}
		$xls = new excel_xml("UTF-8", false, "inquiries");
		$xls->addArray($data);
		$xls->generateXML("inquiries_" . conv_date_string("now", "date"));
		exit;
	}
}