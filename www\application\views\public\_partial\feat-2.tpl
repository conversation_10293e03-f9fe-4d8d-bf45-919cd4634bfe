<section class="g-padded--sm steps-horizontal trigger-section">
	<div class="g-wrap g-wrap--xl">
		<div class="g-head">
			<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
				{$node->title}
			</p>
			<h2 class="g-title g-title--md g-title--underline js-anim">
				{$node->subtitle}
			</h2>
		</div>
		<div class="steps steps--dark">
			{foreach $node->children() as $i}
				<div class="steps__item">
					{if $i->image}
						<div class="steps__img-box">
							<img class="steps__img" src="{$i->image|image:feat5}" alt="">
						</div>
					{/if}
					<span class="steps__icon-box">
						<img class="steps__icon" src="{$i->image2|image:icon2}" alt="">
					</span>
					<div class="steps__header">
						<p class="steps__step">{@misc.step} {$i@iteration}</p>
					</div>
					<div class="steps__content">
						<p class="steps__time">
							{$i->title}
						</p>
						<p class="steps__title">
							{$i->text|pt nofilter}
						</p>
					</div>
				</div>
				<img class="steps__arrow" src="{$res}image/icon/steps-arrow.svg" alt="">
			{/foreach}
			<a href="{$node_partner|path}" class="steps__item steps__item--bg">
				<span class="steps__icon-box">
					<img class="steps__icon" src="{$node->image2|image:icon2}" alt="">
				</span>
				<div class="steps__header">
					<p class="steps__step">{@misc.step} {count($node->children())+1}</p>
				</div>
				<div class="steps__content">
					<p class="steps__time">
						{@misc.choose}
					</p>
					<p class="steps__title">
						{@misc.become_partner}
					</p>
				</div>
			</a>
		</div>
	</div>
</section>