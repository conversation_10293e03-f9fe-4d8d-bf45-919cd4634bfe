<?php

require_once "base/Post_edit.php";

class Words extends Post_edit {

	protected function opts() {
		return array(
				"add"=>false,
				"delete"=>false,
				"multi_edit"=>true,
		);
	}

	public function __construct() {
		parent::__construct();

		$languages = $this->data->languages->all();
		$this->view->set("languages", $languages);

		$this->load->library("Diction");
		$langs = __::map($languages, function($o) { return $o->code; });
		$this->diction->load($langs);

		$this->view->set("phrases", $this->diction->get_editable());

		$can_edit = $this->diction->can_edit();

		if (!$can_edit) {
			$this->set_opt("edit", false);
			$path = $this->diction->get_path();
			$res = array("type" => "notice", "message" => "Директория {$path} няма права за писане");
			$this->view->set("status", $res);
		}
	}

	public function index($paging=null) {
		if ($this->get_opt("edit") && $this->post_action == "save") {
			foreach (_post_arr("phrase") as $lang=>$arr) {
				foreach ($arr as $key=>$value) {
					$this->diction->set_value($lang, $key, $value);
				}
				$this->diction->save($lang);
			}
			$this->add_log_entry("edit", "");
			$this->set_success();
		}

		$this->render("cms/{$this->page_path}/index");
	}
}