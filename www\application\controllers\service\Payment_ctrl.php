<?php

require_once APPPATH . "controllers/Base.php";

class Payment_ctrl extends Base {

	public function __construct() {
		parent::__construct();

		$this->classloader->load("misc", "DSKGate");
		$this->load->config("dsk");
	}

	public function index() {
		$session_key = conf("dsk", "session_key");
		$dsk = _session($session_key, null, true);

		if ($dsk) {
			$model = array();
			$model["root"] = $this->path->base;
			$model["url"] = conf("dsk", "url");
			$model["params"] = $dsk;

			$this->load->view("service/payment", $model);
		} else {
			redirect("/");
		}

	}

}
