/**********************************************************
* Overwrite default styles for plugins
**********************************************************/

.steps {
	.slick-arrow {
		&.slick-prev {
			left: -1rem;
		}
		&.slick-next {
			right: -1rem;
		}
	}
}

.slick-arrow {
	@include transition-sm;
	@include circle(60);
	background-position: center;
	background-repeat: no-repeat;
	background-color: white;
	font-size: 0;
	opacity: 1;
	position: absolute;
	top: 50%;
	transform: translateY(-50%) scale(0.75);
	visibility: visible;
	z-index: 2;

	&.slick-prev {
		@include icon('arrow-left', 60, 60);
		left: 1rem;
	}

	&.slick-next {
		@include icon('arrow-right', 60, 60);
		right: 1rem;
	}
	
	&:hover {
		
	}

	&.slick-disabled {
		opacity: 0;
		visibility: hidden;
	}

	&.slick-hidden {
		display: none !important;
	}


	@include media(lg) {
    	transform: translateY(-50%);
	}
}

.slick-dots {
	display: flex;
	justify-content: center;
	margin: 2rem 0;

	li {
		line-height: 1px;
		margin: 0 0.5rem;
	}

	button {
		@include transition-sm;
		background-color: transparent;
		border: 1px solid black;
		border-radius: 0.5rem;
		font-size: 0;
		height: 1rem;
		width: 1rem;

		&:focus {
			outline: none;
		}
	}

	.slick-active button {
		background-color: black;
	}
}

/* Venobox */
.vbox-close {
	display: none;
}

/* youtube modal */
.yu2fvl {
	z-index: 9999;
	top: 0;

	&-iframe {
		display: block;
		height: 100%;
		width: 100%;
		border: 0;
	}
	
	&-overlay {
		z-index: 9998;
		background: rgba(black, 0.7);
	}
}
.yu2fvl-close {
	@include transition-xs;
	border: none;
	border-radius: 2rem;
	background: white url(#{$img-dir}/icon/close.svg) no-repeat center !important;
	box-shadow: 0 1rem 1rem 0 rgba(black, 0.15);
	cursor: pointer;
	height: 4rem;
	padding: 0;
	position: absolute;
	right: 1rem !important;
	text-indent: -999rem;
	top: -2rem !important;
	width: 4rem !important;

	&:focus {
		outline: none;
		top: -1.8rem !important;
	}

	@include media(md) {
		right: -2rem !important;    
	}
}