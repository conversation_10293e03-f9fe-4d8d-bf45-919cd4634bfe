#!/usr/bin/env python3
"""
<PERSON>ript to extract all text content from HTML files in the html directory
and save it to an INI file format.
"""

import os
import re
from pathlib import Path
from bs4 import BeautifulSoup
import configparser

def clean_text(text):
    """Clean and normalize text content."""
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    # Remove empty lines
    text = re.sub(r'\n\s*\n', '\n', text)
    return text

def extract_text_from_html(html_file_path):
    """Extract text content from an HTML file."""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Parse HTML with BeautifulSoup
        soup = BeautifulSoup(content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Extract text
        text = soup.get_text()
        
        # Clean the text
        cleaned_text = clean_text(text)
        
        return cleaned_text
    
    except Exception as e:
        print(f"Error processing {html_file_path}: {e}")
        return ""

def main():
    """Main function to process all HTML files and create INI file."""
    html_dir = Path("html")
    output_file = "extracted_html_text.ini"
    
    if not html_dir.exists():
        print(f"HTML directory '{html_dir}' not found!")
        return
    
    # Create ConfigParser object
    config = configparser.ConfigParser()
    config.optionxform = str  # Preserve case sensitivity
    
    # Get all HTML files
    html_files = list(html_dir.glob("*.html"))
    html_files.sort()  # Sort for consistent ordering
    
    print(f"Found {len(html_files)} HTML files to process...")
    
    for html_file in html_files:
        print(f"Processing: {html_file.name}")
        
        # Extract text content
        text_content = extract_text_from_html(html_file)
        
        if text_content:
            # Create section name from filename (remove extension and clean)
            section_name = html_file.stem.replace(' ', '_')
            
            # Add section to config
            config[section_name] = {}
            config[section_name]['filename'] = html_file.name
            config[section_name]['text_content'] = text_content
            config[section_name]['character_count'] = str(len(text_content))
            config[section_name]['word_count'] = str(len(text_content.split()))
    
    # Write to INI file
    try:
        with open(output_file, 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        
        print(f"\nSuccessfully extracted text from {len(html_files)} HTML files")
        print(f"Output saved to: {output_file}")
        
        # Print summary
        total_chars = sum(len(config[section]['text_content']) for section in config.sections())
        total_words = sum(int(config[section]['word_count']) for section in config.sections())
        
        print(f"\nSummary:")
        print(f"- Total sections: {len(config.sections())}")
        print(f"- Total characters: {total_chars:,}")
        print(f"- Total words: {total_words:,}")
        
    except Exception as e:
        print(f"Error writing to INI file: {e}")

if __name__ == "__main__":
    main()
