<?php

require_once "Edit_nodes.php";

abstract class Edit_child_nodes extends Edit_nodes {

	protected abstract function parent_nodes();
	
	protected $parent_ids;
	protected $parents;

	public function __construct() {
		parent::__construct();

		$this->parents = $this->get_parents();
		$this->parent_ids = __::map($this->parents, function($o) { return $o->id; });

		$this->view->set("parents", $this->parents);
	}

	protected function get_parents() {
		$filter = array(
				"start" => nodeid($this->parent_nodes()),
				"min_length" => $this->min_depth() - 1,
				"max_length" => $this->depth() - 1,
		);
		return $this->data->nodes->get_list_loc($this->lang_id, $filter);
	}
	
	protected function depth() {
		return 1;
	}

	protected function min_depth() {
		return $this->depth();
	}
	
	protected function objects_filter() {
		return array(
				"parent_ids" => $this->parent_ids,
		);
	}
	
	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		if (count($this->parent_ids) == 1) {
			$object->parent_id = $this->parent_ids[0];
		}
	}

	protected function validate(validator $validator) {
		if (!in_array($validator->object()->parent_id, $this->parent_ids)) {
			$validator->set_invalid("parent_id");
		}
		return parent::validate($validator);
	}

}