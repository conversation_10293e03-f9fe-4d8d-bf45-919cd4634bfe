<?php

require_once __DIR__ . "/../Public_base.php";

class About extends Public_base {
	public function index() {
		$node = $this->node();
		
		$this->view->set("members", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("about"), "kinds" => "member")));
		$this->view->set("feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("about"), "kinds" => "feat")));
		$this->view->set("support_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("about_support"), "kinds" => "feat")));
		$this->view->set("location_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("about_locations"), "kinds" => "feat")));
		$this->view->set("members2", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("about_team"), "kinds" => "member")));

		$this->view->set("reviews", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("reviews"), "kinds" => "review")));

		$benefits = $this->get_nodes("comp_clients", "comp_partners");
		$this->data->nodes->get_list_loc($this->lng->id, array("parent_ids" => array_prop($benefits, "id"), "kinds" => "row"));
		$this->view->set("benefits", $benefits);
		
		$this->render_node("public/about", $node);
	}
}
