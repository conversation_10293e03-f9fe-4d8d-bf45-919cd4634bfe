<?php

/**
 * Gets the serving url for an image
 * @param $file The filename of the image
 * @param string $index The index of the thumbnail to be served. Empty for the original image.
 * @param string $path The path, as defined in filemanger.php, where the original image is located. Emptu for the default path.
 * @return string The resulting url of the image to be served.
 */
function file_image_url($file, $index="", $path="") {
	$CI =& get_instance();
	return $CI->path->base . file_image_url_part($file, $index, $path);
}


function file_image_url_part($file, $index="", $path="") {
	return "service/image?" . Thumbnails::get_thumb_servename($file, $index, $path);
}

function file_get($file, $path="", $handler=true, $attachment=true, $asset=false) {
	$CI =& get_instance();
	if ($handler) {
		if ($path !== "") {
			$path = "&k={$path}";
		}
		$dl = "";
		if (!$attachment) {
			$dl = "&dl=0";
		}
		$asset_param = "";
		if ($asset) {
			$asset_param = "&a=1";
		}
		return $CI->path->base . "service/file?f={$file}{$path}{$dl}{$asset_param}";
	} else {
		return $CI->filemanager->get_file($file, $path, false, $attachment);
	}
}

function file_image_srcset($file, $name="", $path="") {
	$thumbs = get_instance()->thumbnails->get_srcset($name);
	$res = array();
	if ($thumbs) {
		foreach ($thumbs as $key=>$thumb) {
			$res[] = file_image_url($file, $thumb, $path) . " ". $key;
		}
	}
	return join(", ", $res);
}

function file_send($file, $name="", $dl=true) {
	$content_type = FileManager::get_mime($file);
	header("Content-type: $content_type");
	header("Cache-Control: public");
	if (!$name) {
		$name = basename($file);
	}
	if ($dl) {
		$dl = "attachment; ";
	}
	header("Content-Disposition: {$dl}filename=\"{$name}\"");
	readfile($file);
	exit;
}


function my_mkdir($path, $mask = 0777){
	$path = str_replace(array("/", "\\"), DIRECTORY_SEPARATOR, $path);

	if (is_dir($path) || empty($path)) {
		return true;
	}

	if (is_file($path)) {
		throw new exception("File '{$path}' exists");
	}

	$old = umask(0);
	$res = mkdir($path, $mask, true);
	umask($old);
	return $res;
}

function glob_rec($pattern, $flags = 0) {
	$files = glob($pattern, $flags);
	foreach (glob(dirname($pattern)."/*", GLOB_ONLYDIR|GLOB_NOSORT) as $dir) {
		$files = array_merge($files, glob_rec($dir."/".basename($pattern), $flags));
	}
	return $files;
}


function read_ini($file) {
	$file = file($file, FILE_IGNORE_NEW_LINES);
	$res = array();

	$section = "";
	foreach ($file as $row) {
		if (preg_match("/\[([a-z0-9\-\_]+)\]/", $row, $matches)) {
			$section = $matches[1];
		}

		if (preg_match("/^\s*([a-z0-9\-\_]+)\s*=\s*(.*)$/", $row, $matches)) {
			$key = $matches[1];
			$value = $matches[2];
			if ($section) {
				$res[$section][$key] = $value;
			} else {
				$res[$key] = $value;
			}
		}
	}
	return $res;
}

function write_ini($file, $data) {
	$res = array();
	$first = true;
	foreach($data as $key => $val) {
		if(is_array($val)) {
			if (!$first) {
				$res[] = "";
			}
			$first = false;
			$res[] = "[$key]";
			foreach($val as $skey => $sval) {
				$sval = str_replace("\n", "\\n", $sval);
				$res[] = "$skey = ".$sval;
			}
		} else {
			$val = str_replace("\n", "\\n", $val);
			$res[] = "$key = ".$val;
		}
	}
	$res = implode("\n", $res);

	if ($fp = fopen($file, "w")) {
		flock($fp, LOCK_EX);
		fwrite($fp, $res);
		flock($fp, LOCK_UN);
		fclose($fp);
	} else {
		throw new exception("Cannot save file {$file}.");
	}
}