<?php

require_once "Restricted.php";

abstract class Base_edit extends Restricted {

	protected $page_path;
	protected $page_title;
	protected $post_action;
	private $opts;

	private $def_opts = array (
			"view" => true,
			"add" => true,
			"edit" => true,
			"delete" => true,
	);

	public function __construct() {
		parent::__construct();

		$this->load_smarty();

		$this->post_action = _post("action");
		$this->page_path = $this->router->fetch_class();

		$this->opts = $this->get_effective_opts();

		$mainmenu = conf("cms", "menu");
		$this->page_title = array();

		foreach ($mainmenu as &$main) {
			foreach ($main["items"] as &$sub) {
				if ($sub) {
					$sub["enabled"] = $this->usession->user()->has_right($sub["url"], "view");
					if ($sub["url"] == $this->page_path) {
						$main["active"] = true;
						$sub["active"] = true;
						$this->page_title = array($main["title"], $sub["title"]);
					}
				}
			}
		}

		$cms_path = trim(conf("cms", "path"), "/");
		$html_title = array(conf("app_domain"), $cms_path);
		$html_title = array_merge($html_title, $this->page_title);
		$html_title = implode(" &rsaquo; ", $html_title);
		$this->page_title = implode(" / ", $this->page_title);

		$this->view->set("mainmenu", $mainmenu);
		$this->view->set("page_title", $this->page_title);
		$this->view->set("html_title", $html_title);

		$this->view->set("app_root", $this->path->base);
		$this->view->set("cms_root", $this->path->base . conf("cms", "path"));
		$this->view->set("cms_path", $cms_path);
		$this->view->set("page_path", $this->page_path);
		$this->view->set("uri", $this->path->base . conf("cms", "path") . $this->page_path);
		$this->view->set("session_remain", $this->usession->time_remain());
		
		$status = $this->get_status();
		$js_vars = array(
				"cmsRoot" => $this->path->base . conf("cms", "path"),
				"pagePath" => $this->page_path,
				"status" => $status,
		);
		$this->view->set("js_vars", json_encode($js_vars));

		$this->view->set("app_domain", conf("app_domain"));
		$this->view->set("cms_skin", conf("cms", "skin"));
		$this->view->set("cms_name", conf("cms", "name"));
		$this->view->set("app_author", conf("app_author"));
		$this->view->set("app_copyright", conf("app_copyright"));
		$this->view->set("app_copyright_date", conf("app_copyright_date"));

		$this->view->set("opt_bool", conf("cms", "opt_bool"));
		$this->view->set("opt_direction", conf("cms", "opt_direction"));
		$this->view->set("show_stats", conf("show_exec_stats"));
		$this->view->set("lang_code", conf("cms", "lang_code"));

		$this->view->set("usession", $this->usession);
		$this->view->set("status", $status);
		$this->view->set("opts", $this->opts);
	}


	protected function on_indexing($filter, array $objects) { }
	protected function on_deleted(ci_bean $object) { }

	protected function on_object_load($action, ci_bean $object) { }
	protected function on_object_set($action, ci_bean $object) { }
	protected function on_object_saved($action, ci_bean $object) { }

	protected function get_count($filter) {
		return 0;
	}
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return array();
	}
	protected function create_object() {
		return null;
	}
	protected function get_object($id) {
		return null;
	}
	protected function get_object_title(ci_bean $object) {
		return $object->title;
	}

	protected function opts() {
		return array();
	}

	protected function filters() {
		return array();
	}
	protected function orders() {
		return array();
	}
	protected function direction() {
		return "asc";
	}
	protected function reorder_target() {
		return null;
	}

	protected function add_object(ci_bean $object) {
		$this->data->add($object);
	}
	protected function update_object(ci_bean $object) {
		$this->data->update($object);
	}
	protected function delete_object(ci_bean $object) {
		$this->data->delete($object);
	}

	protected function validation_set() {
		return "def";
	}
	protected function validate(validator $validator) {
		return $validator->validate($this->validation_set());
	}

	protected function set_success($location="") {
		$location = $location ? $location : $this->page_path;
		$this->set_status("success", $location, "Промените са запазени!");
	}

	protected function set_error($location, $message) {
		$this->set_status("error", $location, $message);
	}

	protected function set_status($type, $location, $message) {
		$res = array("type" => $type, "message" => $message);
		$this->set_flash(conf("cms", "key_status"), $res);
		redirect(conf("cms", "path") . $location);
	}

	protected function get_status() {
		return $this->get_flash(conf("cms", "key_status"));
	}

	protected function query_save($index, $size, $params) {
		$query = array($index, $size, $params);
		$current = _session(conf("cms", "key_filter"), array());
		$current[$this->page_path] = $query;
		set_session(conf("cms", "key_filter"), $current);
	}

	protected function query_load() {
		$res = "";
		$query = _session(array(conf("cms", "key_filter"), $this->page_path), true, null);
		if (is_array($query)) {
			$res = "/index/{$query[0]},{$query[1]}";
			if ($query[2]) {
				$res .= "?" . $query[2];
			}
		}
		return $res;
	}

	protected function get_save_url($object) {
		return $this->page_path . "/edit/" . $object->id;
	}
	protected function get_close_url() {
		return $this->page_path . $this->query_load();
	}

	protected function add_log_entry($action, $description) {
		$log = new cms_log();
		$log->email = $this->usession->user()->email;
		$log->name = $this->usession->user()->name;
		$section = $this->page_path;
		$mainmenu = conf("cms", "menu");
		$title = "";
		foreach ($mainmenu as $main) {
			foreach ($main["items"] as $sub) {
				if ($sub) {
					if ($sub["url"] == $section) {
						$title = $sub["title"];
					}
				}
			}
		}

		$log->section = $title;
		$log->action = $action;
		$log->description = $description;
		$this->data->add($log);
	}

	protected function get_opt($opt) {
		return arr($this->opts, $opt, false);
	}
	protected function set_opt($opt, $value) {
		$this->opts[$opt] = $value;
		$this->view->set("opts", $this->opts);
	}

	private function get_effective_opts() {
		$user = $this->usession->user();
		if ($this->page_path != "home" && !$user->has_right($this->page_path, "view")) {
			redirect(conf("cms", "path"));
		}

		$res = array_merge($this->def_opts, $this->opts());
		$freeze = conf("cms", "freeze");
		if ($freeze || !$user->has_right($this->page_path, "full")) {
			$res["add"] = false;
			$res["delete"] = false;
		}
		if ($freeze || !$user->has_right($this->page_path, "edit")) {
			$res["edit"] = false;
		}

		return $res;
	}
}
