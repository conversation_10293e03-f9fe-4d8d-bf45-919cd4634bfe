<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:38:44
  from "W:\work\dvm_finance\www\application\views\public\_partial\benefits.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6b742ae3b0_16318601',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9ba79c8e3fdc12b1aea97f3c5ddc599d3bfdc428' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\_partial\\benefits.tpl',
      1 => 1751730493,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_686b6b742ae3b0_16318601 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
?>
<section class="g-padded--sm trigger-section">
	<div class="g-wrap g-wrap--xl">
		<div class="g-head">
			<p class="g-font-title g-center g-title--color g-gray">
				<?php echo $_smarty_tpl->tpl_vars['node_benefits']->value->subtitle;?>

			</p>
		</div>
		<div class="chart">
			<div class="g-wrap">
				<ul class="chart__tabs">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['benefits']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_8_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<li class="chart__tab <?php if ($_smarty_tpl->tpl_vars['i']->first) {?>-current<?php }?>" data-id="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->index, ENT_QUOTES, 'UTF-8');?>
">
							<a class="chart__link js-scroll-to" href="#table"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</a>
						</li>
					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_8_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</ul>
				<div class="g-head">
					<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
						<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_benefits']->value->feat2, ENT_QUOTES, 'UTF-8');?>

					</p>
					<h2 class="g-title g-title--md g-title--underline js-anim">
						<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node_benefits']->value->text);?>

					</h2>
				</div>
				<div class="chart__container" id="table">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['benefits']->value, 'i');
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_9_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<div class="chart__table <?php if ($_smarty_tpl->tpl_vars['i']->first) {?>-visible<?php }?>">
							<div class="chart__row">
								<div class="chart__cell chart__cell--title chart__cell--left"></div>
								<div class="chart__cell chart__cell--title chart__cell--white">
									<p class="chart__title">DVM Finance</p>
								</div>
								<div class="chart__cell chart__cell--title">
									<p class="chart__title"><?php echo smarty_modifier_dict("misc.compare_bank");?>
</p>
								</div>
								<div class="chart__cell chart__cell--title">
									<p class="chart__title"><?php echo smarty_modifier_dict("misc.compare_consult");?>
</p>
								</div>
							</div>
							<?php $_smarty_tpl->_assignInScope('icons', array(1=>"table-check-black",0=>"table-x"));
?>
							<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['i']->value->children(), 'j');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['j']->value) {
?>
								<div class="chart__row">
									<div class="chart__cell chart__cell--left">
										<p><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['j']->value->title, ENT_QUOTES, 'UTF-8');?>
</p>
									</div>
									<div class="chart__cell chart__cell--white">
										<img class="chart__icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['icons']->value[$_smarty_tpl->tpl_vars['j']->value->is_featured_home], ENT_QUOTES, 'UTF-8');?>
.svg" alt="">
									</div>
									<div class="chart__cell">
										<img class="chart__icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['icons']->value[$_smarty_tpl->tpl_vars['j']->value->is_featured], ENT_QUOTES, 'UTF-8');?>
.svg" alt="">
									</div>
									<div class="chart__cell">
										<img class="chart__icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['icons']->value[$_smarty_tpl->tpl_vars['j']->value->is_featured2], ENT_QUOTES, 'UTF-8');?>
.svg" alt="">
									</div>
								</div>
							<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

						</div>
					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_9_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
			</div>
		</div>
	</div>
</section><?php }
}
