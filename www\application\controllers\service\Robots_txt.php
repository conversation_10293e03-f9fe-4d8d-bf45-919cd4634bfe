<?php

class Robots_txt extends CI_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->helper("url");
		$this->classloader->load("common");

		$this->load->library("datamanager", null, "data");

		if (conf("force_ssl") && !$this->input->is_cli_request()) {
			enforce_ssl();
		}
	}

	public function index() {
		$var = $this->data->variables->get_first(array("keys" => "robots.txt"));
		if ($var) {
			header("Content-Type: text/html;");
			echo $var->value;
		}
	}
}