<?php

require_once "base/Edit_nodes.php";

class Pages extends Edit_nodes {

	private $node_opts = array(
		"home" => array(
			"opts" => array("meta", "subtitle_rich"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "subtitle", "text", "image"),
				),
			),
		),
		"home_banks" => array(
			"opts" => array(),
			"subitems" => array(
				"logo" => array(
					"label" => "Лога",
					"opts" => array("image"),
				),
			),
		),
		"home_clients" => array(
			"opts" => array("subtitle", "image"),
		),
		"home_cta" => array(
			"opts" => array("text_plain", "image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "subtitle", "text"),
				),
			),
		),
		"home_process" => array(
			"opts" => array("subtitle"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "image", "icon"),
				),
			),
		),
		"home_partner" => array(
			"opts" => array("subtitle_rich", "image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"home_partner2" => array(
			"opts" => array("subtitle_rich", "image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"home_process2" => array(
			"opts" => array("subtitle"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "image", "icon"),
				),
			),
		),
		"home_why" => array(
			"opts" => array("subtitle_rich", "image"),
			"subitems" => array(
				"member" => array(
					"label" => "Екип",
					"opts" => array("title", "text", "image", "icon", "add"=>false),
				),
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"home_faq" => array(
			"opts" => array("subtitle", "text_plain"),
			"subitems" => array(
				"question" => array(
					"label" => "Въпроси",
					"opts" => array("title", "text"),
				),
			),
		),
		"home_cta2" => array(
			"opts" => array("subtitle_rich"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "add"=> false),
				),
			),
		),
		"services" => array(
			"opts" => array("meta", "slug"),
		),
		"about" => array(
			"opts" => array("meta", "slug", "subtitle_rich", "text_plain", "image"),
			"subitems" => array(
				"member" => array(
					"label" => "Екип",
					"opts" => array("title", "text", "image", "icon", "add"=>false),
				),
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"about_support" => array(
			"opts" => array(),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "subtitle", "subtitle2", "text", "image"),
				),
			),
		),
		"about_locations" => array(
			"opts" => array("text_plain", "preview"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "image"),
				),
			),
		),
		"about_team" => array(
			"opts" => array(),
			"subitems" => array(
				"member" => array(
					"label" => "Екип",
					"opts" => array("title", "subtitle", "text", "image"),
				),
			),
		),
		"partner" => array(
			"opts" => array("meta", "slug", "subtitle_rich", "text_plain", "image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"partner_become" => array(
			"opts" => array("subtitle_rich", "image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"partner_exp" => array(
			"opts" => array("text_plain"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"partner_process" => array(
			"opts" => array("subtitle", "icon"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "image", "icon"),
				),
			),
		),
		"partner_faq" => array(
			"opts" => array("subtitle", "text_plain"),
			"subitems" => array(
				"question" => array(
					"label" => "Въпроси",
					"opts" => array("title", "text"),
				),
			),
		),
		"partner_reviews" => array(
			"opts" => array(),
			"subitems" => array(
				"review" => array(
					"label" => "Отзиви",
					"opts" => array("title", "subtitle", "text", "image"),
				),
			),
		),
		"calculator" => array(
			"opts" => array("meta", "slug", "image"),
		),
		"faq" => array(
			"opts" => array("meta", "slug"),
		),
		"news" => array(
			"opts" => array("meta", "slug"),
			"subitems" => array(
				"category" => array(
					"validator" => "def",
					"label" => "Категории",
					"opts" => array("title", "slug"),
				),
			),
		),
		"contacts" => array(
			"opts" => array("meta", "slug", "subtitle", "map"),
		),
	);

	protected function nodes() {
		return array("home", "services", "about", "partner", "calculator", "faq", "news", "contacts");
	}

	protected function objects_filter() {
		return array(
			"ids" => nodeid($this->nodes()),
		);
	}

	protected function opts() {
		return array(
			"add" => false,
			"delete" => false,
		);
	}
	
	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);

		$nodename = nodename($object->id);

		$filter = array("parent_id" => $object->id, "kinds" => "block");
		$blocks = $this->data->nodes->get_list($filter);
		
		$this->data->node_links->by_parent(array_prop($blocks, "id"));
		$this->data->node_locale->by_nodes($blocks);

		$opts = array();
		foreach ($blocks as $i) {
			$opts[$i->id] = $this->node_opts[nodename($i->id)];
		}

		$this->view->set("blockopts", $opts);
		$this->view->set("blocks", $blocks);
		$this->view->set("node_opts", array_flip($this->node_opts[$nodename]["opts"]));
		$this->view->set("subitems", arr($this->node_opts[$nodename], "subitems", array()));

	}

	protected function asset_types($node_id) {
		$opts = $this->node_opts[nodename($node_id)]["opts"];

		$res = array();
		$known = array("images" => AssetType::IMAGE, "videos" => AssetType::EMBED, "images_videos" => AssetType::IMAGE | AssetType::EMBED);
		foreach ($known as $k=>$t) {
			if (in_array($k, $opts)) {
				$res[] = $t;
			}
		}
		return $res;
	}

	protected function on_object_saved($action, ci_bean $object) {
		parent::on_object_saved($action, $object);

		$main_blocks = $object->children("block");
		foreach ($main_blocks as $i) {
			$data = _post_files_merge(array("block", $i->id));
			if ($data) {
				$i->set($data);
				$this->data->update($i);
			}
			$linked = arr($data, "linked");
			if (!$linked) {
				$linked = array();
			}
		}
	}

	protected function resolve_subitem($id, $kind) {
		$name = nodename($id);
		return arr($this->node_opts, array($name, "subitems", $kind));
	}

	protected function get_subitem_parent($id) {
		$ids = nodeid($this->nodes());
		$filter = array("id" => $id, "parent_ids" => $ids);
		$res = $this->data->nodes->get_first($filter);
		if (!$res) {
			$res = $this->get_object($id);
		}
		return $res;
	}
}
