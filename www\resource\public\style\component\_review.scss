.review {
    position: relative;
    &__content {
        position: absolute;
        bottom: 2rem;
        left: 2rem;
        right: 2rem;
        padding: 2.5rem 2rem;
        background-color: rgba($color-white, .3);
        border: 1px solid rgba($color-black, .2);
        backdrop-filter: blur(20px);
        border-radius: 3px;
        max-width: 53rem;
    }

    &__img-box {
        width: 100%;
        height: 55rem;
        border-radius: 3px;
        overflow: hidden;
    }

    &__img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.review-carousel {
    &__btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        &--prev {
            left: 0;
        }
        &--next {
            right: 0;
        }
    }

    .slick-list {
        margin-right: -2rem;
    }

    .review {
        margin-right: 2rem;
    }
}