<?php

function clean_html($text) {
	$text = strip_tags($text);
	$text = html_entity_decode($text, ENT_QUOTES, "utf-8");
	$text = preg_replace("/\s+/ui", " ", $text);
	$text = trim($text);
	return $text;
}

function text_limit($text, $count, $more = "&#8230;") {
	$text = clean_html($text);

	if (mb_strlen($text, "utf-8") < $count) {
		return $text;
	}
	$text = mb_substr($text, 0, $count, "utf-8");

	$index = mb_strrpos($text, " ", 0, "utf-8");
	if ($index !== false) {
		$text = mb_substr($text, 0, $index, "utf-8");
	}
	$text .= $more;

	return $text;
}

function text_search($text, $search, $characters = 50) {
	$text = clean_html($text);

	$res = "";
	$pos = mb_stripos($text, $search, 0, "utf-8");
	if ($search !== "" && $pos !== false) {
		$start = max($pos - $characters / 2, 0);
		$length = mb_strlen($search, "utf-8") + $characters;
		$context = mb_substr($text, $start, $length, "utf-8");
		$search = preg_quote($search, "/");
		$context = preg_replace("/$search/ui", "<b>$0</b>", $context);
		$left = ($start > 0 ? "&#8230;" : "");
		$right = ($length < mb_strlen($text, "utf-8") ? "&#8230;" : "");
		$res = $left . $context . $right;
	} else {
		$res = text_limit($text, $characters);
	}
	return $res;
}

function ends_with($str, $search) {
	return substr($str, -strlen($search)) == $search;
}

function trim_end($str, $search) {
	if (ends_with($str, $search)) {
		$str = substr($str, 0, -strlen($search));
	}
	return $str;
}

function transliterate($input) {
	$map = array(
			'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y',
			'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u',
			'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch', 'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya',
			'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ж' => 'Zh', 'З' => 'Z', 'И' => 'I', 'Й' => 'Y',
			'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N', 'О' => 'O', 'П' => 'P', 'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U',
			'Ф' => 'F', 'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch', 'Ш' => 'Sh', 'Щ' => 'Sht', 'Ъ' => 'A', 'Ь' => 'Y', 'Ю' => 'Yu', 'Я' => 'Ya'
	);
	return strtr($input, $map);
}
function mb_ucfirst($str) {
	return mb_strtoupper(mb_substr($str, 0, 1)).mb_substr($str, 1);
}

function str_shift($string, $lower = true, $upper = true, $digits = true, $extra = true) {
	static $chars = array (
			"lower" => "abcdefghijklmnopqrstuvwxyz",
			"upper" => "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
			"digits" => "0123456789",
			"extra" => ",.-()<>%/!&=;:_[]{}#?'+*",
	);


	$use = array (
			"lower" => $lower,
			"upper" => $upper,
			"digits" => $digits,
			"extra" => $extra
	);

	$set = "";
	foreach ($chars as $type => $letters) {
		if ($use[$type]) {
			$set .= $letters;
		}
	}

	$shift = mb_strlen($set) / 2;

	$repl = substr($set, $shift) . substr($set, 0, $shift);
	return strtr($string, $set, $repl);
}

function mb_wordwrap($line, $width = 75) {
	$res = array();
	
	$words = explode(' ', rtrim($line));
	$current = '';
	foreach ($words as $i) {
		if (mb_strlen($current.$i) <= $width) {
			$current .= $i . ' ';
		} else {
			if ($current != '') {
				$res[] = $current;
			}
			$current = $i . ' ';
		}
	}
	
	$res[] = $current;
	
	return $res;
}

function mb_str_word_count($str) {
  return count(preg_split('~[^\p{L}\p{N}\']+~u',$str));
}

function read_time($text) {
	$text = strip_tags($text);
	return max(1, intval(ceil(mb_str_word_count($text) / 130)));
}