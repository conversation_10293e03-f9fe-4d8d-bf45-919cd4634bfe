$parent: '.article';

#{$parent} {
    display: flex !important;
    flex-direction: column;
    gap: 2rem;

    &--lg {
        #{$parent}__title {
            font-size: clamp(3rem, 2vw, 4.8rem);
            line-height: 1em;
        }
        #{$parent}__content {
            max-width: 120rem;;
        }
    }

    &:hover {
        #{$parent}__img {
            transform: scale(1.05);
        }

        .btn {
            filter: invert(.5);
        }
    }

    &__img-box {
        width: 100%;
        aspect-ratio: 2 / 1 !important;
        overflow: hidden;
    }

    &__img {
        @include transition-md;
        display: block;
        width: 100%;
        object-fit: cover;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        padding-right: 2rem;
    }

    &__date {
        font-size: 1.4rem;
        opacity: 0.6;
    }

    &__title {
        font-family: $font-title;
        &--lg {
            font-size: clamp(3rem, 2vw, 4.8rem);
            line-height: 1em;
        }
    }

    &__share {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
        font-family: $font-title;
    }

    &__share-label,
    &__share-list {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
}

.article-carousel {
    .slick-list {
        margin-right: -2rem;
    }

    .article {
        margin-right: 2rem;
    }
}