$parent: '.steps';

#{$parent} {
    display: flex;

    &--dark {
        #{$parent}__item {
            background-color: #F7F7F7;

            &--bg {
                background-color: #071B34;
                color: $color-white;
            }
        }

        #{$parent}__icon-box {
            background-color: #071B34;
        }
    }

    &__item {
        position: relative;
        display: flex !important;
        flex-direction: column;
        gap: 12rem;
        flex: 1 1 15%;
        min-height: 29rem;
        border-radius: 1.5rem;
        overflow: hidden;
        padding: 3rem 1.5rem 5rem;
        background-color: $color-gray-light;

        &--img {
            color: $color-white;
        }

        &--bg {
            @include transition-xs;
            background-color: $color-primary;
            color: $color-white;

            &:hover {
                transform: scale(1.02);
            }

            #{$parent}__icon-box {
                background-color: $color-white;
            }
        }
    }

    &__img-box {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        overflow: hidden;

        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color-black, .5);
        }
    }

    &__img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    &__icon-box {
        position: absolute;
        width: 50%;
        aspect-ratio: 1 / 1 !important;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        top: -1.4rem;
        right: -1rem;
        background-color: $color-primary;
        border-radius: 50%;
    }

    &__icon {
        width: 40%;
    }

    &__step {
        font-size: 1.2rem;
        text-transform: uppercase;
        opacity: .5;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    &__time {
        font-size: 1.5rem;
        opacity: .5;
    }

    &__title {
        font-size: 2rem;
        font-family: clamp(1.6rem, 1.5vw, 2rem);
    }

    &__header,
    &__content {
        position: relative;
    }

    &__arrow {
        width: 1.5vw;
    }

    /* 1280 down */
    @include media(lg-down) {
        .slick-list {
            margin-right: -2rem;
        }

        .slick-track {
            display: flex !important;
        }

        .slick-slide {
            height: inherit !important;
        }

        &__arrow {
            display: none !important;
        }

        &__item {
            margin-right: 2rem;
            flex:none;
        }
    }
}