<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:38:44
  from "W:\work\dvm_finance\www\application\views\public\home.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6b74272477_55046769',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4af2949b0e44c1630ec757d6b77d768e0cb9447a' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\home.tpl',
      1 => 1751730621,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/feat-3.tpl' => 1,
    'file:./_partial/calculator.tpl' => 1,
    'file:./_partial/feat-4.tpl' => 1,
    'file:./_partial/feat-2.tpl' => 2,
    'file:./_partial/feat-1.tpl' => 1,
    'file:./_partial/feat-5.tpl' => 1,
    'file:./_partial/benefits.tpl' => 1,
    'file:./_partial/feat-6.tpl' => 1,
    'file:./_partial/news-item.tpl' => 1,
    'file:./_partial/faq.tpl' => 1,
    'file:./_partial/reviews.tpl' => 1,
  ),
),false)) {
function content_686b6b74272477_55046769 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_path')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.path.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--center");
$_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', false);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_845520203686b6b74271a27_16724526', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_845520203686b6b74271a27_16724526 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--sm facts">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-max-1200">
				<h2 class="g-title g-title--xm g-title--underline js-anim">
					<?php echo $_smarty_tpl->tpl_vars['node']->value->subtitle;?>

				</h2>
				<ul>
					<li>
						<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
">
							<?php echo smarty_modifier_dict("misc.free_consult");?>

							<i class="icon icon--arrow-btn-white"></i>
						</a>
					</li>
				</ul>
			</div>
			<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-3.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['feat']->value), 0, false);
?>

		</div>
	</section>
	<section class="g-padded--sm trigger-section">
		<div class="g-wrap g-wrap--xl">
			<h3 class="g-center g-uppercase g-opacity-60 g-font-title g-mb-20 "><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_home_banks']->value->title, ENT_QUOTES, 'UTF-8');?>
</h3>
			<div class="scroll ">
				<div class="scroll__group">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['logos']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_0_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<div class="scroll__item">
							<img src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'logo'), ENT_QUOTES, 'UTF-8');?>
" alt="" class="scroll__img">
						</div>
					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_0_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
				<div class="scroll__group">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['logos']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_1_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<div class="scroll__item">
							<img src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'logo'), ENT_QUOTES, 'UTF-8');?>
" alt="" class="scroll__img">
						</div>
					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_1_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
			</div>
		</div>
	</section>
	<section class="g-padded--sm ">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head">
				<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_home_clients']->value->title, ENT_QUOTES, 'UTF-8');?>

				</p>
				<h2 class="g-title g-title--md g-title--underline js-anim">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_home_clients']->value->subtitle, ENT_QUOTES, 'UTF-8');?>

				</h2>
			</div>
			<?php $_smarty_tpl->_subTemplateRender("file:./_partial/calculator.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

		</div>
	</section>
	<section class="g-padded--sm ">
		<div class="g-wrap">
			<div class="g-head g-mb-0">
				<p class="g-uppercase g-font-title g-mb-30 g-color-primary g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_home_cta']->value->title, ENT_QUOTES, 'UTF-8');?>

				</p>
			</div>
			<div class="g-relative hero-img">
				<div class="hero-img__box">
					<img class="g-img-spacer" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['node_home_cta']->value->image,'banner6'), ENT_QUOTES, 'UTF-8');?>
" alt="">
				</div>
				<div class="hero-img__content">
					<div class="hero-img__title g-title g-title--md g-mb-0">
						<p><?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node_home_cta']->value->text);?>
</p>
					</div>
					<ul>
						<li>
							<a class="btn btn--md btn--white" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
">
								<?php echo smarty_modifier_dict("misc.free_consult");?>

								<i class="icon icon--chat"></i>
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div class="g-row">
				<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-4.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['cta_feat']->value), 0, false);
?>

			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-2.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_home_process']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-1.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_home_partner']->value,'show_link'=>true,'show_title'=>true), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-5.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_home_partner2']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-2.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_home_process2']->value), 0, true);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/benefits.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php if ($_smarty_tpl->tpl_vars['services']->value) {?>
		<section class="g-padded--sm trigger-section">
			<div class="g-wrap g-wrap--xl">
				<div class="g-wrap--spaced related">
					<button class="related__btn related__btn--prev">
						<i class="icon icon--arrow-left-gray"></i>
					</button>
					<button class="related__btn related__btn--next">
						<i class="icon icon--arrow-right-gray"></i>
					</button>
					<div class="g-padded--sm">
						<div class="g-wrap g-wrap--lg">
							<div class="g-row g-gap-1 g-vertical">
								<div class="col-1 col-xm-3-5">
									<div class="related__main">
										<div class="related__carousel">
											<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['services']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_2_saved = $_smarty_tpl->tpl_vars['i'];
?>
												<div class="related__slide">
													<div class="related__header">
														<div class="related__paging">
															<span class="related__current"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->iteration, ENT_QUOTES, 'UTF-8');?>
</span>
															-
															<span class="related__total"><?php echo htmlspecialchars(count($_smarty_tpl->tpl_vars['services']->value), ENT_QUOTES, 'UTF-8');?>
</span>
														</div>
														<h2 class="g-title g-title--md g-mb-0 g-color-primary"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</h2>
													</div>
													<div class="related__img-box is-hidden--xm">
														<img class="g-img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'service'), ENT_QUOTES, 'UTF-8');?>
" alt="">
													</div>
													<div class="related__content">
														<div class="g-editor g-opacity-60">
															<p>
																<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->preview);?>

															</p>
														</div>
														<ul>
															<li>
																<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['i']->value), ENT_QUOTES, 'UTF-8');?>
">
																	<?php echo smarty_modifier_dict("misc.service_more");?>

																	<i class="icon icon--arrow-btn-white"></i>
																</a>
															</li>
														</ul>
													</div>
												</div>
											<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_2_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

										</div>
									</div>
								</div>
								<div class="col-1 col-xm-2-5 is-hidden is-block--xm">
									<div class="related__img-slider">
										<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['services']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_3_saved = $_smarty_tpl->tpl_vars['i'];
?>
											<div class="related__img-box">
												<img class="g-img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'service'), ENT_QUOTES, 'UTF-8');?>
" alt="">
											</div>
										<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_3_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	<?php }?>
	<section class="g-padded--sm ">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head">
				<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node_home_why']->value->title, ENT_QUOTES, 'UTF-8');?>

				</p>
				<h2 class="g-title g-title--md g-title--underline js-anim">
					<?php echo $_smarty_tpl->tpl_vars['node_home_why']->value->subtitle;?>

				</h2>
			</div>
		</div>
		<div class="g-relative">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['members']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_4_saved = $_smarty_tpl->tpl_vars['i'];
?>
				<div class="team-card <?php if ($_smarty_tpl->tpl_vars['i']->first) {?>team-card--left<?php } else { ?>team-card--right<?php }?>">
					<img class="team-card__icon" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image2,'icon'), ENT_QUOTES, 'UTF-8');?>
" alt="">
					<h3 class="team-card__name"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</h3>
					<div class="g-editor">
						<p>
							<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

						</p>
					</div>
					<img class="team-card__signature" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'signature'), ENT_QUOTES, 'UTF-8');?>
" alt="">
				</div>
				<?php if ($_smarty_tpl->tpl_vars['i']->first) {?>
					<img class="g-img-spacer g-radius-3" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['node_home_why']->value->image,'banner4'), ENT_QUOTES, 'UTF-8');?>
" alt="">
				<?php }?>
			<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_4_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

		</div>
		<div class="g-wrap">
			<div class="g-padded--xs">
				<div class="g-row g-gap-1 g-gap-v-1">
					<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-6.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['why_feat']->value), 0, false);
?>

				</div>
			</div>
			<ul class="g-center">
				<li>
					<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_about']->value), ENT_QUOTES, 'UTF-8');?>
">
						<?php echo smarty_modifier_dict("misc.about_more");?>

						<i class="icon icon--arrow-btn-white"></i>
					</a>
				</li>
			</ul>
		</div>
	</section>
	<?php if ($_smarty_tpl->tpl_vars['news']->value) {?>
		<section class="g-padded--sm">
			<div class="g-wrap g-wrap--xl">
				<div class="g-head g-mb-0">
					<p class="g-uppercase g-font-title g-color-primary g-title--sub g-mb-30">
						<?php echo smarty_modifier_dict("misc.news");?>

					</p>
				</div>
				<div class="g-row g-gap-1 article-carousel">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['news']->value, 'i');
$_smarty_tpl->tpl_vars['i']->iteration = 0;
$_smarty_tpl->tpl_vars['i']->index = -1;
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
$_smarty_tpl->tpl_vars['i']->iteration++;
$_smarty_tpl->tpl_vars['i']->index++;
$_smarty_tpl->tpl_vars['i']->first = !$_smarty_tpl->tpl_vars['i']->index;
$__foreach_i_5_saved = $_smarty_tpl->tpl_vars['i'];
?>
						<?php $_smarty_tpl->_subTemplateRender("file:./_partial/news-item.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

					<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_5_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
			</div>
		</section>
	<?php }?>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/faq.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_home_faq']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/reviews.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['node_reviews']->value), 0, false);
?>

	<div class="g-padded--md">
		<div class="g-wrap g-wrap--xl">
			<div class="brave">
				<div class="g-wrap g-wrap--lg">
					<h2 class="g-title g-title--md g-title--color g-center js-anim">
						<?php echo $_smarty_tpl->tpl_vars['node_home_cta2']->value->subtitle;?>

					</h2>
					<div class="brave__container">
						<div class="brave__circle">
							<svg viewBox="0 0 200 200" class="brave__svg">
								<defs>
									<path id="brave__path" d="M 100,100 m -75,0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0" />
								</defs>
								<text dy="5" textLength="470">
									<textPath xlink:href="#brave__path" startOffset="0">
										<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[0]->title, ENT_QUOTES, 'UTF-8');?>
 • <?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[0]->title, ENT_QUOTES, 'UTF-8');?>
 •
									</textPath>
								</text>
							</svg>
							<img class="brave__icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/brave-circle-left.svg" alt="">
						</div>
						<div class="brave__middle">
							<p class="brave__text">
								<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[1]->title, ENT_QUOTES, 'UTF-8');?>

							</p>
							<div class="brave__line brave__line--left"></div>
						</div>
						<img class="brave__logo" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo.svg" alt="">
						<div class="brave__middle">
							<p class="brave__text">
								<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[2]->title, ENT_QUOTES, 'UTF-8');?>

							</p>
							<div class="brave__line brave__line--right"></div>
						</div>
						<div class="brave__circle brave__circle--primary">
							<svg viewBox="0 0 200 200" class="brave__svg">
								<defs>
									<path id="brave__path" d="M 100,100 m -75,0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0" />
								</defs>
								<text dy="5" textLength="470">
									<textPath xlink:href="#brave__path" startOffset="0">
										<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[3]->title, ENT_QUOTES, 'UTF-8');?>
 • <?php echo htmlspecialchars($_smarty_tpl->tpl_vars['cta2_feat']->value[3]->title, ENT_QUOTES, 'UTF-8');?>
 •
									</textPath>
								</text>
							</svg>

							<img class="brave__icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/brave-circle-right.svg" alt="">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

<?php
}
}
/* {/block "content"} */
}
