<?php 

function custom_dt_format($datetime, $format) {
	switch ($format) {
		case "tot_h": 
			$timestamp = $datetime->getTimestamp();
			return intval($timestamp / 3600);
		case "min":
			$timestamp = $datetime->getTimestamp();
			return $timestamp % (24 * 60 * 60) / 60 % 60;
		case "bg_weekday":
			$weekday = $datetime->format("N") - 1;
			return conf("convert", "weekdays", "bg", $weekday);
			case "bg_month":
				$month = $datetime->format("n") - 1;
				return conf("convert", "months", "bg", $month);
			case "en_month":
				$month = $datetime->format("n") - 1;
				return conf("convert", "months", "en", $month);
			case "ru_month":
					$month = $datetime->format("n") - 1;
					return conf("convert", "months", "ru", $month);
		default:
			return $datetime->format($format);
	}
}

function dt_format($datetime, $format) {
	preg_match_all("/\{(.+?)\}/", $format, $matches);
	if ($matches[0]) {
		$res = $format;
		for ($i = 0, $count = count($matches[0]); $i < $count; $i++) {
			$all = $matches[0][$i];
			$format = $matches[1][$i];
			$custom = custom_dt_format($datetime, $format);
			$pattern = "/" . preg_quote($all, "/") . "/u";
			$res = preg_replace($pattern, $custom, $res);
		}
		return $res;
	} else {
		return $datetime->format($format);
	}
}

function conv_date($timestamp, $type="datetime", $format="iso", $modify=null) {
	$dt = new DateTime();
	$dt->setTimestamp($timestamp);
	if ($modify) {
		$dt = $dt->modify($modify);
	}
	$format = conf("convert", "dt_formats", $type, $format);
	return dt_format($dt, $format);
}

function conv_date_string($datetime="now", $type="datetime", $format="iso", $modify=null) {
	if ($datetime == "") {
		return null;
	}
	try {
		$dt = new DateTime($datetime);
		if ($modify) {
			$dt = $dt->modify($modify);
		}
		$format = conf("convert", "dt_formats", $type, $format);
		
		return dt_format($dt, $format);
	} catch (Exception $e) {
		return null;
	}
}

function parse_seconds($seconds) {
	if ($seconds > 0) {
		return array (
				"days" => $seconds / (24 * 60 * 60),
				"hours" => $seconds % (24 * 60 * 60) / (60 * 60),
				"minutes" => $seconds % (24 * 60 * 60) / 60 % 60,
				"minutes_total" => $seconds / 60,
		);
	}
	return null;
}

function conv_filesize($size) {
	$units = array("B", "KB", "MB", "GB");
	$base = 1024;
	foreach ($units as $index=>$unit) {
		if ($size < pow($base, $index + 1) || $index == count($units) - 1) {
			$size /= pow($base, $index);
			return number_format($size, 0) . " " . $unit;
		}
	}
	return null;
}

function conv_price($price, $format="") {
	$format = conf("convert", "price_formats", $format);
	return number_format($price, $format[0], $format[1], $format[2]);
}