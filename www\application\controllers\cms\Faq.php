<?php

require_once "base/Edit_child_nodes.php";

class Faq extends Edit_child_nodes {

	const SUBITEMS = array (
		"item" => array (
			"label" => "Въпроси",
			"opts" => array("title", "text"),
		),
	);

	protected function parent_nodes() {
		return "faq";
	}
	
	protected function filters() {
		return array (
			"term" => array("type"=>"text", "label"=>"Търсене"),
		);
	}

	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);

		$this->view->set("subitems", self::SUBITEMS);
	}

	protected function resolve_subitem($id, $kind) {
		if (isset(self::SUBITEMS[$kind])) {
			return self::SUBITEMS[$kind];
		}
		return null;
	}
	
	protected function delete_object(ci_bean $object) {
		$children = $this->data->nodes->get_list(array("parent_id" => $object->id));
		foreach ($children as $i) {
			$this->data->delete($i);
		}
		parent::delete_object($object);
	}

	protected function validation_set() {
		return "subitem";
	}

	protected function public_url(ci_bean $object) {
		return "";
	}

	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		$object->kind = node::KIND_FAQ;
	}
	
	protected function objects_filter() {
		return array(
				"parent_ids" => $this->parent_ids,
				"kinds" => node::KIND_FAQ,
		);
	}
}