.near {
    width: 100%;
    color: $color-white;
    &__accent {
        position: absolute;
        width: 50%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        &:before,
        &::after {
            content: '';
            position: absolute;
            width: 50%;
            height: 1px;
            background-color: rgba($color-white, 1);
            right: 0;
        }
        &:before {
            top: 20%;
            transform: rotate(-45deg);
        }
        &::after {
            bottom: 20%;
            transform: rotate(45deg);
        }

    }

    &__accent-icon {
        width: 6rem;
    }

    &__bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        overflow: hidden;
        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color-black, 0.2);
            z-index: 1;
        }

        .slick-list,
        .slick-track {
            height: 100%;
        }
    }

    &__bg-img {
        position: relative;
        width: 100%;
        height: 100%;
        
    }

    &__img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    &__content {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 5rem;
        width: 100%;
        height: 100%;
        padding-top: 5rem;
        padding-bottom: 2.5rem;
        max-width: 40rem;
        z-index: 2;
    }

    &__center {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        max-width: 40rem;
        height: 100%;
        z-index: 2;
    }

    .g-row {
        width: 100%;
        height: 100%;
        align-items: center;
    }

    .col-1 {
        height: 100%;
    }

    &__vertical {
        height: 20rem;
        overflow: hidden;
        .slick-slide {
            opacity: 0.6;
            margin-bottom: 2.5rem !important;
        }

        .slick-center {
            opacity: 1;
        }
    }

    /* 1024 down */
    @include media(xm-down) {
        padding: 3rem 2rem;
        padding-bottom: 0;
        &__center {
            max-width: 30rem;
        }

        &__content {
            gap: 2rem;
        }

        &__accent {
            display: none;
        }
    }
}