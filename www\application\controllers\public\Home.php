<?php

require_once __DIR__ . "/../Public_base.php";

class Home extends Public_base {
	public function index() {
		$node = $this->node();

		$this->view->set("feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home"), "kinds" => "feat")));
		$this->view->set("logos", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_banks"), "kinds" => "logo")));
		$this->view->set("cta_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_cta"), "kinds" => "feat")));
		$this->view->set("process_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_process"), "kinds" => "feat")));
		$this->view->set("partner_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_partner"), "kinds" => "feat")));
		$this->view->set("partner2_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_partner2"), "kinds" => "feat")));
		$this->view->set("process2_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_process2"), "kinds" => "feat")));
		$this->view->set("members", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_why"), "kinds" => "member")));
		$this->view->set("why_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_why"), "kinds" => "feat")));
		$this->view->set("questions", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_faq"), "kinds" => "question")));
		$this->view->set("cta2_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("home_cta2"), "kinds" => "feat")));

		$this->view->set("reviews", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("reviews"), "kinds" => "review")));
		
		$benefits = $this->get_nodes("comp_clients", "comp_partners");
		$this->data->nodes->get_list_loc($this->lng->id, array("parent_ids" => array_prop($benefits, "id"), "kinds" => "row"));
		$this->view->set("benefits", $benefits);

		$services = $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("services"), "kinds" => node::KIND_SERVICE));
		$this->view->set("services", $services);

		$filter = array(
			"kinds" => node::KIND_NEWS,
			"start" => nodeid("news"),
			"min_length" => 2,
			"max_length" => 2,
			"is_featured_home" => 1,
		);
		$news = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "desc");
		$this->view->set("news", $news);

		$this->render_node("public/home", $node);
	}
}
