<?php

require_once "Base_edit.php";

abstract class Post_edit extends Base_edit {

	public function index($paging=null) {
		if (!$this->get_opt("view")) {
			redirect(conf("cms", "path"));
		}

		$paging = explode(",", $paging);
		$page_index = intval($paging[0]);
		$page_size_val = isset($paging[1]) ? intval($paging[1]) : 0;

		$sizes = conf("cms", "page_sizes");
		if (!array_key_exists($page_size_val, $sizes)) {
			$page_size_val = 0;
		}
		$page_size = intval($sizes[$page_size_val]);
		$page_size = $page_size ? $page_size : PHP_INT_MAX;

		if ($this->post_action == "delete") {
			$ids = _post("id", array());
			$this->delete($ids);
		}

		$params = $this->get_index_params();
		$filter = $params["filter"];
		$order = $params["order"];
		$dir = $params["dir"];

		$total = $this->get_count($filter);

		$paging = new Paging($total, $page_size, $page_index, 5);

		$objects = $this->get_list($filter, $order, $dir, $paging->offset(), $paging->size());

		$this->query_save($page_index, $page_size_val, _server("QUERY_STRING"));

		
		$this->view->set("page_size", $page_size_val);
		$this->view->set("total", $total);
		$this->view->set("paging", $paging);
		$this->view->set("objects", $objects);
		
		$this->on_indexing($filter, $objects);
		
		$this->view->set("index_filters", $this->filters());
		$this->view->set("index_orders", $this->orders());
		$this->view->set("index_reorder", $this->reorder_target());
		$this->view->set("index_filter", $filter);
		$this->view->set("index_order", $order);
		$this->view->set("index_dir", $dir);
		$this->view->set("query_string", _server("QUERY_STRING"));
		$this->view->set("page_sizes", $sizes);

		$this->render("cms/{$this->page_path}/index");
	}

	private function get_index_params() {
		$defaults = array();
		foreach ($this->filters() as $key=>$val) {
			if (arr($val, "range")) {
				$defaults[$key . "_from"] = arr($val, "def", "");
				$defaults[$key . "_to"] = arr($val, "def", "");
			} else {
				$defaults[$key] = arr($val, "def", "");
			}
		}

		$input = (array)_get("filter", array());
		$filter = array_merge($defaults, $input);

		$orders = array_keys($this->orders());
		$order = _get("order", "");
		$order = in_array($order, $orders) ? $order : ($orders ? $orders[0] : null);

		$dir = _get("dir", $this->direction());

		return array (
				"filter" => $filter,
				"order" => $order,
				"dir" => $dir,
		);
	}

	public function add() {
		if (!$this->get_opt("add")) {
			redirect(conf("cms", "path") . $this->page_path);
		}
		$object = $this->create_object();
		$this->on_object_load("add", $object);
		$validator = $object->validator();
		if ($this->post_action == "save" || $this->post_action == "save-close") {
			$object->set(_post_files_merge("object"));
			$this->on_object_set("add", $object);
			if ($this->validate($validator)) {
				$this->add_object($object);
				$this->add_log_entry("add", sprintf("id=%s", $object->id));
				$this->on_object_saved("add", $object);
				if ($this->post_action == "save") {
					$this->set_success($this->get_save_url($object));
				} else {
					$this->set_success($this->get_close_url());
				}
			}
		}

		$this->set_opt("delete", false);

		$this->view->set("validator", $validator);
		$this->view->set("object", $object);
		$this->view->set("object_title", $this->get_object_title($object));
		$this->view->set("action_label", "Добавяне");

		$this->render("cms/{$this->page_path}/edit");
	}

	public function edit($id) {
		if (!$this->get_opt("edit") && !$this->get_opt("view")) {
			redirect(conf("cms", "path") . $this->page_path);
		}
		$object = $this->get_object($id);
		if ($object) {
			$this->on_object_load("edit", $object);
			$validator = $object->validator();
			if ($this->get_opt("edit")) {
				if ($this->post_action == "save" || $this->post_action == "save-close") {
					$object->set(_post_files_merge("object"));
					$this->on_object_set("edit", $object);
					if ($this->validate($validator)) {
						$this->add_log_entry("edit", sprintf("id=%s", $object->id));
						$this->update_object($object);
						$this->on_object_saved("edit", $object);
						if ($this->post_action == "save") {
							$this->set_success($this->get_save_url($object));
						} else {
							$this->set_success($this->get_close_url());
						}
					}
				} else if ($this->post_action == "delete") {
					$this->delete($object->id);
				}
			}
			$this->view->set("validator", $validator);
			$this->view->set("object_title", $this->get_object_title($object));
		}
		$this->view->set("object", $object);
		$this->view->set("action_label", "Редакция");
		$this->render("cms/{$this->page_path}/edit");
	}

	private function delete($ids) {
		if (!$this->get_opt("delete")) {
			return;
		}
		$ids = (array)$ids;

		try {
			$ok = false;
			foreach($ids as $id) {
				$obj = $this->get_object($id);

				if ($obj) {
					$this->delete_object($obj);
					$this->on_deleted($obj);
					$ok = true;
				}
			}
			if ($ok) {
				$log_message = sprintf("id=%s", join(", ", $ids));
				$this->add_log_entry("delete", $log_message);
				$this->set_success($this->page_path);
			} else {
				$this->set_error($this->page_path, "Моля, изберете обект!");
			}
		} catch (CmsException $e) {
			$this->set_error($this->page_path, $e->getMessage());
		}
	}

}
