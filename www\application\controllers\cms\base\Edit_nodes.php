<?php

require_once "Post_edit.php";

abstract class Edit_nodes extends Post_edit {

	protected $languages;
	protected $lang_id;

	public function __construct() {
		parent::__construct();

		$this->config->load("nodes");
		$this->languages = $this->data->languages->all();
		$this->lang_id = conf("cms", "lang_id");

		$this->view->set("lang_id", $this->lang_id);
		$this->view->set("languages", $this->languages);
		$this->load_vars(1);
		
		$this->view->set("googlemap_key", $this->_var("googlemap-key"));
	}

	protected function reorder_target() {
		return "nodes";
	}

	protected function asset_types($node_id) {
		return array();
	}

	protected function create_object() {
		return new node();
	}
	
	protected function get_count($filter) {
		return $this->data->nodes->get_count_loc($this->lang_id, array_merge($filter, $this->objects_filter()));
	}
	
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->nodes->get_list_loc($this->lang_id, array_merge($filter, $this->objects_filter()), $order, $dir, $offset, $count);
	}
	
	protected function get_object($id) {
		$filter = $this->objects_filter();
		$filter["id"] = $id;
		$res = $this->data->nodes->get_first($filter);
		$this->data->node_locale->by_nodes($res);
		return $res;
	}

	protected function get_object_title(ci_bean $object) {
		return $object->locale($this->lang_id)->title;
	}
	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);

		$this->view->set("public_url", $this->public_url($object));

		$asset_types = call_user_func_array(array("AssetType", "resolve"), (array)$this->asset_types($object->id));
		$this->assets($object, $asset_types);
		$this->view->set("asset_types", $asset_types);
	}
	
	protected function objects_filter() {
		return array();
	}

	protected function public_url(ci_bean $object) {
		if ($object->id) {
			$res = $this->data->nodes->get_first_loc($this->lang_id, array("id"=>$object->id));
			if ($res) {
				return $this->path->base . $res->path;
			}
		}
		return "";
	}

	protected function delete_object(ci_bean $object) {
		$children = $this->data->nodes->get_list(array("parent_id" => $object->id));
		if ($children) {
			throw new CmsException("Страницата съдържа под-страници!");
		}
		$assets = $this->data->assets->by_node($object);
		foreach($assets as $obj) {
			$obj->delete_files();
		}
		$this->data->delete($object);
	}

	protected function assets(node $object, $types) {
		$current = $this->data->assets->by_node($object);

		if ($this->get_opt("edit")) {
			if ($this->post_action == "save" || $this->post_action == "save-close") {
				foreach(_post_arr("asset") as $id => $data) {
					$asset = __::find($current, function($o) use($id) { return $o->id == $id; });
					if ($asset) {
						$asset->set($data);
						$this->data->update($asset);
					}
				}

				$added = false;

				$embed = _post_arr("embed");

				foreach (array_keys($types) as $type) {
					foreach (_post_arr(array("file", $type)) as $file) {
						$file = basename($file);
						if ($this->filemanager->exists($file, "tmp")) {
							$ext = $this->filemanager->get_extension($file, "tmp");
							$size = (array)@getimagesize($this->filemanager->get_file($file, "tmp", true));
							$this->filemanager->move($file, "tmp");
							
							$asset = new asset();
							$asset->node_id = $object->id;
							$asset->type = AssetType::resolve_ext($type, $ext);

							$files = _session(conf("fileupload_key"), array());
							$fileinfo = arr($files, $file, "");
							unset($files[$file]);
							set_session(conf("fileupload_key"), $files);

							$asset->width = arr($size, 0, 0);
							$asset->height = arr($size, 1, 0);

							$asset->file = json_encode(array(
								"name" => $file,
								"type" => $fileinfo["type"],
								"size" => $fileinfo["size"],
								"orig_name" => $fileinfo["name"],
							));

							if ($asset->validate()) {
								$this->data->add($asset);
								$added = true;
							} else {
								$this->filemanager->delete($file);
							}
						}
					}

					if (isset($embed[$type])) {
						$asset = new asset();
						$asset->set($embed[$type]);
						$asset->node_id = $object->id;
						$asset->type = AssetType::EMBED;

						if ($asset->validate()) {
							$this->data->add($asset);
							$added = true;
						}
					}
				}

				if ($added) {
					$this->add_log_entry("add_assets", sprintf("id=%s", $object->id));
				}
			} elseif ($this->post_action == "delete-asset") {
				foreach(_post_arr("id") as $id) {
					$obj = $this->data->assets->id($id);

					if ($obj) {
						$this->data->delete($obj);
					}
				}

				$this->add_log_entry("delete_assets", sprintf("id=%s", $object->id));
				$this->set_success($this->get_save_url($object));
			}
		}
	}
	
	public function reorder() {
		$TARGETS = array("nodes", "assets");
		
		if ($this->input->is_ajax_request()) {
			$target = _post("target", "");
			$dir = _post("dir", "asc");
			$ids = _post_arr("id");
			$parent = _post("parent");
			$kind = _post("kind");
			
			if (in_array($target, $TARGETS)) {
				$filter = array();
				if ($parent) {
					$filter = array("parent_id" => $parent);
					$filter["kinds"] = $kind;
				} else {
					$filter = $this->objects_filter();
					$filter["ids"] = $ids;
				}
				$objects = $this->data->$target->get_list($filter, "position", $dir);
				$old_positions = array();
				foreach($objects as $object) {
					$old_positions[$object->get_id_string()] = $object->position;
				}
				$old_positions = array_values($old_positions);
				foreach($ids as $index=>$id) {
					$object = __::find($objects, function($o) use($id) { return $o->id == $id; });
					if ($object && isset($old_positions[$index])) {
						$object->position = $old_positions[$index];
						$this->data->update($object);
					}
				}
			} else {
				$this->output->append_output("Unknown target: $target");
			}
		}
	}

	protected function resolve_subitem($id, $kind) {
		return array();
	}

	public function subitems($object_id, $kind, $method) {
		$subitem = $this->resolve_subitem($object_id, $kind);
		if ($subitem) {
			$validator = arr($subitem, "validator", "subitem");
			$label = $subitem["label"];
			$opts =  $subitem["opts"];

			switch ($method) {
				case "list": return $this->subitems_list($kind, $object_id, $opts);
				case "add": return $this->subitems_add($kind, $object_id, $validator);
				case "edit": return $this->subitems_edit($object_id, $opts);
				case "cancel": return $this->subitems_cancel($object_id, $opts);
				case "update": return $this->subitems_update($object_id, $validator, $opts);
				case "delete": return $this->subitems_delete($object_id);
			}
		}
	}


	protected function get_subitem_parent($id) {
		return $this->get_object($id);
	}

	private function subitems_list($kind, $object_id, $opts) {
		if ($this->input->is_ajax_request()) {
			$model = array();
			$model["items"] = null;
			$model["opts"] = $opts;

			if ($object_id) {
				$object = $this->get_subitem_parent($object_id);
				if ($object) {
					$where = $this->data->prep("kind=?", $kind);
					$filter = array(
							"parent_id" => $object->id,
							"kinds" => $kind,
					);
					$res = $this->data->nodes->get_list($filter);
					$this->data->node_locale->by_nodes($res);
					$model["items"] = $res;
				}
			}
			$this->view->set("subitems", $model);
			$res = $this->view->result("cms/_partial/subitems", true);

			$this->json_response($res);
		}
	}

	private function subitems_render($item_id, $mode, $opts) {
		$item = $this->data->nodes->get_first(array("id" => $item_id));
		$this->data->node_locale->by_nodes($item);

		if ($item) {
			$model = array(
				"item" => $item,
				"mode" => $mode,
				"opts" => $opts,
			);
			$this->view->set("subitems", $model);
			return $this->view->result("cms/_partial/subitems", true);
		}
		return "";
	}


	private function subitems_add($kind, $object_id, $set) {
		if ($this->get_opt("edit") && $this->input->is_ajax_request()) {
			$object = $this->get_subitem_parent($object_id);
			if ($object) {
				$item = new node();
				$item->set(_post_files_merge());
				$item->parent_id = $object->id;
				$item->kind = $kind;

				$validator = $item->validator();
				if ($validator->validate($set)) {
					$this->data->add($item);

					$res = array(
							"success" => true,
					);

				} else {
					$res = array(
							"success" => false,
							"data" => $validator->errors(),
					);
				}
				$this->json_response($res);
			}
		}
	}

	private function subitems_edit($object_id, $opts) {
		if ($this->get_opt("edit") && $this->input->is_ajax_request()) {
			$object = $this->get_subitem_parent($object_id);
			if ($object) {
				$item_id = _post("id");

				$this->json_response($this->subitems_render($item_id, "item_edit", $opts));
			}
		}
	}

	private function subitems_cancel($object_id, $opts) {
		if ($this->input->is_ajax_request()) {
			$object = $this->get_subitem_parent($object_id);
			if ($object) {
				$item_id = _post("id");

				$this->json_response($this->subitems_render($item_id, "item_view", $opts));
			}
		}
	}

	private function subitems_update($object_id, $set, $opts) {
		if ($this->get_opt("edit") && $this->input->is_ajax_request()) {

			$object = $this->get_subitem_parent($object_id);
			if ($object) {
				$item_id = _post("id");
				$item = $this->data->nodes->get_first(array(
						"id" => $item_id,
						"parent_id" => $object_id,
				));
				$this->data->node_locale->by_nodes($item);
				if ($item) {
					$item->set(_post_files_merge());
					$validator = $item->validator();
					if ($validator->validate($set)) {
						$this->data->update($item);

						$res = array(
								"success" => true,
								"data" => $this->subitems_render($item->id, "item_view", $opts),
						);

					} else {
						$res = array(
								"success" => false,
								"data" => $validator->errors(),
						);
					}
					$this->json_response($res);
				}
			}

		}
	}

	private function subitems_delete($object_id) {
		if ($this->get_opt("edit") && $this->input->is_ajax_request()) {
			$object = $this->get_subitem_parent($object_id);
			if ($object) {
				$res = false;
				$item_id = _post("id");
				$item = $this->data->nodes->get_first(array(
						"id" => $item_id,
						"parent_id" => $object_id,
				));
				
				if ($item) {
					$this->data->delete($item);
					$res = true;
				}
				$this->json_response($res);
			}
		}
	}
}