<?php

class QrcodeCtrl extends CI_Controller {

	private $cachePath = "/qrcode";

	public function __construct() {
		parent::__construct();

		$this->load->library("thumbnails");
		require_once($this->path->abs_app."/third_party/qr/qrlib.php");

		$this->cachePath = $this->path->abs_cache . $this->cachePath;

		if (!file_exists($this->cachePath)) {
			assert(!is_file($this->cachePath));

			my_mkdir($this->cachePath);
		}
	}


	public function index() {
		$url = $this->input->get("url");
		$size = intval($this->input->get("s"));

		if (!$size) {
			$size = 5;
		}
		$size = min($size, 5);
		$ok = false;
		if ($url) {
			$file = sha1($url . "size=" . $size);
			$cache = $this->cachePath . "/" . $file;

			if (!is_file($cache)) {
				QRcode::png($url, $cache, QR_ECLEVEL_M, $size, 1);
			}


			$res = file_get_contents($cache);

			header("Content-type: image/png");
			header(sprintf('Content-Disposition: filename="%s"', $file. ".png"));
			readfile($cache);
		} else {
			$this->thumbnails->send_empty();
		}

	}
}