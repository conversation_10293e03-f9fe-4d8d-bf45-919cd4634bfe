<?php

require_once "base/Post_edit.php";

class Administrators extends Post_edit {

	public function __construct() {
		parent::__construct();

		$this->view->set("sections", conf("cms", "menu"));
		$this->view->set("rights", conf("cms", "rights"));
	}

	protected function get_count($filter) {
		$filter = array(
				"role_ids" => conf("cms", "role"), 
		);
		return $this->data->users->get_count($filter);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		$filter = array(
				"role_ids" => conf("cms", "role"),
		);
		return $this->data->users->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function get_object($id) {
		$filter = array(
				"id" => $id,
				"role_ids" => conf("cms", "role"), 
		);
		return $this->data->users->get_first($filter);
	}

	protected function get_object_title(ci_bean $object) {
		return $object->email;
	}

	protected function validation_set() {
		return "admin";
	}

	protected function create_object() {
		return new user();
	}

	protected function delete_object(ci_bean $object) {
		if ($object->id == $this->usession->user_id) {
			throw new CmsException("Не можете да изтриете себе си!");
		}
		$this->data->delete($object);
	}

	protected function on_object_load($action, ci_bean $object) {
		$is_current = $object->id == $this->usession->user_id;
		$this->view->set("is_current", $is_current);
		$this->set_opt("delete", !$is_current);
	}

	protected function on_object_saved($action, ci_bean $object) {
		parent::on_object_saved($action, $object);

		if ($action == "add") {
			$this->data->user_roles->link($object->id, conf("cms", "role"));
		}

		$this->update_permissions($object);
	}

	private function update_permissions($object) {
		$post = _post("permissions");
		if ($post) {
			$res = array();
			foreach (conf("cms", "menu") as $main) {
				foreach ($main["items"] as $sub) {
					if ($sub) {
						$res[$sub["url"]] = arr($post, $sub["url"]);
					}
				}
				if ($object->id == $this->usession->user()->id) {
					$res["administrators"] = "full";
				}
			}
			$object->permissions = $res;
			$this->data->update($object);
		}
	}
}
