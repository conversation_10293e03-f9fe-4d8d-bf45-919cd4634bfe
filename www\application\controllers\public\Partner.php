<?php

require_once __DIR__ . "/../Public_base.php";

class Partner extends Public_base {
	public function index() {
		$node = $this->node();
		
		$this->view->set("feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner"), "kinds" => "feat")));
		$this->view->set("become_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner_become"), "kinds" => "feat")));
		$this->view->set("exp_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner_exp"), "kinds" => "feat")));
		$this->view->set("process_feat", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner_process"), "kinds" => "feat")));
		$this->view->set("questions", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner_faq"), "kinds" => "question")));
		$this->view->set("reviews", $this->data->nodes->get_list_loc($this->lng->id, array("parent_id" => nodeid("partner_reviews"), "kinds" => "review")));

		$benefits = $this->get_nodes("comp_clients", "comp_partners");
		$this->data->nodes->get_list_loc($this->lng->id, array("parent_ids" => array_prop($benefits, "id"), "kinds" => "row"));
		$this->view->set("benefits", $benefits);

		$this->render_node("public/partner", $node);
	}
}
