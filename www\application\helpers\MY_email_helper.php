<?php

function send_email($subject, $message, $recipient, $sender="", $format="", $attachments=array()) {
	$CI =& get_instance();
	$CI->config->load("email");

	$config = config_item("email");
	if ($config["ms_api"]) {
		send_email_ms($subject, $message, $recipient, $sender);
	} else {
		send_email_ci($subject, $message, $recipient, $sender, $format, $attachments);
	}
}


function send_email_ci($subject, $message, $recipient, $sender="", $format="", $attachments=array()) {
	$CI =& get_instance();

	$CI->load->library("email");
	$CI->email->clear(true);

	$config = config_item("email");
	$config["mailtype"] = ($format == "text" ? $format : "html");

	$sender = ($sender ? $sender : $config["default_sender"]);
	$sender_name = $config["default_sender_name"];

	$CI->email->initialize($config);

	$CI->email->to($recipient);
	$CI->email->from($sender, $sender_name);
	$CI->email->subject($subject);
	$CI->email->message($message);

	foreach ($attachments as $a) {
		$CI->email->attach($a);
	}

	$CI->email->send();
}

function send_email_ms($subject, $content, $recipient, $sender) {
	$config = config_item("email");
	$params = 'client_id=' . $config["ms_client_id"] . '&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&client_secret=' . $config["ms_client_secret"] . '&grant_type=client_credentials';
	$res = http_post('https://login.microsoftonline.com/' . $config["ms_tenant_id"] . '/oauth2/v2.0/token', $params);

	$token = $res->access_token;

	$recipients = array();
	foreach ((array)$recipient as $email) {
		$recipients[] = array("emailAddress" => array('address' => $email));
	}
	$message = array(
		"message" => array(
			'subject' => $subject,
			'toRecipients' => $recipients,
			'body' => array(
				'contentType' => 'HTML',
				'content' => $content,
			),
		)
	);
	$url = 'https://graph.microsoft.com/v1.0/users/' . $sender . '/sendMail';
	http_post($url, json_encode($message), $token, true);
}

function http_post($url, $params, $token = "", $json = false) {
	$ch = curl_init($url);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
	$headers = array();
	if ($json) {
		$headers = array(
			'Content-type: application/json',
		);
	}
	if ($token) {
		$headers[] = 'Authorization: Bearer ' . $token;
	}
	curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	$res = curl_exec($ch);
	curl_close($ch);
	return json_decode($res);
}