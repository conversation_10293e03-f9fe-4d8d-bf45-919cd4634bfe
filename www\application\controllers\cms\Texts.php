<?php

require_once "base/Post_edit.php";

class Texts extends Post_edit {

	const FILTER = array("groups" => "text");

	protected function opts() {
		return array(
			"add"=>false,
			"delete"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->variables->get_count(self::FILTER);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->variables->get_list(self::FILTER);
	}

	public function on_indexing($filter, array $objects) {
		$this->view->set("languages", $this->data->languages->all());
		
		if ($this->get_opt("edit") && $this->post_action == "save") {

			foreach($objects as $object) {
				$langs =_post(array("lang", $object->id), array());
				$value = _post(array("value", $object->id), array());
				foreach ($langs as $lang_id) {
					$locale = $object->locale($lang_id);
					$v = arr($value, $locale->language_id);
					$locale->value = $v;

				}
				$this->data->update($object);
			}
			$this->add_log_entry("edit", "");
			$this->set_success();
		}
	}
}