/**********************************************************
 * Force an element to contain its children
**********************************************************/
.g-clearfix::after, .g-grid::after { 
	clear: both; 
	content: " "; 
	display: block; 
	font-size: 0; 
	height: 0; 
	visibility: hidden; 
}
* html .g-clearfix, * html .g-grid { zoom: 1; } /* IE6 */
*:first-child+html .g-clearfix, *:first-child+html .g-grid { zoom: 1; } /* IE7 */