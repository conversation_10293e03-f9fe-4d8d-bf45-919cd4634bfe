<?php

require_once "base/Post_edit.php";

class Custom_codes extends Post_edit {

	protected function get_count($filter=null) {
		return $this->data->custom_codes->get_count($filter);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->custom_codes->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function get_object($id) {
		$filter = array("id" => $id);
		return $this->data->custom_codes->get_first($filter);
	}

	protected function get_object_title(ci_bean $object) {
		return $object->title;
	}

	protected function create_object() {
		return new custom_code();
	}

	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"is_active" => array("type"=>"checkbox", "label"=>"Активен", "def"=>-1),
				"has_head" => array("type"=>"checkbox", "label"=>"Head", "def"=>-1),
				"has_body_start" => array("type"=>"checkbox", "label"=>"Body - начало", "def"=>-1),
				"has_body_end" => array("type"=>"checkbox", "label"=>"Body - край", "def"=>-1),
		);
	}
}