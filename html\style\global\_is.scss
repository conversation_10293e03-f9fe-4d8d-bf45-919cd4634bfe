/**********************************************************
 * Show/hide elements from a breakpoint up
**********************************************************/

.is-hidden {
    display: none !important;
}

@include media(sm) {
    .is-inline--sm {
        display: inline-block !important;
    }
    .is-block--sm {
        display: block !important;
    }
    .is-flex--sm {
        display: flex !important;
    }
    .is-inline-flex--sm {
        display: inline-flex !important;
    }
    .is-hidden--sm {
        display: none !important;
    }
}

@include media(md) {
    .is-inline--md {
        display: inline-block !important;
    }
    .is-block--md {
        display: block !important;
    }
    .is-flex--md {
        display: flex !important;
    }
    .is-inline-flex--md {
        display: inline-flex !important;
    }
    .is-hidden--md {
        display: none !important;
    }
}

@include media(xm) {
    .is-inline--xm {
        display: inline-block !important;
    }    
    .is-block--xm {
        display: block !important;
    }
    .is-flex--xm {
        display: flex !important;
    }    
    .is-inline-flex--xm {
        display: inline-flex !important;
    }
    .is-hidden--xm {
        display: none !important;
    }
}

@include media(lg) {
    .is-inline--lg {
        display: inline-block !important;
    }    
    .is-block--lg {
        display: block !important;
    }
    .is-flex--lg {
        display: flex !important;
    }
    .is-inline-flex--lg {
        display: inline-flex !important;
    }
    .is-hidden--lg {
        display: none !important;
    }
}