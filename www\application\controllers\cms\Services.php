<?php

require_once "base/Edit_child_nodes.php";

class Services extends Edit_child_nodes {

	private $services;
	
	const SUBITEMS = array (
	);

	const SUBNODES = array(
		"feat" => array(
			"titles" => array(
				1 => "Акценти",
				2 => "Features",
			),
			"opts" => array("image"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "icon"),
				),
			),
		),
		"params" => array(
			"titles" => array(
				1 => "Основни параметри за кредитиране",
				2 => "Basic lending parameters",
			),
			"opts" => array(),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "subtitle", "text"),
				),
			),
		),
		"types" => array(
			"titles" => array(
				1 => "видове кредити",
				2 => "types of loans",
			),
			"opts" => array("subtitle"),
			"subitems" => array(
				"textblock" => array(
					"label" => "Текстови блокове",
					"opts" => array("title", "text", "image", "link_title", "link_loc"),
				),
			),
		),
		"client" => array(
			"titles" => array(
				1 => "пътят на клиента",
				2 => "the customer journey",
			),
			"opts" => array("subtitle"),
			"subitems" => array(
				"feat" => array(
					"label" => "Акценти",
					"opts" => array("title", "text", "image", "icon"),
				),
			),
		),
		"faq" => array(
			"titles" => array(
				1 => "Често задавани въпроси",
				2 => "Frequently Asked Questions",
			),
			"opts" => array("subtitle", "text_plain"),
			"subitems" => array(
				"question" => array(
					"label" => "Въпроси",
					"opts" => array("title", "text"),
				),
			),
		),
	);
	
	protected function parent_nodes() {
		return "services";
	}
	
	protected function filters() {
		return array (
			"term" => array("type"=>"text", "label"=>"Търсене"),
		);
	}
	
	protected function get_count($filter) {
		return $this->data->nodes->get_count_loc($this->lang_id, array_merge($filter, $this->objects_filter()));
	}
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->nodes->get_list_loc($this->lang_id, array_merge($filter, $this->objects_filter()), $order, $dir, $offset, $count);
	}

	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);

		$this->data->node_links->by_parent($object->id);

		$this->view->set("subitems", self::SUBITEMS);

		$this->services = $this->data->nodes->get_list_loc($this->lang_id, array(
			"parent_id" => nodeid("services"),
			"kinds" => node::KIND_SERVICE,
			"not_id" => $object->id,
		), "title");
		$this->view->set("services", $this->services);

		if ($action == "edit") {
			$filter = array("parent_id" => $object->id, "kinds" => "block");
			$blocks = $this->data->nodes->get_list($filter);
			$this->data->node_locale->by_nodes($blocks);

			foreach (self::SUBNODES as $slug=>$info) {
				$found = __::find($blocks, function($o) use ($slug) {
					return $o->locale(1)->slug == $slug;
				});
				if (!$found) {
					$this->data->nodes->add_child_block($object->id, $slug, $info);
				}
			}

			$filter = array("parent_id" => $object->id, "kinds" => "block");
			$blocks = $this->data->nodes->get_list($filter);
			
			$this->data->node_locale->by_nodes($blocks);

			$opts = array();
			foreach ($blocks as $i) {
				$opts[$i->id] = self::SUBNODES[$i->locale(1)->slug];
			}

			$this->view->set("blockopts", $opts);
			$this->view->set("blocks", $blocks);
		}
	}

	protected function on_object_saved($action, ci_bean $object) {
		parent::on_object_saved($action, $object);

		if ($action == "add") {
			$this->data->nodes->add_children_blocks($object->id, self::SUBNODES);
		}

		$blocks = $object->children("block");
		foreach ($blocks as $i) {
			$data = _post_files_merge(array("block", $i->id));
			if ($data) {
				$i->set($data);
				$this->data->update($i);
			}
		}

		$this->data->node_links->update($object->id, array_prop($this->services, "id"), $object->linked_ids(), _post_arr("linked"));

	}

	protected function resolve_subitem($id, $kind) {
		$object = $this->data->nodes->get_first_loc(1, array("id" => $id));
		$res = arr(self::SUBNODES, array($object->slug, "subitems", $kind));
		if ($res) {
			return $res;
		}
		if (isset(self::SUBITEMS[$kind])) {
			return self::SUBITEMS[$kind];
		}
		return null;
	}
	
	protected function delete_object(ci_bean $object) {
		$children = $this->data->nodes->get_list(array("parent_id" => $object->id));
		foreach ($children as $i) {
			$this->data->delete($i);
		}
		parent::delete_object($object);
	}

	protected function get_subitem_parent($id) {
		$res = $this->get_object($id);
		if (!$res) {
			$obj = $this->data->nodes->get_first_loc(1, array("id" => $id));
			if ($obj && $this->get_object($obj->parent_id)) {
				$res = $obj;
			}
		}
		return $res;
	}

	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		$object->kind = node::KIND_SERVICE;
	}
	
	protected function objects_filter() {
		return array(
				"parent_ids" => $this->parent_ids,
				"kinds" => node::KIND_SERVICE,
		);
	}
}