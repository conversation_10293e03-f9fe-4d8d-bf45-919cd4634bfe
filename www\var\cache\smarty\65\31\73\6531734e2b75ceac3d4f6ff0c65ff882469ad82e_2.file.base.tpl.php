<?php
/* Smarty version 3.1.30, created on 2025-07-04 13:52:22
  from "W:\work\dvm_finance\www\application\views\public\base.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_6867b2669585f7_07297858',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6531734e2b75ceac3d4f6ff0c65ff882469ad82e' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\base.tpl',
      1 => 1751625733,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:public/_partial/custom_codes.tpl' => 3,
    'file:public/_partial/header.tpl' => 1,
    'file:public/_partial/footer.tpl' => 1,
    'file:public/_partial/ld.tpl' => 1,
  ),
),false)) {
function content_6867b2669585f7_07297858 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_function_webresources')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\function.webresources.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
if (!is_callable('smarty_function_stats')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\function.stats.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, false);
?>
<!DOCTYPE html>
<html prefix="og: http://ogp.me/ns#" lang="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['lng']->value->code, ENT_QUOTES, 'UTF-8');?>
" data-root="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['app_root']->value, ENT_QUOTES, 'UTF-8');?>
" data-vars="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['js_vars']->value, ENT_QUOTES, 'UTF-8');?>
">
<head>
	<title><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['html_meta']->value['title'], ENT_QUOTES, 'UTF-8');?>
</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0" />
<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['html_meta']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->key => $_smarty_tpl->tpl_vars['i']->value) {
$__foreach_i_0_saved = $_smarty_tpl->tpl_vars['i'];
?>
	<meta name="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->key, ENT_QUOTES, 'UTF-8');?>
" content="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value, ENT_QUOTES, 'UTF-8');?>
" />
<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_0_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['html_og']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->key => $_smarty_tpl->tpl_vars['i']->value) {
$__foreach_i_1_saved = $_smarty_tpl->tpl_vars['i'];
?>
	<meta property="og:<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->key, ENT_QUOTES, 'UTF-8');?>
" content="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value, ENT_QUOTES, 'UTF-8');?>
" />
<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_1_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['languages']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->key => $_smarty_tpl->tpl_vars['i']->value) {
$__foreach_i_2_saved = $_smarty_tpl->tpl_vars['i'];
?>
	<link rel="alternate" href="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['app_root_abs']->value, ENT_QUOTES, 'UTF-8');
echo htmlspecialchars((($tmp = @$_smarty_tpl->tpl_vars['lang_paths']->value[$_smarty_tpl->tpl_vars['i']->value->id])===null||$tmp==='' ? '' : $tmp), ENT_QUOTES, 'UTF-8');?>
" hreflang="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->locale, ENT_QUOTES, 'UTF-8');?>
">
<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_2_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['html_link']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->key => $_smarty_tpl->tpl_vars['i']->value) {
$__foreach_i_3_saved = $_smarty_tpl->tpl_vars['i'];
?>
	<link rel="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->key, ENT_QUOTES, 'UTF-8');?>
" href="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value, ENT_QUOTES, 'UTF-8');?>
" />
<?php
$_smarty_tpl->tpl_vars['i'] = $__foreach_i_3_saved;
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

	<?php echo smarty_function_webresources(array('bundle'=>"public_css"),$_smarty_tpl);?>

	<?php echo smarty_function_webresources(array('bundle'=>"public_js"),$_smarty_tpl);?>

	<?php if ($_smarty_tpl->tpl_vars['favicon']->value) {?>
		<link rel="icon" type="image/png" sizes="16x16" href="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['favicon']->value,'favicon16','var'), ENT_QUOTES, 'UTF-8');?>
">
		<link rel="icon" type="image/png" sizes="32x32" href="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['favicon']->value,'favicon32','var'), ENT_QUOTES, 'UTF-8');?>
">
		<link rel="icon" type="image/png" sizes="192x192" href="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['favicon']->value,'favicon192','var'), ENT_QUOTES, 'UTF-8');?>
">
		<link rel="shortcut icon" href="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['favicon']->value,'favicon','var'), ENT_QUOTES, 'UTF-8');?>
" />
	<?php }?>
	<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/custom_codes.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('prop'=>"head"), 0, false);
?>

</head>
<body>
	<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/custom_codes.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('prop'=>"body_start"), 0, true);
?>

	<div class="g-wrap--main <?php echo htmlspecialchars((($tmp = @$_smarty_tpl->tpl_vars['wrapper_css']->value)===null||$tmp==='' ? '' : $tmp), ENT_QUOTES, 'UTF-8');?>
">
		<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/header.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

		<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_14751138446867b266954772_03351172', "content");
?>

		<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/footer.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	</div>
	<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/custom_codes.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('prop'=>"body_end"), 0, true);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:public/_partial/ld.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php if ($_smarty_tpl->tpl_vars['show_stats']->value) {?>
		<p id="stats"><?php echo smarty_function_stats(array(),$_smarty_tpl);?>
</p>
	<?php }?>
</body>
</html><?php }
/* {block "content"} */
class Block_14751138446867b266954772_03351172 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
}
}
/* {/block "content"} */
}
