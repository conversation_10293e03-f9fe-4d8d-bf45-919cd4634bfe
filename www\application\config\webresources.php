<?php

$config["webresources_minify"] = false;

$config["webresources"] = array (
		"public_css" => array (
				"resource/public/style/fonts.css",
				"resource/shared/style/reset.css",
				"resource/shared/style/lib.scss",
				"resource/vendor/style/jquery.simple-popup.css",
				"resource/vendor/style/jquery.simple-popup.settings.css",
				"resource/public/style/main.scss",
		),
		"public_js" => array (
				"resource/vendor/javascript/jquery-3.6.0.min.js",
				"resource/vendor/javascript/underscore.js",
				"resource/vendor/javascript/sprintf.min.js",
				"resource/vendor/javascript/js.cookie.min.js",
				"resource/vendor/javascript/sweetalert2.all.js",
				"resource/vendor/javascript/jquery.simple-popup.js",
				"resource/vendor/javascript/anime.min.js",
				"resource/vendor/javascript/autosize.min.js",
				"resource/vendor/javascript/gsap.min.js",
				"resource/vendor/javascript/ion.rangeSlider.min.js",
				"resource/vendor/javascript/jquery.waypoints.min.js",
				"resource/vendor/javascript/ScrollTrigger.min.js",
				"resource/vendor/javascript/slick.min.js",
				"resource/vendor/javascript/venobox.min.js",
				"resource/shared/javascript/lib.js",
				"resource/shared/javascript/app.js",
				"resource/shared/javascript/jquery.validate-form.js",
				"resource/public/javascript/googlemap_style.js",
				"resource/public/javascript/googlemap.js",
				"resource/public/javascript/animations.js",
				"resource/public/javascript/main.js",
		),
		"cms_css" => array (
				"resource/cms/_[skin]/style/fonts.css",
				"resource/shared/style/reset.css",
				"resource/shared/style/lib.scss",
				"resource/vendor/style/jquery-ui-1.11.2.custom.css",
				"resource/vendor/style/flatpickr.min.css",
				"resource/vendor/style/jquery.ui.timepicker.css",
				"resource/vendor/style/jquery.colorpicker.css",
				"resource/vendor/style/jquery.chosen.css",
				"resource/vendor/style/jquery.simple-popup.css",
				"resource/vendor/style/jquery.simple-popup.settings.css",
				"resource/vendor/style/select2.min.css",
				"resource/vendor/style/prettyPhoto.css",
				"resource/cms/_[skin]/style/main.scss",
		),
		"cms_js" => array (
			"resource/vendor/javascript/jquery-3.6.0.min.js",
				"resource/vendor/javascript/underscore.js",
				"resource/vendor/javascript/sprintf.min.js",
				"resource/vendor/javascript/js.cookie.min.js",
				"resource/vendor/javascript/jquery-ui-1.11.2.custom.min.js",
				"resource/vendor/javascript/flatpickr.js",
				"resource/vendor/javascript/flatpickr-bg.js",
				"resource/vendor/javascript/jquery.chosen.js",
				"resource/vendor/javascript/jquery.fileupload.js",
				"resource/vendor/javascript/jquery.fileupload-process.js",
				"resource/vendor/javascript/jquery.fileupload-validate.js",
				"resource/vendor/javascript/jquery.hotkeys.js",
				"resource/vendor/javascript/jquery.tablesorter.js",
				"resource/vendor/javascript/highcharts.js",
				"resource/vendor/javascript/jquery.ui.timepicker.js",
				"resource/vendor/javascript/jquery.colorpicker.js",
				"resource/vendor/javascript/jquery.prettyPhoto.js",
				"resource/vendor/javascript/sweetalert2.all.js",
				"resource/vendor/javascript/jquery.simple-popup.js",
				"resource/vendor/javascript/select2.min.js",
				"resource/shared/javascript/jquery.filednd.js",
				"resource/shared/javascript/app.js",
				"resource/cms/javascript/tinymce_preinit.js",
				"resource/vendor/tinymce/tinymce.min.js",
				"resource/vendor/tinymce/jquery.tinymce.min.js",
				"resource/shared/javascript/lib.js",
				"resource/shared/javascript/jquery.validate-form.js",
				"resource/cms/javascript/tinymce_config.js",
				"resource/cms/javascript/main.js",
				"resource/cms/javascript/location.js",
				"resource/cms/javascript/grid_filter.js",
				"resource/cms/javascript/tabs.js",
				"resource/cms/javascript/timer.js",
				"resource/cms/javascript/permissions.js",
				"resource/cms/javascript/settings.js",
				"resource/cms/javascript/graph.js",
				"resource/cms/javascript/ajax-grid.js",
				"resource/cms/javascript/sort.js",
				"resource/cms/javascript/validate.js",
				"resource/cms/javascript/shortcuts.js",
				"resource/cms/javascript/globals.js",
		),

		"login_css" => array (
				"resource/cms/_[skin]/style/fonts.css",
				"resource/shared/style/reset.css",
				"resource/shared/style/lib.scss",
				"resource/cms/_[skin]/style/login.scss"
		),
		"login_js" => array (
				"resource/vendor/javascript/jquery-3.6.0.min.js",
				"resource/vendor/javascript/underscore.js",
				"resource/vendor/javascript/sweetalert2.all.js",
				"resource/shared/javascript/lib.js",
				"resource/shared/javascript/jquery.validate-form.js",
				"resource/shared/javascript/app.js",
				"resource/cms/javascript/login.js",
		),
);

$config["webresources_icons"] = array(
	"public" => "resource/public/image/favicon.ico",
	"cms" => "resource/cms/_[skin]/image/favicon.ico",
);

if (function_exists("merge_env")) {
	merge_env(__FILE__, $config);
}
