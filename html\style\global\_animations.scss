/**********************************************************
 * Fade-in animations
**********************************************************/
.word { display: inline-block; white-space: nowrap; }
.letter { display: inline-block; opacity: 0; }

.js-anim,
.js-anim-rev,
.js-stagger-item { 
	opacity: 0; 
}

/**********************************************************
 * Dealing with display:none transition nightmares
**********************************************************/
@keyframes display {
	0% { display: none; opacity: 0; }
	1% { display: block; opacity: 0; }
	100% { display: block; opacity: 1; }
}


@keyframes display-flex {
	0% { display: none; opacity: 0; }
	1% { display: flex; opacity: 0; }
	100% { display: flex; opacity: 1; }
}