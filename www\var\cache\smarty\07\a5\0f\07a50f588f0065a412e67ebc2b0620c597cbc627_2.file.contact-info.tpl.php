<?php
/* Smarty version 3.1.30, created on 2025-07-05 18:51:01
  from "W:\work\dvm_finance\www\application\views\public\_partial\contact-info.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686949e5324060_91836690',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '07a50f588f0065a412e67ebc2b0620c597cbc627' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\_partial\\contact-info.tpl',
      1 => 1751730529,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_686949e5324060_91836690 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
if (!is_callable('smarty_function_var')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\function.var.php';
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_url')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.url.php';
?>
<div class="g-row g-gap-1 g-gap-v-2">
	<div class="col-1 col-md-1-2">
		<div class="contacts__item">
			<p class="contacts__label"><?php echo smarty_modifier_dict("misc.address");?>
</p>
			<p class="contacts__text g-color-primary g-mb-15">
				<?php ob_start();
echo smarty_function_var(array(0=>"address",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);
$_prefixVariable10=ob_get_clean();
echo smarty_modifier_pt(($_prefixVariable10));?>

			</p>
			<?php ob_start();
echo smarty_function_var(array(0=>"map-link",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);
$_prefixVariable11=ob_get_clean();
if ($_prefixVariable11) {?>
				<a class="g-opacity-60 g-font-15 g-link btn" href="<?php ob_start();
echo smarty_function_var(array(0=>"map-link",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);
$_prefixVariable12=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable12)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
					rel="noopener noreferrer nofollow">
					<i class="icon icon--roadmap"></i>
					<?php echo smarty_modifier_dict("misc.map");?>

				</a>
			<?php }?>
		</div>
	</div>
	<div class="col-1 col-md-1-2">
		<div class="contacts__item">
			<p class="contacts__label"><?php echo smarty_modifier_dict("misc.write");?>
</p>
			<a href="mailto:<?php echo smarty_function_var(array(0=>"email",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>
" class="contacts__text underline g-color-primary">
				<?php echo smarty_function_var(array(0=>"email",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>

			</a>
		</div>
	</div>
	<div class="col-1 col-md-1-2">
		<div class="contacts__item">
			<p class="contacts__label"><?php echo smarty_modifier_dict("misc.phone");?>
</p>
			<a href="tel:<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>
" class="contacts__text underline g-color-primary">
				<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>

			</a>
		</div>
	</div>
	<div class="col-1 col-md-1-2">
		<div class="contacts__item">
			<p class="contacts__label"><?php echo smarty_modifier_dict("misc.social");?>
</p>
			<ul class="contacts__socials">
				<?php ob_start();
echo smarty_function_var(array(0=>"facebook"),$_smarty_tpl);
$_prefixVariable13=ob_get_clean();
if ($_prefixVariable13) {?>
					<li>
						<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"facebook"),$_smarty_tpl);
$_prefixVariable14=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable14)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
							rel="noopener noreferrer nofollow">
							<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/facebook.svg" alt="">
						</a>
					</li>
				<?php }?>
				<?php ob_start();
echo smarty_function_var(array(0=>"instagram"),$_smarty_tpl);
$_prefixVariable15=ob_get_clean();
if ($_prefixVariable15) {?>
					<li>
						<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"instagram"),$_smarty_tpl);
$_prefixVariable16=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable16)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
							rel="noopener noreferrer nofollow">
							<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/instagram.svg" alt="">
						</a>
					</li>
				<?php }?>
				<?php ob_start();
echo smarty_function_var(array(0=>"tiktok"),$_smarty_tpl);
$_prefixVariable17=ob_get_clean();
if ($_prefixVariable17) {?>
					<li>
						<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"tiktok"),$_smarty_tpl);
$_prefixVariable18=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable18)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
							rel="noopener noreferrer nofollow">
							<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/tiktok.svg" alt="">
						</a>
					</li>
				<?php }?>
				<?php ob_start();
echo smarty_function_var(array(0=>"youtube"),$_smarty_tpl);
$_prefixVariable19=ob_get_clean();
if ($_prefixVariable19) {?>
					<li>
						<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"youtube"),$_smarty_tpl);
$_prefixVariable20=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable20)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
							rel="noopener noreferrer nofollow">
							<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/youtube.svg" alt="">
						</a>
					</li>
				<?php }?>
			</ul>
		</div>
	</div>
</div><?php }
}
