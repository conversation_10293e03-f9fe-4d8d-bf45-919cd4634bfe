<?php

require_once __DIR__ . "/../Public_base.php";

class Contacts extends Public_base {

	public function index() {
		$node = $this->node();
		
		$map_model = array();
		$map = @json_decode($node->map);
		$zoom = 10;
		if ($map) {
			$map_model[] = array (
				"lat" => $map->lat,
				"lng" => $map->lng,
				"zoom" => $map->zoom,
				//"icon_url" => $this->path->base . "resource/public/image/<EMAIL>",
				"icon_size" => array(81, 51),
				"icon_anchor" => array(21, 50),
			);
			$zoom = $map->zoom;
		}
		
		$this->view->set("map_model", $map_model);
		$this->view->set("zoom", $zoom);
		
		if ($this->input->is_ajax_request() && _post()) {
			$this->form();
			return;
		}

		$this->render_node("public/contacts", $node);
	}
	
	private function form() {
		$validator = new Validator();
		$model = array(
			"name" => _post("name"),
			"surname" => _post("surname"),
			"email" => _post("email"),
			"phone" => _post("phone"),
			"message" => _post("message"),
			"terms" => _post("terms"),
			"subscribe" => _post("subscribe"),
		);

		$validator->required($model["name"], "name");
		$validator->required($model["surname"], "surname");
		$validator->email($model["email"], "email");
		$validator->required($model["message"], "message");
		$validator->required($model["terms"], "terms");
		
		if ($validator->is_valid()) {
			$inquiry = new inquiry();
			$inquiry->type = "contacts";
			$inquiry->name = $model["name"] . " " . $model["surname"];
			$inquiry->email = $model["email"];
			$inquiry->phone = $model["phone"];
			$inquiry->data = json_encode($model);
			$this->data->add($inquiry);
			
			$recipient = $this->_var("contact-form");
			$this->create_email_send("contacts", $model, "Форма за контакти", $recipient, "text");
			
			if ($model["subscribe"]) {
				$this->add_subscriber($model["name"], $model["email"]);
			}

			$email_model = array(
				"app_domain" => conf("app_domain"),
				"name" => $model["name"],
			);
	
			$this->set_flash("message", $this->diction->get("contacts.success"));

			$this->json_response(array(
				"success" => true,
				"refresh" => true,
			));
		} else {
			$errors = $validator->get_invalid();
			$message = array();
			foreach ($validator->get_invalid() as $k) {
				$message[] = $this->diction->get("contacts.invalid_{$k}");
			}
			$this->json_response(array(
				"success" => false,
				"errors" => $errors,
				"message" => $message,
			));
		}
	}

	public function offer() {
		if (!$this->input->is_ajax_request() || !_post()) {
			$this->_404();
			return;
		}

		$validator = new Validator();
		$model = array(
			"name" => _post("name"),
			"phone" => _post("phone"),
			"terms" => _post("terms"),
		);

		$validator->required($model["name"], "name");
		$validator->required($model["phone"], "phone");
		$validator->required($model["terms"], "terms");
		
		if ($validator->is_valid()) {
			$inquiry = new inquiry();
			$inquiry->type = "offer";
			$inquiry->name = $model["name"];
			$inquiry->phone = $model["phone"];
			$inquiry->data = json_encode($model);
			$this->data->add($inquiry);
			
			$recipient = $this->_var("offer-form");
			$this->create_email_send("offer", $model, "Форма за безплатна оферта", $recipient, "text");
			
			$email_model = array(
				"app_domain" => conf("app_domain"),
				"name" => $model["name"],
			);
	
			$this->set_flash("message", $this->diction->get("contacts.success"));

			$this->json_response(array(
				"success" => true,
				"refresh" => true,
			));
		} else {
			$errors = $validator->get_invalid();
			$message = array();
			foreach ($validator->get_invalid() as $k) {
				$message[] = $this->diction->get("contacts.invalid_{$k}");
			}
			$this->json_response(array(
				"success" => false,
				"errors" => $errors,
				"message" => $message,
			));
		}
	}
}