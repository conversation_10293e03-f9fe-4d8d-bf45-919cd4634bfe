/**********************************************************
 * Wrapper for section headings
**********************************************************/

.g-head {
	margin-bottom: 2rem;
	&--vertical {
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
	}

	&--inner {
		position: relative;
		&:after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			bottom: -1rem;
			width: 100%;
			height: 1px;
			background: linear-gradient(90deg, #939393 0%, rgba(45, 45, 45, 0) 41.35%);
		}
	}


	@include media(md) {
		&--spaced {
			margin-bottom: 3rem;
		}
	}

	@include media(xm) {
		margin-bottom: 2rem;

		&--spaced {
			margin-bottom: 4rem;
		}
	}

	@include media(lg) {
		margin-bottom: 4rem;

		&--spaced {
			margin-bottom: 6rem;
		}
	}

	@include media(xl) {
		margin-bottom: 5rem;

		&--spaced {
			margin-bottom: 8rem;
		}
	}
}