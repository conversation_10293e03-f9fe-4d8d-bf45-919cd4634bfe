<?php

function array_merge_replace() {
	$params = func_get_args();

	// first array is used as the base, everything else overwrites on it
	$res = array_shift($params);

	// merge all arrays on the first array
	foreach($params as $array) {
		foreach($array as $key => $value) {
			if (isset($res[$key]) && is_array($value) && is_array($res[$key])) {
				$res[$key] = merge_replace($res[$key], $value);
			} else {
				$res[$key] = $value;
			}
		}
	}
	return $res;
}

function array_put_prop($arr, $prop_name, $arr_vals, $field, $match_prop="id") {
	return __::map($arr, function($o1) use($prop_name, $arr_vals, $field, $match_prop) {
		$val = __::find($arr_vals, function($o2) use($o1, $match_prop) {
			return $o1->$match_prop == $o2->$match_prop;
		});
		$o1->$prop_name = ($val ? $val->$field : null);
	});
}

function array_prop($arr, $prop) {
	return __::map($arr, function ($o) use ($prop) { return $o->$prop; });
}

function array_find($arr, $prop, $val) {
	return __::find($arr, function($o) use ($prop, $val) { return $o->$prop == $val; });
}

function array_flatten($arr) {
	$return = array();
	array_walk_recursive($arr, function($a) use (&$return) { $return[] = $a; });
	return $return;
}

function array_index($arr, $prop_name) {
	$res = array();
	foreach ($arr as $i) {
		$res[$i->$prop_name] = $i;
	}
	return $res;
}