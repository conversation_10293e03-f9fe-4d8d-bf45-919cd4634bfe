<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	https://codeigniter.com/user_guide/general/hooks.html
|
*/

$hook['pre_system'][] = array(
		'class'    => 'MyHooks',
		'function' => 'generate_routing',
		'filename' => 'MyHooks.php',
		'filepath' => 'hooks',
);
$hook['pre_controller'][] = array(
		'class'    => 'MyHooks',
		'function' => 'files_fix',
		'filename' => 'MyHooks.php',
		'filepath' => 'hooks',
);
$hook['post_controller'][] = array(
		'class'    => 'MyHooks',
		'function' => 'stat_headers',
		'filename' => 'MyHooks.php',
		'filepath' => 'hooks',
);