<?php

require_once "base/Post_edit.php";

class Email_log extends Post_edit {
	
	protected function opts() {
		return array(
				"add"=>false,
				"edit"=>false,
				"delete"=>false,
		);
	}
	
	protected function get_count($filter) {
		return $this->data->emails->get_count($filter);
	}
	
	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->emails->get_list($filter, $order, $dir, $offset, $count);
	}
	
	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"created" => array("type"=>"date", "label"=>"Добавен", "range"=>1),
		);
	}
	protected function orders() {
		return array (
				"created" => "дата и час",
				"name" => "форма",
				"from" => "от",
				"to" => "до",
		);
	}
	protected function direction() {
		return "desc";
	}
	
	public function details($id=0) {
		$this->load->library("diction");
		$this->diction->load("bg");
		
		$item = $this->data->emails->id($id);
		
		if ($item) {
			$template = "email/". $item->name;
			$model = unserialize($item->model);
			if ($item->format == "text") {
				header("Content-Type: text/plain");
			}
			$this->view->result($template, false, $model);
		}
	}
}