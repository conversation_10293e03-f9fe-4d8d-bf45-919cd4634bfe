<?php

$config["email"]["useragent"] = "littleCMS";
$config["email"]["protocol"] = "smtp";
$config["email"]["mailpath"] = "/usr/sbin/sendmail";
$config["email"]["smtp_host"] = "localhost";
$config["email"]["smtp_user"] = "";
$config["email"]["smtp_pass"] = "";
$config["email"]["smtp_port"] = 25;
$config["email"]["smtp_timeout"] = 5;
$config["email"]["smtp_keepalive"] = false;
$config["email"]["smtp_crypto"] = "";
$config["email"]["wordwrap"] = true;
$config["email"]["wrapchars"] = 120;
$config["email"]["mailtype"] = "html";
$config["email"]["charset"] = "utf-8";
$config["email"]["validate"] = true;
$config["email"]["priority"] = 2;
$config["email"]["crlf"] = "\r\n";
$config["email"]["newline"] = "\r\n";
$config["email"]["default_sender"] = "noreply@" . conf("app_domain");
$config["email"]["default_sender_name"] = "";
$config["email"]["multipart"] = "related";

$config["email"]["ms_api"] = false;
$config["email"]["ms_client_id"] = "";
$config["email"]["ms_tenant_id"] = "";
$config["email"]["ms_client_secret"] = "";

merge_env(__FILE__, $config);