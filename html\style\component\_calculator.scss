.calculator {
    display: flex;
    flex-direction: column;

    &__wrap {
        margin-top: -14vw;
    }

    &__main {
        display: flex;
        flex-direction: column;
        gap: 4rem;
        padding: 3rem 2vw;
        background-color: rgba($color-white, .8);
        border: 1px solid rgba(#071B34, .2);
        backdrop-filter: blur(20px);
        border-radius: 3px;
    }

    &__head {
        display: flex;
        justify-content: space-between;
    }

    &__label {
        font-size: 1.5rem;
        font-weight: 600;
    }

    &__current-value {
        font-size: clamp($font-body-lg, 3vw, $font-title-md);
        font-family: $font-title;
    }

    &__values {
        display: flex;
        justify-content: space-between;
        font-size: 1.5rem;
        opacity: .6;
    }

    &__item {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    &__result {
        display: flex;
        gap: 1rem;
    }

    &__list {
        display: flex;
        flex-direction: column;
        max-width: 42rem;
        gap: 1.5rem;
    }

    &__list-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: clamp(1.3rem, 1.5vw, 1.5rem);
        gap: 2rem;
    }

    &__list-value {
        display: flex;
        align-items: center;
        gap: .5rem;
    }

    &__list-hl {
        width: 100%;
        height: 1px;
        background-color: $color-gray-light;
    }

    .irs {
        height: 6rem;
    }

    .irs-min,
    .irs-max,
    .irs-single {
        display: none;
    }

    .irs-handle {
        width: 6rem;
        height: 6rem;
        background-color: $color-white;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        cursor: pointer;

        &:hover {
            background-color: $color-primary;
        }

        &:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2.4rem;
            height: 1.6rem;
            background: url('#{$img-dir}/icon/calculator.svg') no-repeat center center;
            background-size: contain;
            transform: translate(-50%, -50%);
        }

        i {
            background-color: transparent !important;
        }
    }

    .irs-line {
        height: 1px;
        background-color: $color-black;
        top: 3rem;
    }

    .irs-bar--single {
        display: none;
    }

    /* 1024 down */
    @include media(xm-down) {
        .irs-handle {
            transform: translate(-50%, -50%) scale(0.75);
        }
        &__main {
            padding: 2rem 3rem;
        }
    }
}