<?php

abstract class Base extends CI_Controller {
	
	protected $usession = null;
	protected $languages = array();
	protected $lng = null;
	protected $variables = array();

	public function __construct() {
		parent::__construct();

		$this->classloader->load("common");

		$this->config->load("cms");
		$this->config->load("webresources");
		$this->load->helper("url");
		$this->load->library("thumbnails");
		$this->load->library("datamanager", null, "data");
		$this->load->library("hubconnector");
		
		$this->load_firephp();

		if (conf("force_ssl") && !$this->input->is_cli_request()) {
			enforce_ssl();
		}

		$this->classloader->load("ext", "underscore");
		$this->classloader->load("ext", "password");

		$this->output->enable_profiler(conf("profiler"));
		
		$name = app_session_id(conf("app_name"));
		session_name($name);
		session_start();
	}
	
	public function _root() {
		return $this->path->base . $this->_lang_root();
	}
	
	public function _lang_root() {
		if (count($this->languages) > 1 && 
			(conf("default_lang_slug") || $this->lng->code != conf("default_lang"))) {
			return $this->lng->code . "/";
		}
		return "";
	}
	
	protected function load_smarty() {
		$this->load->library("ci_smarty", null, "view");
	}

	protected function load_diction($lang) {
		$this->load->library("diction");
		$this->diction->load($lang->code);
	}

	protected function json_response($res) {
		$this->output
				->set_content_type("application/json")
				->set_output(json_encode($res));
	}

	protected function text_response($res) {
		$this->output->set_output($res);
	}

	protected function render($template) {
		try {
			$this->view->result($template);
		} catch (Exception $e) {
			show_error(htmlspecialchars_decode($e->getMessage()), 500, "Smarty Exception");
		}
	}
	
	protected function is_user($role) {
		return $this->usession && $this->usession->is_valid($role);
	}
	
	protected function create_email($time, $template, $model, $subject, $recipient, $format, $attachments=array()) {
		$this->config->load("email");
		$from = conf("email", "default_sender");
			
		$model["root"] = conf("http_root") ? conf("http_root") : $this->path->http_root;
		$model["app_image"] = (conf("app_image") ? $this->path->http_base . conf("app_image") : "");

		$res = array();

		foreach ((array)$recipient as $to) {
			$message = new email();
			$message->name = $template;
			$message->from = $from;
			$message->to = $to;
			$message->format = $format;
			$message->subject = $subject;
			$message->model = serialize($model);
			$message->attachments = json_encode($attachments);
			$message->scheduled = $time;
			
			// test model and template
			$this->view->result("email/". $template, true, $model);

			$this->data->add($message);
			
			$res[] = $message;
		}
		return $res;
	}

	protected function create_email_send($template, $model, $subject, $recipient, $format="html", $attachments=array()) {
		$emails = $this->create_email(conv_date_string(), $template, $model, $subject, $recipient, $format, $attachments);
		foreach ($emails as $i) {
			$this->send_email($i);
		}
		return $emails;
	}

	protected function send_email($email) {
		$this->config->load("email");
		
		$model = unserialize($email->model);

		$subject = sprintf("[%s] %s", conf("app_domain"), $email->subject);
		$body = $this->view->result("email/". $email->name, true, $model);
		
		send_email($subject, $body, $email->to, $email->from, $email->format, json_decode($email->attachments));

		$email->sent = conv_date_string();
		$this->data->update($email);
	}
	
	protected function set_flash($name, $val=1) {
		set_session($name, $val);
	}
	
	protected function get_flash($name) {
		return _session($name, null, true);
	}
	
	protected function load_usession() {
		$this->usession = $this->data->sessions->get_current();
	}
	
	protected function load_lang() {
		$this->languages = $this->data->languages->all();
		assert($this->languages);
		
		$segments = $this->uri->segment_array();
		$first = array_shift($segments);
		
		$this->lng = __::find($this->languages, function($o) use($first) { return $o->code == $first; });
		
		if (!$this->lng) {
			$this->lng = __::find($this->languages, function($o) { return $o->code == conf("default_lang"); });
			if (!$this->lng) {
				throw new Exception("default language not found: " . conf("default_lang"));
			}
			if (count($this->languages) > 1 && 
				(conf("default_lang_slug") || $this->lng->code != conf("default_lang"))) {
				redirect($this->lng->code . $this->path->current_qs);
			}
		}
		if (count($this->languages) == 1 && $this->lng->code == $first) {
			redirect(implode("/", array_slice($segments, 1)));
		}

		$this->load_diction($this->lng);
	}
	
	protected function load_vars($language_id) {
		$filter = array("language_id" => $language_id);
		$this->variables = $this->data->variables->get_list($filter);
	}
	
	public function _var($key, $lang_id=null) {
		$res = __::find($this->variables, function($o) use($key) { return $o->key == $key; });
		if ($res) {
			return $res->value($lang_id);
		}
		throw new exception("Variable '{$key}' not found.");
	}
	
	protected function load_firephp() {
		$this->classloader->load("ext", "firephp/FirePHP");
		$this->classloader->load("ext", "firephp/fb");
	}
}
