$parent: '.services';

#{$parent} {
    display: flex;
    flex-direction: column;
    gap: 5rem;

    &--inner {
        #{$parent}__item {
            align-items: flex-end;
            padding: 0;
            gap: 2rem;

            &:nth-child(even) {
                flex-direction: row;
            }
            &:nth-child(odd) {
                flex-direction: row-reverse;
            }
        }

        #{$parent}__col {
            width: 40%;
            max-width: none;
        }

        #{$parent}__img-box {
            width: 100%;
            max-width: none;
        }
    }

    &__item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 3rem;
        padding: 5rem 10vw;
        background-color: $color-white;
        border-radius: 3px;

        &:nth-child(even) {
            flex-direction: row-reverse;
        }
    }

    &__col {
        display: flex;
        gap: 6vw;
        flex-direction: column;
        max-width: 56rem;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    &__img-box {
        width: 100%;
        max-width: 44rem;
    }

    &__img {
        display: block;
        width: 100%;
    }

    /* 640 down */
    @include media(md-down) {
        &__item {
            flex-direction: column;
        }

        &--inner {
            #{$parent}__col {
                width: 100%;
            }
        }
    }
}