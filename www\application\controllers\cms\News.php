<?php

require_once "base/Edit_child_nodes.php";

class News extends Edit_child_nodes {
	
	protected function filters() {
		return array (
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"parent_id" => array("type"=>"select", "label"=>"Категория", "opts"=>$this->parents, "opt_val"=>"id", "opt_text"=>"get_title(1)"),
				"is_featured_home" => array("type"=>"checkbox", "label"=>"Акцент (начало)", "def"=>-1),
				"is_featured" => array("type"=>"checkbox", "label"=>"Акцент (категория)", "def"=>-1),
		);
	}

	protected function parent_nodes() {
		return "news";
	}
	
	protected function depth() {
		return 2;
	}

	protected function direction() {
		return "desc";
	}

	protected function on_object_set($action, ci_bean $object) {
		parent::on_object_set($action, $object);
		
		$object->kind = node::KIND_NEWS;
	}
	
	protected function objects_filter() {
		return array(
				"parent_ids" => $this->parent_ids,
				"kinds" => node::KIND_NEWS,
		);
	}
}