<?php

require_once "base/Edit_nodes.php";

class Blocks extends Edit_nodes {

	private $node_opts = array(
		"header" => array(
			"opts" => array("text_plain"),
		),
		"footer" => array(
			"opts" => array("text_plain"),
		),
		"benefits" => array(
			"opts" => array("subtitle_rich", "subtitle2", "text"),
		),
		"comp_clients" => array(
			"opts" => array(),
			"subitems" => array(
				"row" => array(
					"label" => "Редове",
					"opts" => array("title", "comp_checks"),
				),
			),
		),
		"comp_partners" => array(
			"opts" => array(),
			"subitems" => array(
				"row" => array(
					"label" => "Редове",
					"opts" => array("title", "comp_checks"),
				),
			),
		),
		"cta_banner" => array(
			"opts" => array("subtitle_rich", "image"),
		),
		"cta_contacts" => array(
			"opts" => array("subtitle"),
		),
		"reviews" => array(
			"opts" => array(),
			"subitems" => array(
				"review" => array(
					"label" => "Отзиви",
					"opts" => array("title", "subtitle", "text", "image"),
				),
			),
		),
	);

	protected function nodes() {
		return array("header", "footer", "comp_clients", "comp_partners", "cta_banner", "cta_contacts", "reviews", "benefits");
	}

	protected function objects_filter() {
		return array(
			"ids" => nodeid($this->nodes()),
		);
	}

	protected function opts() {
		return array(
			"add" => false,
			"delete" => false,
		);
	}

	protected function asset_types($node_id) {
		$opts = $this->node_opts[nodename($node_id)]["opts"];

		if (in_array("images", $opts)) {
			return array(AssetType::IMAGE);
		}
		return array();
	}

	protected function on_object_load($action, ci_bean $object) {
		parent::on_object_load($action, $object);

		$nodename = nodename($object->id);

		$this->view->set("node_opts", array_flip($this->node_opts[$nodename]["opts"]));
		$this->view->set("subitems", arr($this->node_opts[$nodename], "subitems", array()));
	}

	protected function resolve_subitem($id, $kind) {
		$name = nodename($id);
		return arr($this->node_opts, array($name, "subitems", $kind));
	}

	protected function get_subitem_parent($id) {
		$ids = nodeid($this->nodes());
		$filter = array("id" => $id, "parent_ids" => $ids);
		$res = $this->data->nodes->get_first($filter);
		if (!$res) {
			$res = $this->get_object($id);
		}
		return $res;
	}

	protected function public_url(ci_bean $object) {
		return "";
	}
}
