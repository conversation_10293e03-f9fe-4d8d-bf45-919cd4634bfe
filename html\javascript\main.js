$(document).ready(function () {

	autosize($('textarea'));

	new VenoBox({
		selector: '.-venobox',
		fitView: true
	});

	var $slickCarousels = $('.facts__carousel, .audience-panel__carousel, .related__carousel, .related__img-slider, .article-carousel, .review-carousel, .accorion-slider, .near__vertical, .near__bg, .accordion__team-img-box');

	$slickCarousels.on('init', function (e) {
		Waypoint.refreshAll();
		ScrollTrigger.refresh();
	});

	(function () {
		var lastScroll = $(window).scrollTop();
		var lenis = window.lenis || null;

		function makeSticky() {
			var $header = $('.header');
			var $nav = $header.find('.nav');

			if ($('body').hasClass('-no-scroll')) return;

			if ($(window).scrollTop() > 50) {
				$header.addClass('-sticky');

				if (lastScroll < $(window).scrollTop()) {
					$header.addClass('-hidden');
				} else {
					$header.removeClass('-hidden');
				}

				lastScroll = $(window).scrollTop();
			} else {
				$header.removeClass('-sticky -hidden');
				$nav.removeClass('-open');
			}
		}

		$(window).on('scroll', makeSticky);

		$('.header__nav-trigger').on('click', function (e) {
			e.preventDefault();
			e.stopPropagation();
			$('.header').toggleClass('-active');
			$('.header').removeClass('-hidden');
			$('.nav').toggleClass('-active');
			$('body').toggleClass('-no-scroll');

			if ($('body').hasClass('-no-scroll')) {
				$('html').css('overflow', 'hidden');
			} else {
				$('html').css('overflow', '');
			}
		});

		$('.nav__link').on('click', function (e) {
			e.preventDefault();
			e.stopPropagation();
			$('.header').removeClass('-active');
			$('.nav').removeClass('-active');
			$('body').removeClass('-no-scroll');
			$('html').css('overflow', '');
		});

	})();


	(function () {
		function animateScroll(target) {
			if ($(target).length > 0) {
				var offset = $(target).offset().top - 50;
				$('html, body').animate({ scrollTop: offset }, 800);
			}
		}

		$('.js-scroll-to').on('click', function (e) {
			e.preventDefault();

			var target = $(this).attr('href');

			setTimeout(function () {
				animateScroll(target);
			}, 100);
		});
	})();

	
	if ($(".calculator__input-price").length == 1) {
		$(".calculator__input-price").ionRangeSlider({
			min: 5000,
			max: 300000,
			from: 5000,
			onChange: function (data) {
				$('.price-current').text(data.from_pretty);
			}
		});
	}

	if ($(".calculator__input-years").length == 1) {
		$(".calculator__input-years").ionRangeSlider({
			min: 2,
			max: 20,
			from: 2,
			onChange: function (data) {
				$('.years-current').text(data.from_pretty);
			}
		});
	}

	(function () {
		$('.chart__tab').on('click', function (e) {
			let dataId = $(this).attr("data-id");
			$('.chart__tab').removeClass("-current");
			$(this).addClass("-current");
			$('.chart__table').removeClass("-visible");
			$(".chart__table").eq(dataId).addClass("-visible");
		});
	})();

	$('.facts').each(function () {
		var $t = $(this);

		$t.find('.facts__carousel').slick({
			arrows: true,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 1000,
			infinite: true,
			variableWidth: false,
			autoplay: true,
			autoplaySpeed: 1000,
			responsive: [
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 4,
					}
				}
			]

		});
	});

	$('.audience-panel').each(function () {
		var $t = $(this);
		var $apartment = $(this).find('.audience-panel__carousel');
		var $tab = $(this).find('.audience-panel__tab');
		$tab.on("click", function (e) {
			e.preventDefault();
			$tab.removeClass("-current");
			$(this).addClass("-current");
			var dataId = $(this).attr("data-id");
			$apartment.slick('slickGoTo', dataId);
		});

		$apartment.slick({
			arrows: false,
			prevArrow: null,
			infinite: true,
			dots: false,
			slidesToShow: 1,
			slidesToScroll: 1,
			mobileFirst: true,
			adaptiveHeight: true,
			fade: true,
		}).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
			var $currentTab = $t.find('.audience-panel__tab').filter(function (item) {
				return nextSlide === $(this).data('id');
			});

			$t.find(".audience-panel__tab").removeClass("-current");
			$currentTab.addClass('-current');
		});
	});

	$('.related').each(function () {
		var $t = $(this);
		var $mainSlider = $t.find('.related__carousel');
		var $imgSlider = $t.find('.related__img-slider')

		$mainSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			prevArrow: $t.find('.related__btn--prev'),
			nextArrow: $t.find('.related__btn--next'),
			fade: true,
			asNavFor: $imgSlider,
			mobileFirst: true,
			infinite: true,
		});
		$imgSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			asNavFor: $mainSlider,
			dots: false,
			focusOnSelect: true,
			arrows: false,
			variableWidth: false,
			infinite: true,
			mobileFirst: true,
		});
	});

	$('.article-carousel').each(function () {
		var $t = $(this);

		$t.slick({
			arrows: false,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			responsive: [
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 3,
					}
				}
			]
		});
	});

	$('.review-carousel').each(function () {
		var $t = $(this);

		$t.slick({
			prevArrow: ('.review-carousel__btn--prev'),
			nextArrow: ('.review-carousel__btn--next'),
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			responsive: [
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 2,
					}
				}
			]
		});
	});

	$('.accorion-slider').each(function () {
		var $t = $(this);
		var $carousel = $t.find('.accordion__team-img-box');
		var $tab = $t.find('.accordion__item');
		$tab.on("click", function (e) {
			e.preventDefault();
			var dataId = $(this).attr("data-id");
			$carousel.slick('slickGoTo', dataId);
		});

		$carousel.slick({
			arrows: false,
			prevArrow: null,
			infinite: true,
			dots: false,
			slidesToShow: 1,
			slidesToScroll: 1,
			mobileFirst: true,
			adaptiveHeight: true,
			fade: true,
		}).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
			var $currentTab = $t.find('.accordion__item').filter(function (item) {
				return nextSlide === $(this).data('id');
			});
		});
	});

	$('.near').each(function () {
		var $t = $(this);
		var $mainSlider = $t.find('.near__vertical');
		var $imgSlider = $t.find('.near__bg')

		$mainSlider.slick({
			arrows: true,
			slidesToShow: 3,
			slidesToScroll: 1,
			arrows: false,
			fade: false,
			asNavFor: $imgSlider,
			mobileFirst: true,
			infinite: true,
			autoplay: true,
			autoplaySpeed: 3000,
			vertical: true,
			verticalSwiping: true,
			useTransform: true,
			cssEase: 'cubic-bezier(0.645, 0.045, 0.355, 1.000)',
			adaptiveHeight: true,
			centerMode: true,
			centerPadding: '60px',
		});
		$imgSlider.slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			asNavFor: $mainSlider,
			dots: false,
			focusOnSelect: true,
			arrows: false,
			variableWidth: false,
			infinite: true,
			mobileFirst: true,
			autoplay: true,
			autoplaySpeed: 3000,
			fade: false,
		});
	});

	$('.steps').each(function () {
		var $t = $(this);

		$t.slick({
			// prevArrow: ('.review-carousel__btn--prev'),
			// nextArrow: ('.review-carousel__btn--next'),
			arrows: true,
			dots: false,
			centerMode: false,
			mobileFirst: true,
			slidesToShow: 1,
			speed: 600,
			infinite: false,
			responsive: [
				{
					breakpoint: 450,
					settings: {
						slidesToShow: 2,
					}
				},
				{
					breakpoint: 640,
					settings: {
						slidesToShow: 3,
					}
				},
				{
					breakpoint: 768,
					settings: {
						slidesToShow: 4,
					}
				},
				{
					breakpoint: 1024,
					settings: {
						slidesToShow: 5,
					}
				},
				{
					breakpoint: 1280,
					settings: "unslick"
				}
			]
		});
	});


	$('.accordion').each(function () {
		var button = $(this).find('.accordion__control');
		if (button.hasClass("-active-control")) {
			$(".-active-control").next(".accordion__panel").slideDown(400);
		}
		$(this).on('click', '.accordion__control', function (e) {
			e.preventDefault();
			var $t = $(this);

			if (!$t.hasClass("-active-control")) {
				$(".accordion__panel").slideUp(400);
				$(".accordion__control").removeClass("-active-control");
			}

			$t.toggleClass("-active-control");
			$t.next().slideToggle();
			$('.accordion__slider').slick('refresh');
		});

		$("accordion__panel:visible").each(function () {
			console.log($t.attr('id'));
		});
	});
	$('.partners-accordion').each(function () {
		const $accordion = $(this);

		$accordion.find('.partners-accordion__header.-open').each(function () {
			$(this).next('.partners-accordion__content').slideDown(0);
			$(this).closest('.partners-accordion__item').addClass('-active');
		});

		$accordion.on('click', '.partners-accordion__header', function (e) {
			e.preventDefault();

			const $header = $(this);
			const $item = $header.closest('.partners-accordion__item');
			const $content = $header.next('.partners-accordion__content');

			if (!$header.hasClass('-open')) {
				$accordion.find('.partners-accordion__header').removeClass('-open');
				$accordion.find('.partners-accordion__item').removeClass('-active');
				$accordion.find('.partners-accordion__content').slideUp(400);
			}

			$header.toggleClass('-open');
			$item.toggleClass('-active');
			$content.stop(true, true).slideToggle(400);
		});
	});

	$('.consent').each(function () {
		var $t = $(this);

		if (!localStorage.getItem('cookieConsent')) {
			$t.addClass('-is-shown');
		}

		$t.find('.consent__btn').on('click', function (e) {
			e.preventDefault();

			localStorage.setItem('cookieConsent', true);
			$t.removeClass('-is-shown');
		});
	});

});