/**********************************************************
 * 
**********************************************************/

$parent: '.tabs';

#{$parent} {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 2rem;

    &--news {
        font-family: $font-title;
        font-size: clamp(1.8rem, 1.5vw, 2rem);
        text-transform: uppercase;

        #{$parent}__item {

            &.-current,
            &:hover {
                #{$parent}__link {
                    color: $color-primary;
                }
            }

        }
    }

    &--pill {
        #{$parent}__link {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            border-radius: 2rem;
            border: 1px solid currentColor;
            padding: 1rem 2rem;

            &:hover {
                color: black;
            }
        }

        #{$parent}__item {
            &.-current #{$parent}__link {
                background-color: #000;
                color: #fff;
            }
        }
    }

    &--underline {
        #{$parent}__item {
            &.-current #{$parent}__link {
                color: black;

                &::after {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }

        #{$parent}__link {
            display: block;
            position: relative;
            padding-bottom: 1rem;

            &::after {
                background: gray no-repeat left bottom;
                bottom: 0;
                content: '';
                height: 0.5rem;
                left: 0;
                opacity: 0;
                position: absolute;
                visibility: hidden;
                width: 100%;
            }

            &:hover {
                color: black;
            }
        }
    }

    &__item {
        margin-bottom: 0.8rem;
        margin-right: 1.5rem;
    }

    &__link {
        color: gray;
        font-weight: bold;



    }


    @include media(sm) {
        margin-bottom: 3rem;

        &__item {
            margin-bottom: 1rem;
            margin-right: 2rem;
        }

        &__link {
            padding-bottom: 1rem;
        }
    }

    @include media(lg) {
        &__item {
            margin-right: 3rem;
        }

        &__link {
            font-size: $font-body-lg;
        }
    }
}