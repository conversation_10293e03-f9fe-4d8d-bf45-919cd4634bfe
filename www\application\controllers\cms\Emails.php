<?php

require_once "base/Post_edit.php";

class Emails extends Post_edit {

	const FILTER = array("groups"=>"email");

	protected function opts() {
		return array(
				"add"=>false,
				"delete"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->variables->get_count(self::FILTER);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->variables->get_list(self::FILTER);
	}

	public function on_indexing($filter, array $objects) {
		$validator = new Validator();
		if ($this->get_opt("edit") && $this->post_action == "save") {
			foreach($objects as $object) {
				$object->set(_post_files_merge(array("object", $object->id)));		

				$v = $object->validator();
				if (!$v->validate()) {
					$validator->merge($v, $object->id . ".");
				}
			}
			if ($validator->is_valid()) {
				foreach($objects as $object) {
					$this->data->update($object);
				}

				$this->add_log_entry("edit", "");
				$this->set_success();
			}
		}
		$this->view->set("validator", $validator);
	}
}