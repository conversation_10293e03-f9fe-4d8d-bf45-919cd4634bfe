$parent: '.audience-panel';

#{$parent}  {
    position: absolute;
    left: 0;
    bottom: 0;

    &__wrap {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        max-width: 44rem;
    }

    &__tabs {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    &__tab {
        &.-current,
        &:hover {
            #{$parent}__tab-btn {
                background-color: rgba($color-white, .8);
            }
        }
    }

    &__tab-btn {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 2rem;
        background-color: rgba($color-white, .2);
        backdrop-filter: blur(60px);
        border: 1px solid rgba($color-white, .3);
    }

    &__vl {
        align-self: center;
        width: 1px ;
        height: 4rem;
        background-color: $color-primary;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        padding: 2rem;
        background-color: rgba($color-white, .8);
        border: 1px solid rgba($color-black, .3);
        backdrop-filter: blur(20px);
    }

    &__carousel {
        max-width: 33rem;
    }

    /* 1024 down */
    @include media(xm-down) {
        position: relative;
        left: unset;
        bottom: unset;
        margin-top: -25rem;
    }
}