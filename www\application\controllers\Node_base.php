<?php

require_once __DIR__ . "/Base.php";

abstract class Node_base extends Base {
	
	private $nodes;
	
	protected $page_title;
	protected $page_description;
	protected $page_image;
	protected $page_url;
	protected $page_type = "website";
	protected $user;
	protected $paging_prefix;

	private $js_vars = array();
	private $html_meta = array();
	private $html_link = array();
	private $html_og = array();
	
	public function __construct() {
		parent::__construct();
		
		$this->load_usession();

		$this->load_lang();

		$this->load_vars($this->lng->id);

		$this->check_http_auth();

		$this->load_smarty();

		$this->config->load("nodes");

		$node_ids = array_values(conf("nodes"));
		$node_names = array_flip(conf("nodes"));

		$nodes = $this->data->nodes->get_list_loc($this->lng->id, array("ids" => $node_ids));

		$this->nodes = new stdClass();
		foreach ($nodes as $node) {
			assert(isset($node_names[$node->id]));
			$name = $node_names[$node->id];
			$this->nodes->$name = $node;
		}

		$this->view->set("app_root", $this->path->base);
		$this->view->set("app_root_abs", $this->path->http_base);
		$this->view->set("usession", $this->usession);
		$this->view->set("root", $this->_root());
		$this->view->set("languages", $this->languages);
		$this->view->set("lng", $this->lng);
		$this->view->set("is_user", $this->is_user(conf("profile_role")));
		$this->view->set("res", $this->path->base . "resource/public/");
		$this->view->set("main_title", $this->_var("title", $this->lng->id));
		$this->view->set("favicon", $this->_var("favicon"));

		foreach ($this->nodes as $name => $node) {
			$this->view->set("node_{$name}", $node);
		}

		$this->view->set("custom_codes", $this->data->custom_codes->get_list(array("is_active" => 1)));
		
		if ($this->usession) {
			$this->user = $this->data->users->get_first(array(
				"id" => $this->usession->user_id,
				"role_ids" => conf("profile_role"),
			));
		}
		
		$this->view->set("user", $this->user);

	}
	
	protected function node() {
		$res = null;
		
		$segments = $this->uri->segment_array();
		$segments = array_values($segments);
		if (count($this->languages) > 1 && (conf("default_lang_slug") || $segments && strlen($segments[0]) == 2)) {
			array_shift($segments);
		}
		
		if ($segments) {
			foreach ($segments as $seg) {
				$current = __::find($this->nodes, function ($o) use($seg, $res) {
					$parent_id = ($res ? $res->id : null);
					return $o->slug == urldecode($seg) && $o->parent_id == $parent_id;
				});
				if ($current) {
					$res = $current;
					array_shift($segments);
				} else {
					break;
				}
			}
		} else {
			$res = $this->get_node(conf("default_node"));
		}
		return $res;
	}

	protected function render_node($template, $node) {
		if ($node) {
			$this->load_lang_paths($node);
			$this->load_custom_urls();
			$this->load_ld($node);

			$this->prepare_render($node);

			$this->add_hit($node);

			$this->render($template);
		} else {
			$this->_404();
		}
	}

	protected function _404() {
		$url = $this->path->current_qs;
		if ($url) {
			$filter = array(
				"url" => urldecode($url),
				"is_active" => 1,
			);
			$res = $this->data->custom_urls->get_first($filter);
			if ($res && $res->redirect) {
				redirect($res->redirect);
			}
		}
		
		$this->output->set_status_header(404, "404 Page Not Found");
		
		if ($this->input->is_ajax_request()) {
			$this->json_response(array(
				"error" => true,
				"message" => "The resource was not found.",
			));
		} else {
			$node = $this->get_node("404");
	
			assert($node);
			
			$this->load_lang_paths($node);
			$this->load_ld($node);

			$this->prepare_render($node);
			
			$this->render("public/404", $node);
		}
	}

	protected function get_node($node) {
		return $this->nodes->$node;
	}

	protected function get_nodes() {
		$nodes = func_get_args();
		if (isset($nodes[0]) && is_array($nodes[0])) {
			$nodes = $nodes[0];
		}
		$nodes = (array)nodeid($nodes);

		return __::filter($this->nodes, function ($o) use($nodes) {
			return in_array($o->id, $nodes);
		});
	}

	protected function get_nodes_ordered() {
		$nodes = func_get_args();
		if (isset($nodes[0]) && is_array($nodes[0])) {
			$nodes = $nodes[0];
		}
		$res = array ();
		foreach ($nodes as $n) {
			$res[] = $this->get_node($n);
		}
		return $res;
	}

	protected function goto_node($node, $append = "") {
		redirect(self::get_path($node, $append));
	}

	protected function get_path($node, $append = "") {
		if (is_string($node)) {
			$node = $this->nodes->$node;
		}
		return $this->_lang_root() . $node->path() . $append;
	}

	protected function get_page_title($node) {
		$main = $this->_var("title", $this->lng->id);
		$node_title = "";
		if ($node && !$node->is_index()) {
			$node_title = $node->title;
		}

		$res = "";
		if ($this->paging_prefix) {
			$res = $this->paging_prefix . " - ";
		}

		if ($this->page_title) {
			$res .= $this->page_title;
		} else if ($main && $node_title) {
			$res .=  sprintf("%s | %s", $node_title, $main);
		} else if ($node_title) {
			$res .= $node_title;
		} else {
			$res .= $main;
		}
		return $res;
	}

	protected function get_page_description($node) {
		if ($this->page_description) {
			return $this->page_description;
		}
		if ($node && $node->description) {
			return $node->description;
		}
		if ($node) {
			$res = "";
			if ($this->paging_prefix) {
				$res .= $this->paging_prefix . " - ";
			}
			$res = text_limit($node->text, 150, "");
			if (!$res) {
				$res = $this->_var("html-desc", $this->lng->id);
			}
			return $res;
		}
		return "";
	}

	protected function get_keywords() {
		return $this->_var("html-keywords", $this->lng->id);
	}

	protected function get_page_image($node) {
		$res = "";
		if ($this->page_image) {
			$res = $this->page_image;
		}
		if (!$res && $node && $node->image) {
			$res = file_image_url($node->image, "social");
		}
		if (!$res && $node) {
			$asset = __::find($node->assets(), function ($o) {
				return $o->type == AssetType::IMAGE;
			});

			if ($asset) {
				$res = file_image_url($asset->file, "social");
			}
		}
		if (!$res) {
			$var = $this->_var("social-image");
			if ($var) {
				$res = file_image_url($var, "social", "var");
			}
		}
		if ($res) {
			return $this->path->http_root . $res;
		}
		return "";
	}

	protected function get_page_canonical($node) {
		if ($this->page_url) {
			return $this->page_url;
		}
		if ($node) {
			return $this->path->http_base . $this->_lang_root() . $node->path();
		} else {
			return $this->path->current_abs;
		}
	}
	
	protected function get_page_type($node) {
		if ($this->page_type) {
			return $this->page_type;
		}
		return "";
	}

	protected function load_assets($nodes, $type="") {
		if ($nodes) {
			if (!is_array($nodes)) {
				$nodes = array($nodes);
			}
			$filter = array(
				"node_ids" => array_prop($nodes, "id"),
				"type" => $type,
			);
			return $this->data->assets->get_list($filter);
		}
		return array();
	}

	protected function load_lang_paths($node) {
		if ($node && count($this->languages) > 1) {
			$this->lang_paths = $this->data->nodes->load_lang_paths($node->id);
			foreach ($this->lang_paths as $id=>$v) {
				$lang = __::find($this->languages, function($o) use ($id) {
					return $o->id == $id;
				});
				if (conf("default_lang_slug") || $lang->code != conf("default_lang")) {
					$this->lang_paths[$id] = $lang->code . "/" . $v;
				}
			}
			$this->view->set("lang_paths", $this->lang_paths);
		}
	}
	
	protected function load_custom_urls() {
		$url = $this->path->current_qs;
		if ($url) {
			$filter = array(
				"url" => urldecode($url),
				"is_active" => 1,
			);
			$res = $this->data->custom_urls->get_first($filter);
			if ($res) {
				$this->page_title = $res->title;
				$this->page_description = $res->desc;
				if ($res->image) {
					$this->page_image = file_image_url($res->image, "social");
				}
			}
		}
	}
	
	protected function load_node($slugs) {
		$slugs = array_map("urldecode", $slugs);
		$filter = array(
				"path" => implode("/", $slugs)
		);
		return $this->data->nodes->get_first_loc($this->lng->id, $filter);
	}

	protected function find_node($start, $slugs) {
		if ($start) {
			foreach ($slugs as $s) {
				if ($start) {
					$filter = array(
						"parent_id" => $start->id,
						"slug" => urldecode($s),
					);
					$start = $this->data->nodes->get_first_loc($this->lng->id, $filter);
				}
			}
		}
		return $start;
	}
	
	protected function add_hit($node) {
		$url = $this->path->current_qs;
		$node_id = $node ? $node->id : null;
		$user_id =$this->usession ? $this->usession->user_id : null;
		$this->data->hits->add($url, $node_id, $this->lng->id, $user_id);
	}

	protected function add_js_var($name, $value, $overwrite=false) {
		if (!isset($this->js_vars[$name]) || $overwrite) {
			$this->js_vars[$name] = $value;
		}
	}

	protected function add_html_meta($name, $value, $overwrite=false) {
		if (!isset($this->html_meta[$name]) || $overwrite) {
			$this->html_meta[$name] = $value;
		}
	}

	protected function add_html_link($name, $value, $overwrite=false) {
		if (!isset($this->html_link[$name]) || $overwrite) {
			$this->html_link[$name] = $value;
		}
	}

	protected function add_html_og($name, $value, $overwrite=false) {
		if (!isset($this->html_og[$name]) || $overwrite) {
			$this->html_og[$name] = $value;
		}
	}
	
	private function prepare_render($node) {
		$this->add_html_meta("title", $this->get_page_title($node));
		$this->add_html_meta("description", $this->get_page_description($node));
		$this->add_html_meta("keywords", $this->get_keywords());
		$this->add_html_meta("image", $this->get_page_image($node));
		$this->add_html_meta("type", $this->get_page_type($node));
		$this->add_html_meta("url", $this->get_page_canonical($node));
		$this->add_html_meta("twitter:image", $this->get_page_image($node));
		$this->add_html_meta("twitter:title", $this->get_page_title($node));
		$this->add_html_meta("twitter:description", $this->get_page_description($node));
		$this->add_html_meta("application-name", conf("app_domain"));
		$this->add_html_meta("author", conf("app_author"));
		$this->add_html_meta("dcterms.rightsHolder", conf("app_copyright"));
		$this->add_html_meta("dcterms.dateCopyrighted", conf("app_copyright_date"));
		$this->add_html_meta("google-site-verification", $this->_var("google-site-ver"));
		$this->add_html_meta("robots", "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1");
		$this->add_html_meta("locale", $this->lng->locale);
		$this->add_html_meta("site_name", $this->_var("title", $this->lng->id));
		$this->add_html_meta("twitter:site", $this->get_page_canonical($node));
		$this->add_html_meta("twitter:card", $this->get_page_title($node));
		$this->add_html_meta("twitter:image", $this->get_page_image($node));
		
		
		$this->add_html_og("title", $this->get_page_title($node));
		$this->add_html_og("description", $this->get_page_description($node));
		$this->add_html_og("keywords", $this->get_keywords());
		$this->add_html_og("image", $this->get_page_image($node));
		$this->add_html_og("url", $this->get_page_canonical($node));
		
		$this->add_html_link("canonical", $this->get_page_canonical($node));
		
		$this->add_js_var("facebookAppId", conf("facebook_app_id"));
		
		$this->view->set("node", $node);
		
		$this->view->set("app_domain", conf("app_domain"));
		$this->view->set("show_stats", conf("show_exec_stats"));
		$this->view->set("query_string", _server("QUERY_STRING"));
		
		$this->view->set("js_vars", json_encode($this->js_vars));
		$this->view->set("html_meta", array_filter($this->html_meta));
		$this->view->set("html_og", array_filter($this->html_og));
		$this->view->set("html_link", $this->html_link);
	}

	private function check_http_auth() {
		if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
			$auth = explode(':' , base64_decode(substr($_SERVER['HTTP_AUTHORIZATION'], 6)));
			$_SERVER['PHP_AUTH_USER'] = arr($auth, 0);
			$_SERVER['PHP_AUTH_PW'] = arr($auth, 1);
		}
		
		if (!(_server("PHP_AUTH_USER") == $this->_var("http-auth-username") && _server("PHP_AUTH_PW") == $this->_var("http-auth-password"))) {
			header('WWW-Authenticate: Basic realm="Password required"');
			header('HTTP/1.0 401 Unauthorized');
			exit;
		}
	}

	protected function set_paging_meta($paging) {
		$qs = $this->path->qs();
		if ($paging->index() > 1) {
			$append = ($paging->prev()==1 ? "" : "/@".$paging->prev());
			$prev = rtrim($this->path->base, "/") . preg_replace("/\/@\d+/", "", $this->path->current) . $append;
			$this->add_html_link("prev", $prev);
			$this->paging_prefix = $this->diction->get("seo.page") . " " . $paging->index();
		}
		if ($paging->index() < $paging->pages()) {
			$append = "/@".$paging->next();
			
			$next = rtrim($this->path->base, "/") . preg_replace("/\/@\d+/", "", $this->path->current) . $append;

			$this->add_html_link("next", $next);
		}
	}
}
