<?php

require_once __DIR__ . "/../Public_base.php";

class Faq extends Public_base {
	public function index() {
		$node = $this->node();
		
		$list = $this->data->nodes->get_list_loc($this->lng->id, array(
			"parent_id" => nodeid("faq"),
			"kinds" => node::KIND_FAQ,
		));
		$this->data->nodes->get_list_loc($this->lng->id, array(
			"parent_ids" => array_prop($list, "id"),
		));
		$this->view->set("list", $list);
		
		$this->render_node("public/faq", $node);
	}
}
