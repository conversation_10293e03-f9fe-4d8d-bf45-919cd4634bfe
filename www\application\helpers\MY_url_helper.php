<?php

function refresh() {
	$qs = get_instance()->path->qs;
	redirect(current_url() . ($qs ? "?" : "") . $qs);
}

function get_url($input, $secure=false) {
	if (preg_match("/^(#|http(s?):\/\/)/", $input)) {
		return $input;
	}
	$s = ($secure ? "s" : "");
	return "http{$s}://" . $input;
}
function get_qrcode_url($url, $size=5) {
	return sprintf("%sservice/qrcode?url=%s&s=%s", get_instance()->path->base, urlencode($url), $size);
}
function get_map_url($lat, $lng) {
	return sprintf("https://maps.google.com/maps?q=%s,%s", $lat, $lng);
}
function slug_auto($input, $lang) {
	$CI =& get_instance();
	$CI->config->load("stopwords");

	$transliterate = conf("slugs_transliterate");
	$separator = conf("slugs_separator");
	$max_length = conf("stopwords", "max_length");
	$words = conf("stopwords", $lang);

	if ($words) {
		$input = slug_escape($input, 99999);
		$split = explode($separator, $input);

		$res = array();
		$length = 0;
		foreach ($split as $word) {
			if (!in_array($word, $words)) {
				$res[] = $transliterate ? transliterate($word) : $word;
				$length += (mb_strlen($word, "utf-8") + 1);
				if ($length >= $max_length) {
					break;
				}
			}
		}
		return implode($separator, $res);
	}
	$res = slug_escape($input);
	return $transliterate ? transliterate($res) : slug_escape($input);
}

function slug_escape($input, $length=64) {
	$separator = conf("slugs_separator");
	$quoted = preg_quote($separator);

	$res = trim($input);
	$res = preg_replace("/([^a-z\p{Cyrillic}0-9\-]|\s)/ui", $separator,  $res);
	$res = preg_replace("/{$quoted}+/", $separator,  $res);
	$res = preg_replace("/{$quoted}+$/", "",  $res);
	$res = mb_substr($res, 0, $length, "utf-8");
	$res = mb_strtolower($res, "utf-8");
	$res = trim($res);
	return $res;
}

