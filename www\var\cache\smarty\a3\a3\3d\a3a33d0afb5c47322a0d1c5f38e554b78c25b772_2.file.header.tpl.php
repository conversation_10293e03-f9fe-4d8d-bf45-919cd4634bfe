<?php
/* Smarty version 3.1.30, created on 2025-07-05 18:53:31
  from "W:\work\dvm_finance\www\application\views\public\_partial\header.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_68694a7bed6f10_41207817',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a3a33d0afb5c47322a0d1c5f38e554b78c25b772' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\_partial\\header.tpl',
      1 => 1751730734,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68694a7bed6f10_41207817 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_path')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.path.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
if (!is_callable('smarty_function_var')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\function.var.php';
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_url')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.url.php';
?>
<div class="g-pin-sign <?php echo htmlspecialchars($_smarty_tpl->tpl_vars['header_cta_css']->value, ENT_QUOTES, 'UTF-8');?>
">
	<img class="g-pin-sign__logo outline" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo-Background.svg" alt="">
	<img class="g-pin-sign__logo filled" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Sign-Logo-Background-Active.svg" alt="">
</div>
<div class="header__cta is-hidden--xm">
	<a class="btn btn--md btn--black btn--partner" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_partner']->value), ENT_QUOTES, 'UTF-8');?>
">
		<?php echo smarty_modifier_dict("misc.become_partner");?>

		<i class="icon icon--handshake"></i>
	</a>
	<a class="btn btn--md btn--cta" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
">
		<?php echo smarty_modifier_dict("misc.free_consult");?>

		<i class="icon icon--chat"></i>
	</a>
</div>
<header class="header">
	<div class="g-wrap g-wrap--xl header__wrap js-anim-rev">
		<div class="header__left">
			<a class="header__logo-box" href="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['root']->value, ENT_QUOTES, 'UTF-8');?>
">
				<img class="header__logo" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/Logo.svg" alt="">
			</a>
			<div class="btn__wrap">
				<button class="header__nav-trigger">
					<div class="header__hamburger">
						<span class="header__hamburger-line"></span>
					</div>
					<span class="is-hidden header__hamburger-txt is-inline--lg">
						<span class="-open"><?php echo smarty_modifier_dict("misc.menu");?>
</span>
						<span class="-close"><?php echo smarty_modifier_dict("misc.close");?>
</span>
					</span>
				</button>
				<a class="btn btn--md btn--black btn--partner is-hidden is-flex--xm" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_partner']->value), ENT_QUOTES, 'UTF-8');?>
">
					<?php echo smarty_modifier_dict("misc.become_partner");?>

					<i class="icon icon--handshake"></i>
				</a>
			</div>
		</div>
		<div class="header__right">
			<div class="btn__wrap">
				<a class="header__call" href="tel:<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>
">
					<i class="icon icon--phone is-hidden--lg"></i>
					<div class="header__call-txt is-hidden is-flex--lg">
						<?php $_smarty_tpl->_assignInScope('text', explode("\n",$_smarty_tpl->tpl_vars['node_header']->value->text));
?>
						<strong><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['text']->value[0], ENT_QUOTES, 'UTF-8');?>
</strong>
						<span class="header__call-subtxt"><?php echo htmlspecialchars((($tmp = @$_smarty_tpl->tpl_vars['text']->value[1])===null||$tmp==='' ? '' : $tmp), ENT_QUOTES, 'UTF-8');?>
</span>
					</div>
					<div class="header__call-num is-hidden is-inline--xm">
						<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>

					</div>
				</a>
				<a class="btn btn--md btn--cta is-hidden is-flex--xm" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
">
					<?php echo smarty_modifier_dict("misc.free_consult");?>

					<i class="icon icon--chat"></i>
				</a>
			</div>
		</div>
	</div>
</header>
<nav class="nav">
	<div class="nav__main">
		<div class="g-wrap g-wrap--xl g-row">
			<div class="col-1 col-xm-1-2">
				<p class="nav__label"><?php echo smarty_modifier_dict("misc.menu");?>
</p>
				<div class="g-row">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, array_chunk($_smarty_tpl->tpl_vars['nav']->value,ceil(count($_smarty_tpl->tpl_vars['nav']->value)/2)), 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
						<div class="col-1 col-md-1-2">
							<ul class="nav__list">
								<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['i']->value, 'j');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['j']->value) {
?>
									<li class="nav__item">
										<a class="nav__link underline" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['j']->value), ENT_QUOTES, 'UTF-8');?>
"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['j']->value->title, ENT_QUOTES, 'UTF-8');?>
</a>
									</li>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

							</ul>
						</div>
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
			</div>
			<div class="col-1 col-xm-1-2">
				<p class="nav__label"><?php echo smarty_modifier_dict("misc.contacts");?>
</p>
				<div class="g-row">
					<div class="col-1 col-md-1-2">
						<ul class="contacts">
							<li class="contacts__item">
								<p class="contacts__label"><?php echo smarty_modifier_dict("misc.address");?>
</p>
								<p class="contacts__text">
									<?php ob_start();
echo smarty_function_var(array(0=>"address",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);
$_prefixVariable1=ob_get_clean();
echo smarty_modifier_pt(($_prefixVariable1));?>

								</p>
							</li>
							<li class="contacts__item">
								<p class="contacts__label"><?php echo smarty_modifier_dict("misc.phone");?>
</p>
								<a href="tel:<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>
" class="contacts__text underline">
									<?php echo smarty_function_var(array(0=>"phone",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>

								</a>
							</li>
						</ul>
					</div>
					<div class="col-1 col-md-1-2">
						<ul class="contacts">
							<li class="contacts__item">
								<p class="contacts__label"><?php echo smarty_modifier_dict("misc.write");?>
</p>
								<a href="mailto:<?php echo smarty_function_var(array(0=>"email",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>
" class="contacts__text underline">
									<?php echo smarty_function_var(array(0=>"email",1=>$_smarty_tpl->tpl_vars['lng']->value->id),$_smarty_tpl);?>

								</a>
							</li>
							<li class="contacts__item">
								<p class="contacts__label"><?php echo smarty_modifier_dict("misc.social");?>
</p>
								<ul class="contacts__socials">
									<?php ob_start();
echo smarty_function_var(array(0=>"facebook"),$_smarty_tpl);
$_prefixVariable2=ob_get_clean();
if ($_prefixVariable2) {?>
										<li>
											<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"facebook"),$_smarty_tpl);
$_prefixVariable3=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable3)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
												rel="noopener noreferrer nofollow">
												<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/facebook.svg" alt="">
											</a>
										</li>
									<?php }?>
									<?php ob_start();
echo smarty_function_var(array(0=>"instagram"),$_smarty_tpl);
$_prefixVariable4=ob_get_clean();
if ($_prefixVariable4) {?>
										<li>
											<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"instagram"),$_smarty_tpl);
$_prefixVariable5=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable5)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
												rel="noopener noreferrer nofollow">
												<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/instagram.svg" alt="">
											</a>
										</li>
									<?php }?>
									<?php ob_start();
echo smarty_function_var(array(0=>"tiktok"),$_smarty_tpl);
$_prefixVariable6=ob_get_clean();
if ($_prefixVariable6) {?>
										<li>
											<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"tiktok"),$_smarty_tpl);
$_prefixVariable7=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable7)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
												rel="noopener noreferrer nofollow">
												<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/tiktok.svg" alt="">
											</a>
										</li>
									<?php }?>
									<?php ob_start();
echo smarty_function_var(array(0=>"youtube"),$_smarty_tpl);
$_prefixVariable8=ob_get_clean();
if ($_prefixVariable8) {?>
										<li>
											<a class="contacts__socials-link" href="<?php ob_start();
echo smarty_function_var(array(0=>"youtube"),$_smarty_tpl);
$_prefixVariable9=ob_get_clean();
echo htmlspecialchars(smarty_modifier_url(($_prefixVariable9)), ENT_QUOTES, 'UTF-8');?>
" target="_blank"
												rel="noopener noreferrer nofollow">
												<img class="contacts__socials-icon" src="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['res']->value, ENT_QUOTES, 'UTF-8');?>
image/icon/youtube.svg" alt="">
											</a>
										</li>
									<?php }?>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="nav__form">
		<div class="g-wrap g-wrap--xl nav__form-wrap">
			<p class="g-title g-title--sm g-mb-0">
				<?php echo smarty_modifier_dict("misc.free_offer");?>

			</p>
			<form class="form form--cta ajax" action="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
/offer" method="post">
				<div class="form__row g-relative">
					<div class="form__item">
						<label class="form__label" for="header-name"><?php echo smarty_modifier_dict("contacts.names");?>
</label>
						<input class="form__input" type="text" id="header-name" name="name">
					</div>
					<div class="form__item">
						<label class="form__label" for="header-phone"><?php echo smarty_modifier_dict("misc.phone");?>
</label>
						<input class="form__input" type="text" id="header-phone" name="phone">
					</div>
					<div class="checkbox checkbox--white checkbox--absolute">
						<input class="checkbox__input" type="checkbox" id="header-terms" name="terms" value="1">
						<label class="checkbox__label" for="header-terms"><?php echo smarty_modifier_dict("contacts.terms2");?>
</label>
					</div>
					<div class="form__item">
						<button class="btn btn--white btn--md form__btn" type="submit">
							<?php echo smarty_modifier_dict("contacts.submit2");?>

							<i class="icon icon--arrow-btn-primary"></i>
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</nav>
<?php }
}
