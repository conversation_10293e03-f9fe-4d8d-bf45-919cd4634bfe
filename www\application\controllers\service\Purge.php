<?php

class Purge extends CI_Controller {

	public function __construct() {
		parent::__construct();

		if (!in_array($this->input->ip_address(), conf("service_allow_ip"))) {
			die("not allowed");
		}
	}

	public function smarty() {
		$this->load->library("ci_smarty", null, "view");
		$this->view->clear_cache();
	}

	public function bean() {
		// nothing yet
	}

	public function thumbnails($index="") {
		$this->load->library("Thumbnails");
		$this->thumbnails->clear_cache($index);
	}

	public function tmp() {
		$this->load->library("filemanager");
		$hours = conf("filemanager", "tmp_files_lifespan");

		$this->filemanager->garbage_collector("tmp", $hours);
	}

	public function connector() {
		$this->load->library("hubconnector");
		$this->hubconnector->purge_cache();
	}

	public function routing() {
		clear_routes_dyn();
	}

	public function all() {
		$this->smarty();
		$this->bean();
		$this->thumbnails();
		$this->tmp();
		$this->connector();
	}

}
