<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:39:36
  from "W:\work\dvm_finance\www\application\views\public\services-details.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6ba829a177_44059876',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4dfcd13e6ebfd4fc3f3e84392a540905e34e96d8' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\services-details.tpl',
      1 => 1751730453,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/feat-1.tpl' => 1,
    'file:./_partial/feat-4.tpl' => 1,
    'file:./_partial/feat-2.tpl' => 1,
    'file:./_partial/benefits.tpl' => 1,
    'file:./_partial/faq.tpl' => 1,
    'file:./_partial/contacts.tpl' => 1,
  ),
),false)) {
function content_686b6ba829a177_44059876 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_path')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.path.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_url')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.url.php';
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('wrapper_css', "g-bg-light-gray");
$_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_921611201686b6ba8299444_78868714', "content");
?>

<?php $_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_921611201686b6ba8299444_78868714 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm g-padded--no-bottom">
		<div class="g-wrap g-wrap--xl">
			<a class="btn g-mb-20" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_services']->value), ENT_QUOTES, 'UTF-8');?>
">
				<i class="icon icon--arrow-btn-left"></i>
				<span class="underline"><?php echo smarty_modifier_dict("misc.services_all");?>
</span>
			</a>
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
			<div class="g-head g-head--vertical g-mb-0 g-max-1400">
				<h2 class="g-title g-title--md g-title--underline-primary js-anim">
					<?php echo $_smarty_tpl->tpl_vars['node']->value->feat2;?>

				</h2>
				<div class="g-editor g-font-title g-max-900">
					<p>
						<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node']->value->text);?>

					</p>
				</div>
				<ul>
					<li>
						<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_contacts']->value), ENT_QUOTES, 'UTF-8');?>
">
							<?php echo smarty_modifier_dict("misc.free_consult");?>

							<i class="icon icon--chat-white"></i>
						</a>
					</li>
				</ul>
			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-1.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['feat']->value,'show_link'=>true,'show_title'=>false), 0, false);
?>

	<section class="g-padded--sm">
		<div class="g-wrap">
			<h2 class="g-title g-title--md js-anim">
				<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['params']->value->title, ENT_QUOTES, 'UTF-8');?>

			</h2>
			<div class="g-row">
				<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-4.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('list'=>$_smarty_tpl->tpl_vars['params_feat']->value), 0, false);
?>

			</div>
		</div>
	</section>
	<section class="g-padded--sm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-bg">
				<div class="g-padded--md g-wrap">
					<div class="g-head">
						<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['types']->value->title, ENT_QUOTES, 'UTF-8');?>

						</p>
						<h1 class="g-title g-title--md">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['types']->value->subtitle, ENT_QUOTES, 'UTF-8');?>

						</h1>
					</div>
					<div class="services services--inner">
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['textblocks']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
						<div class="services__item js-anim">
							<div class="services__col">
								<div class="services__content">
									<a <?php if ($_smarty_tpl->tpl_vars['i']->value->feat2) {?>href="<?php echo htmlspecialchars(smarty_modifier_url($_smarty_tpl->tpl_vars['i']->value->feat2), ENT_QUOTES, 'UTF-8');?>
"<?php }?> class="g-title g-title--sm g-mb-0 g-color-primary">
										<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>

									</a>
									<a class="services__img-box is-hidden--md" <?php if ($_smarty_tpl->tpl_vars['i']->value->feat2) {?>href="<?php echo htmlspecialchars(smarty_modifier_url($_smarty_tpl->tpl_vars['i']->value->feat2), ENT_QUOTES, 'UTF-8');?>
"<?php }?>>
										<img class="services__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'feat2'), ENT_QUOTES, 'UTF-8');?>
" alt="">
									</a>
									<div class="g-editor g-opacity-60">
										<p>
											<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

										</p>
									</div>
									<?php if ($_smarty_tpl->tpl_vars['i']->value->feat2) {?>
									<ul>
										<li>
											<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_url($_smarty_tpl->tpl_vars['i']->value->feat2), ENT_QUOTES, 'UTF-8');?>
">
												<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->feat1, ENT_QUOTES, 'UTF-8');?>

												<i class="icon icon--arrow-btn-white"></i>
											</a>
										</li>
									</ul>
									<?php }?>
								</div>
							</div>
							<a class="services__img-box is-hidden is-block--md" <?php if ($_smarty_tpl->tpl_vars['i']->value->feat2) {?>href="<?php echo htmlspecialchars(smarty_modifier_url($_smarty_tpl->tpl_vars['i']->value->feat2), ENT_QUOTES, 'UTF-8');?>
"<?php }?>>
								<img class="services__img" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['i']->value->image,'feat2'), ENT_QUOTES, 'UTF-8');?>
" alt="">
							</a>
						</div>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

					</div>
				</div>
			</div>
		</div>
	</section>
	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/feat-2.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['client']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/benefits.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/faq.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('node'=>$_smarty_tpl->tpl_vars['faq']->value), 0, false);
?>

	<?php $_smarty_tpl->_subTemplateRender("file:./_partial/contacts.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php
}
}
/* {/block "content"} */
}
