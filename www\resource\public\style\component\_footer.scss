/**********************************************************
 * 
**********************************************************/

$parent: '.footer';

#{$parent} {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-bottom: 2rem;

    &__bg-box {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
        }
    }

    &__bg {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    &__newsletter {
        display: flex;
        justify-content: center;
        padding: 5rem 2rem 8rem 2rem;
        background: linear-gradient(131.72deg, #F8E2AE 12.6%, #A88861 46.32%, #E6CE9D 84.43%);
        color: $color-white;
        border-radius: 3px;
    }

    &__newsletter-inner {
        display: flex;
        flex-direction: column;
        gap: 3vw;
        width: 100%;
        
    }

    &__logo-box {
        width: 15vw;
        min-width: 20rem;
    }

    &__wrap {
        width: 100%;
        height: 100%;
        background-color: $color-gray-light;
        border-radius: 3px;
    }

    &__container {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    &__nav {
        display: flex;
        flex-direction: column;
        gap: 4rem;
        margin-bottom: 6rem;
    }

    &__nav-list {
        display: flex;
        flex-direction: column;
        gap: 2.5rem;
    }

    &__nav-link {
        font-size: clamp(1.6rem, 1.5vw, 2rem);
        font-family: $font-title;
    }

    &__cta {
        display: flex;
        flex-direction: column;
        gap: 2.5rem;
        max-width: 55rem;
    }
    
	&__end {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 2rem;
        align-items: center;
        font-size: $font-body-sm;
        padding-bottom: 4rem;
        padding-top: 4rem;
        color: #899597;
    }

    &__end-item {
        line-height: 1.2em;
        padding: 0 1rem;
    }
    
    &__end-link {
        display: inline-flex;
        align-items: center;
        gap: 1rem;

        &:hover {
            text-decoration: underline;
        }
    }

    &__end-logo {
        display: inline-block;
        vertical-align: 0.7rem;

        &:hover {
            opacity: 0.7;
        }
    }

    @include media(768px) {
        &__container {
            flex-direction: row;
            gap: 2.5vw;
        }
    }
    /* 1024 up */
    @include media(xm) {
        &__newsletter-inner {
            max-width: 35rem;
        }
    }
}