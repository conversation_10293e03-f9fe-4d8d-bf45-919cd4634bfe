<?php

require_once "Base_edit.php";

abstract class Ajax_edit extends Base_edit {

	public function index() {
		$this->view->set("index_filters", $this->filters());
		$this->view->set("index_reorder", $this->reorder_target());
		$this->view->set("item", $this->create_object());

		$this->render("cms/{$this->page_path}/index");
	}

	public function list() {
		if (!$this->get_opt("view") || !$this->input->is_ajax_request()) {
			return;
		}
		$total = $this->get_count();
		$objects = $this->get_list();
		$this->view->set("objects", $objects);

		$res = array(
				"list" => $this->view->result("cms/{$this->page_path}/list", true),
				"count" => $total,
		);
		$this->json_response($res);
	}


	public function add() {
		if (!$this->get_opt("add") || !$this->input->is_ajax_request()) {
			return;
		}
		$object = $this->create_object();
		$object->set(_post_files_merge());
		$this->on_object_set("add", $object);

		$validator = $object->validator();
		if ($validator->validate()) {
			$this->data->add($object);
			$this->add_log_entry("add", sprintf("id=%s", $object->id));
			$this->on_object_saved("add", $object);

			$res = array(
					"success" => true,
			);

		} else {
			$res = array(
					"success" => false,
					"data" => $validator->errors(),
			);
		}
		$this->json_response($res);
	}

	public function edit() {
		if (!$this->get_opt("view") || !$this->input->is_ajax_request()) {
			return;
		}

		$id = _post("id");

		$this->json_response($this->render_one($id, "edit"));
	}

	public function cancel() {
		if (!$this->input->is_ajax_request()) {
			return;
		}

		$id = _post("id");

		$this->json_response($this->render_one($id, "view"));
	}

	public function edit_save() {
		if (!$this->get_opt("edit") || !$this->input->is_ajax_request()) {
			return;
		}
		
		$id = _post("id");

		$object = $this->get_object($id);
		$object->set(_post_files_merge());
		$this->on_object_set("edit", $object);

		if ($object) {
			$validator = $object->validator();
			if ($validator->validate()) {
				$this->add_log_entry("edit", sprintf("id=%s", $object->id));
				$this->data->update($object);
				$this->on_object_saved("edit", $object);

				$res = array(
						"success" => true,
						"data" => $this->render_one($id, "view"),
				);

			} else {
				$res = array(
						"success" => false,
						"data" => $validator->errors(),
				);
			}
			$this->json_response($res);
		}

	}

	public function delete() {
		if (!$this->get_opt("delete") || !$this->input->is_ajax_request()) {
			return;
		}

		$id = _post("id");
		$res = false;

		$object = $this->data->subscribers->id($id);
		if ($object) {
			$this->add_log_entry("delete", "id={$object->id}");
			$this->data->delete($object);
			$this->on_deleted($object);
			$res = true;
		}
		$this->json_response($res);
	}



	private function render_one($id, $mode) {
		$object = $this->data->subscribers->id($id);

		if ($object) {
			$this->view->set("mode", $mode);
			$this->view->set("item", $object);
			$this->view->set("index", "");

			return $this->view->result("cms/{$this->page_path}/row", true);
		}
		return "";
	}
}