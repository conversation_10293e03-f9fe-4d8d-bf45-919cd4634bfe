/**********************************************************
 * 
**********************************************************/

$parent: '.header';

#{$parent} {
    position: relative;
    @include transition-xs;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    background-color: $color-white;
    z-index: 10;

    &.-sticky {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 10;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    &.-active {
        .-close {
            display: inline-block;
        }

        .-open {
            display: none;
        }

        #{$parent}__hamburger-line {
            background-color: transparent;

            &:before {
                transform: rotate(135deg) translate(0.6rem, -0.5rem);
            }

            &:after {
                transform: rotate(-135deg) translate(0.6rem, 0.5rem);
                width: 100%;
            }

        }
    }

    &__wrap {
        display: flex;
        justify-content: space-between;
    }

    &__left,
    &__right {
        display: flex;
        gap: 2.5vw;
    }

    &__logo-box {
        width: 9vw;
        min-width: 12rem;
        align-self: center;
    }

    &__logo {
        display: block;
        width: 100%;
        font-size: clamp(1.3rem, 1vw, 1.5rem);
    }

    &__nav-trigger {
        display: inline-flex;
        align-items: center;
        color: #071B34;
        cursor: pointer;

        .-close {
            display: none;
        }

        &:hover {
            #{$parent}__hamburger-line {

                &::before,
                &::after {
                    width: 100%;
                }
            }
        }
    }

    &__hamburger {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 5.4rem;
        height: 5.4rem;
        border: 1px solid #D9D9D9;
        background-color: rgba(#EFEFEF, .3);
        backdrop-filter: blur(25px);
        padding: 1.5rem;
        border-radius: 3px;
    }

    &__hamburger-line {
        position: relative;
        width: 100%;
        height: 2px;
        border-radius: 10px;
        background-color: currentColor;

        &::before,
        &::after {
            @include transition-xs;
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            border-radius: 10px;
            background-color: #071B34;
        }

        &::before {
            top: -8px;
            left: 0;
            width: 100%;
        }

        &::after {
            bottom: -8px;
            right: 0;
        }
    }

    &__hamburger-txt {
        padding: 1.5rem 0.7vw;
        font-size: clamp(1.4rem, 1vw, 1.6rem);
        font-family: $font-title;
    }

    &__call {
        display: flex;
        align-items: center;
        gap: 1.5vw;
        padding: 8px 1.5rem;
        background-color: rgba(#EFEFEF, .3);
        backdrop-filter: blur(25px);
        border-radius: 3px;
        border: 1px solid #D9D9D9;
        font-family: $font-title;
        line-height: 1em;
        font-size: clamp(1.4rem, 1vw, 1.6rem);
    }

    &__call-txt {
        display: flex;
        flex-direction: column;
        // padding-right: 1.5vw;

    }

    &__call-num {
        padding-left: 1rem;
        border-left: 1px solid #D9D9D9;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    &__call-subtxt {
        font-size: clamp(1.1rem, 1vw, 1.3rem);
        font-family: $font-family;
    }

    &__cta {
        position: fixed;
        display: flex;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: $color-white;
        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
        z-index: 9;

        .btn {
            flex: 1;
            border-radius: 0;
            border: none;
        }
    }

    /* 480 up */
    @include media(sm) {}

    /* 640 down */
    @include media(md-down) {
        padding-top: 1rem;
        padding-bottom: 1rem;

        &__hamburger,
        &__call {
            transform: scale(0.8);
        }
    }

    /* 1024 up */
    @include media(xm) {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }

    /* 1280 up */
    @include media(lg) {}

    /* 1600 up */
    @include media(xl) {}
}