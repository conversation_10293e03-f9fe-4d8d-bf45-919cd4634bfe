$(document).ready(function () {

	// Blocks animation (img, div, .g-editor)
	$('.js-anim')
		.css({
			opacity: 0
		})
		.waypoint({
			handler: function () {
				anime({
					targets: this.element,
					translateY: [30, 0],
					opacity: [0, 1],
					easing: 'easeOutExpo',
					duration: 2500,
					delay: 200
				});

				// Fire only once
				this.destroy();
			},
			offset: '80%'
		});

	// Reverse direction block animations
	$('.js-anim-rev').not('.js-no-anim')
		.css({
			opacity: 0
		})
		.waypoint({
			handler: function () {
				anime({
					targets: this.element,
					translateY: [-50, 0],
					opacity: [0, 1],
					easing: 'easeOutExpo',
					duration: 2500,
					delay: 200
				});

				// Fire only once
				this.destroy();
			},
			offset: '80%'
		});

	$('.gallery__item').on('click', function () {
		$('.gallery__item').removeClass('gallery__item--active');
		$(this).addClass('gallery__item--active');
		const container = $('.gallery-container');
		const item = $(this);
		const offset = item.position().left + item.outerWidth() / 2 - container.width() / 2;

		container.animate({ scrollLeft: container.scrollLeft() + offset }, 500);
	});;

	gsap.registerPlugin(ScrollTrigger);

	$(".trigger-section").each(function (index, el) {
		ScrollTrigger.create({
			trigger: el,
			start: "top center",
			end: "bottom center",
			onEnter: () => {
				if (index % 2 === 0) {
					$(".g-pin-sign").addClass("switched");
				} else {
					$(".g-pin-sign").removeClass("switched");
				}
			},
			onLeaveBack: () => {
				if (index % 2 === 0) {
					$(".g-pin-sign").removeClass("switched");
				} else {
					$(".g-pin-sign").addClass("switched");
				}
			}
		});
	});

});