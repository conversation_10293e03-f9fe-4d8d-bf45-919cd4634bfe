<?php

/**
 * Returns the variable if it isset or the default value "def".
 * Shorthand for (isset($var) ? $var : $def). It can be used as isset_or($var, $def) without raising notice.
 * @param $var Variable to check.
 * @param $def Default value.
 * @return Variable if set or default value.
 */
function isset_or(&$var, $def=null) {
	return isset($var) ? $var : $def;
}

/**
 * Gets a value in an array, specified by the selector or the default value if the selector is not found. If selector is null, the original array will be returned.
 * @param array $arr The array to look into.
 * @param string $selector The selector. Can be a single value like "key", or an array of selectors for multi-dimensional arrays.
 * @param $def Default (coalesce) value if the selector is not existing in the array.
 * @return Found value or default value.
 */
function arr($arr, $selector=null, $def=null) {
	$current = $arr;
	if ($selector !== null) {
		$selector = (array)$selector;
		foreach ($selector as $s) {
			if (is_array($current) && array_key_exists($s, $current)) {
				$current = $current[$s];
			} else {
				return $def;
			}
		}
	}
	return $current;
}


/**
 * Gets a CI config value by the supplied cascade keys. Throws an exception if not found.
 * @param args The keys by which to find config value.
 * @return The config value.
 */
function conf() {
	$config =& get_config();

	$args = func_get_args();

	$res = $config;
	foreach ($args as $arg) {
		if (!is_array($res) || !array_key_exists($arg, $res)) {
			$tostring = implode(", ", $args);
			throw new Exception("Config key [{$tostring}] not found!");
		}
		$res = $res[$arg];
	}
	return $res;
}

function _get($key=null, $def="") {
	return arr($_GET, $key, $def);
}
function _get_arr($key=null, $def=array()) {
	return (array)_get($key, $def);
}

function _post($key=null, $def="") {
	return arr($_POST, $key, $def);
}
function _post_arr($key=null, $def=array()) {
	return (array)_post($key, $def);
}

function _files($key=null, $def="") {
	return arr($_FILES, $key, $def);
}
function _files_arr($key=null, $def=array()) {
	return (array)_files($key, $def);
}

function _get_post($key=null, $def="") {
	$res = _get($key, $def);
	if ($res === $def) {
		$res = _post($key, $def);
	}
	return $res;
}
function _get_post_arr($key=null, $def=array()) {
	return (array)_get_post($key, $def);
}

function _post_files_merge($key=null, $def="") {
	$post = (array)_post($key, array());
	$files = (array)_files($key, array());
	return array_merge_recursive_distinct($post, $files);
}

function _session($key=null, $def="", $flash=false) {
	$res = arr($_SESSION, $key, $def);
	if ($flash) {
		unset_session($key);
	}
	return $res;
}

function set_session($key, $value) {
	$_SESSION[$key] = $value;
}
function unset_session($key) {
	unset($_SESSION[$key]);
}

function _cookie($key=null, $def="") {
	return arr($_COOKIE, $key, $def);
}
function _server($key=null, $def="") {
	return arr($_SERVER, $key, $def);
}

function data_encrypt($data, $key) {
	return @openssl_encrypt($data, 'aes-256-cbc', $key);
}

function data_decrypt($data, $key) {
	return openssl_decrypt($data, 'aes-256-cbc', $key);
}

function my_random_string($length, $charset="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz") {
	$res = "";
	$count = strlen($charset);
	while ($length--) {
		$res .= $charset[mt_rand(0, $count-1)];
	}
	return $res;
}

/**
 * Similar to preg_replace but ensures that once a value is replaced part of it will not be replaced again, which could happen if a replacement value itself contains a match.
 * @param string $pattern Regex pattern.
 * @param string $subject The subject to replace into.
 * @param array $values The values to replace.
 * @return string The result string.
 */
function preg_replace_incremental($pattern, $subject, $values) {
	$matches = preg_match_capture($pattern, $subject);
	$res = "";
	$prev = 0;
	for ($i = 0, $count = count($matches[0]); $i < $count; $i++) {
		$current = mb_strlen($matches[0][$i][0]) + $matches[0][$i][1];
		$part = mb_substr($subject, $prev, $current - $prev);

		if (!array_key_exists($i, $values)) {
			continue;
		}

		$prev = $current;
		$value = $values[$i];
		$value = preg_replace('/\\\\/', '\\\\\\\\', $value);
		$res .= preg_replace("/".preg_quote($matches[0][$i][0])."/", $value, $part, 1);
	}
	$res .= mb_substr($subject, $prev);

	return $res;
}

/*
 * Same as preg_match_all with PREG_OFFSET_CAPTURE flag, but handles utf8 correctly
 */
function preg_match_capture($pattern, $subject) {
	
	$res = array();
	preg_match_all($pattern, $subject, $matches, PREG_OFFSET_CAPTURE);
	
	foreach ($matches as $i) {
		$row = array();
		foreach ($i as $j) {
			if (isset($j[0])) {
				$text = $j[0];
				$index = $j[1];
				$real_index = mb_strlen(mb_strcut($subject, 0, $index));
				$row[] = array(
						$text,
						$real_index,
				);
			}
		}
		$res[] = $row;
	}
	return $res;
}

/**
 * Same as base64_encode function, but replaces non-url safe characters with safe ones:
 * "+" => "-"
 * "/" => "/" (unchanged)
 * "=" => "_"
 * @param string The string to encode as base64.
 * @return string The encoded data.
 */
function base64_encode_urlsafe($data) {
	$res = base64_encode($data);
	return strtr($res, "+/=", "-/_");
}

/**
 * Same as base64_decode function, but replaces the url safe characters with original ones:
 * "-" => "+"
 * "/" => "/" (unchanged)
 * "_" => "="
 * @param string The string to encode as base64.
 * @return string The decoded data.
 */
function base64_decode_urlsafe($data) {
	$res = strtr($data, "-/_", "+/=");
	return base64_decode($res);
}

/**
 * Checks if the request comes from a bot, based on the user agent.
 * @return boolean True if bot is detected.
 */
function is_bot() {
	$crawlers_agents = "Google|msnbot|Rambler|Yahoo|AbachoBOT|accoona|AcioRobot|ASPSeek|CocoCrawler|Dumbot|FAST-WebCrawler|GeonaBot|Gigabot|Lycos|MSRBOT|Scooter|AltaVista|IDBot|eStyle|Scrubby";

	$ua = isset($_SERVER["HTTP_USER_AGENT"]) ? $_SERVER["HTTP_USER_AGENT"] : "";

	return strpos($crawlers_agents, $ua) !== false;
}

function array_merge_recursive_distinct($arr1, $arr2) {
	$res = $arr1;

	foreach ($arr2 as $key => &$value) {
		if (is_array($value) && isset($res[$key]) && is_array($res[$key])) {
			$res[$key] = array_merge_recursive_distinct($res[$key], $value);
		} else {
			$res[$key] = $value;
		}
	}

	return $res;
}

/**
 * Gets the secure URL for the current one
 */
function ssl_url($port="") {
	$host = arr($_SERVER, "HTTP_HOST");
	$host = preg_replace("/:[0-9]+/", "", $host);
	if ($port) {
		$host = ":{$port}";
	}
	return "https://".$host.$_SERVER['REQUEST_URI'];
}

/**
 * Redirects to https on the same URL
 */
function enforce_ssl($port="") {
	$https = strtolower(_server("HTTPS"));
	$is_on = ($https == "on" || $https == "1");
	if (!$is_on) {
		$url = ssl_url($port);
		redirect($url, "auto", 301);
	}
}

/**
 * Gets a node id by its name. Can accept multiple names as parameters and will return all corresponding ids.
 */
function nodeid() {
	$args = func_get_args();
	$nodes = conf("nodes");

	if (!$args) {
		return array_values($nodes);
	}

	if (count($args) == 1 && is_string($args[0])) {
		if (isset($nodes[$args[0]])) {
			return $nodes[$args[0]];
		}
		throw new Exception("Node' {$args[0]}' not found");
	}

	if (is_array($args[0])) {
		$args = $args[0];
	}

	$res = array();

	foreach ($args as $name) {
		if (isset($nodes[$name])) {
			$res[] = $nodes[$name];
		} else {
			throw new Exception("Node '{$name}' not found");
		}
	}
	return $res;
}

function nodename($node_id) {
	$args = func_get_args();
	$nodes = array_flip(conf("nodes"));

	if (isset($nodes[$node_id])) {
		return $nodes[$node_id];
	}
	throw new Exception("Node' {$node_id}' not found");
}

function clear_routes_dyn() {
	$file = conf("routes_dyn_compiled");
	if (file_exists($file)) {
		@unlink ($file);
	}
}

function jstest($input=null, $reset=true) {
	$key = conf("key_jstest");
	$val =  _session($key, null);
	if ($val === null) {
		$val = rand(0, 1000);
		set_session($key, $val);
	}
	
	if ($input === null) {
		return $val;
	} else {
		$res = (7 * $val == $input);
		if ($reset) {
			unset_session($key);
		}
		return $res;
	}
}

function calc_pmt($rate, $nper, $pv) {
	return ($pv * pow(1 + $rate, $nper)) / ((pow(1 + $rate, $nper) - 1) / $rate);
}
function calc_interest($rate, $periods, $total, $type) {
	$res = array();

	for ($i = 0; $i < $periods; $i++) {
		$l = $total * $rate / 12;
		
		$pmt = calc_pmt($rate / 12, $periods - $i, $total);
		$monthly = $pmt;
		$deduct = $monthly - ($type == "equal" ? $l : 0);

		$res[] = array(
			"principal" => $total,
			"interest" => $l,
			"deduct" => $deduct,
			"monthly" => $monthly + ($type == "equal" ? 0 : $l),
		);
		$total = $total - $deduct;
	 }
	 return $res;
}