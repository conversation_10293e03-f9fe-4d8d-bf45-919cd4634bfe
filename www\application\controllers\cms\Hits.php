<?php

require_once "base/Post_edit.php";

class Hits extends Post_edit {

	protected $languages;

	public function __construct() {
		parent::__construct();

		$this->languages = $this->data->languages->all();
		$this->view->set("languages", $this->languages);
	}

	protected function opts() {
		return array(
				"add"=>false,
				"edit"=>false,
				"delete"=>false,
		);
	}

	protected function get_count($filter) {
		return $this->data->hits->get_count($filter);
	}

	protected function get_list($filter, $order, $dir, $offset, $count) {
		return $this->data->hits->get_list($filter, $order, $dir, $offset, $count);
	}

	protected function filters() {
		return array (
				"view" => array("type"=>"select", "label"=>"Изглед", "opts"=>Data_hits::GROUPS, "opt_val"=>"#", "opt_text"=>"0", "opt_all"=>"подробен", "def"=>-1),
				"term" => array("type"=>"text", "label"=>"Търсене"),
				"language_id" => array("type"=>"select", "label"=>"Език", "opts"=>$this->languages, "opt_val"=>"id", "opt_text"=>"title"),
				"time" => array("type"=>"date", "label"=>"Дата", "range"=>1),
		);
	}
	protected function orders() {
		return array (
				"time" => "дата и час",
				"url" => "адрес",
				"lang" => "език" ,
				"ip" => "IP адрес" ,
		);
	}

	protected function direction() {
		return "desc";
	}

	protected function on_indexing($filter, array $objects) {
		$this->view->set("views", Data_hits::GROUPS);

		$view = $filter["view"];
		if ($view != -1) {
			$keys = __::map($objects, function($o) { return $o->key; });
			$newkeys = array();
			foreach ($keys as $k) {
				switch ($view) {
					case 1: $newkeys[] = conf("convert", "months", "bg", $k - 1); break;
					case 2: $newkeys[] = conv_date_string($k, "date", "bg"); break;
					case 3: $newkeys[] = conf("convert", "weekdays", "bg", $k - 1); break;
					case 6: $newkeys[] = __::find($this->languages, function($o) use($k) { return $o->id == $k; })->title; break;
					default: $newkeys[] = $k;
				}
			}

			$this->view->set("object_keys", $newkeys);

			$values = __::map($objects, function($o) { return $o->count; });
			$values = array_map("intval", $values);
			$this->view->set("object_values", $values);
		}
	}
}