/**********************************************************
 * Reverse column order up to a breakpoint
**********************************************************/

.g-invert,
.g-invert--sm,
.g-invert--md,
.g-invert--xm,
.g-invert--lg,
.g-invert--xl { flex-direction: column-reverse; }

@include media(sm) {
	.g-invert--sm { flex-direction: row; }
}
@include media(md) {
	.g-invert--md { flex-direction: row; }
}
@include media(xm) {
	.g-invert--xm { flex-direction: row; }
}
@include media(lg) {
	.g-invert--lg { flex-direction: row; }
}
@include media(xl) {
	.g-invert--xl { flex-direction: row; }
}