<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:39:39
  from "W:\work\dvm_finance\www\application\views\public\news-details.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6babb04300_38329488',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f101a9bc4a33462f83318f1ba17a0c931fac0893' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\news-details.tpl',
      1 => 1751730207,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
    'file:./_partial/share.tpl' => 1,
    'file:./_partial/news-item.tpl' => 1,
  ),
),false)) {
function content_686b6babb04300_38329488 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_image')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.image.php';
if (!is_callable('smarty_modifier_date')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.date.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('wrapper_css', "g-bg-light-gray");
$_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_18357926686b6babb03c74_08513746', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_18357926686b6babb03c74_08513746 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--sm">
		<div class="g-wrap">
			<img class="g-img g-mb-30" src="<?php echo htmlspecialchars(smarty_modifier_image($_smarty_tpl->tpl_vars['node']->value->image,'banner2'), ENT_QUOTES, 'UTF-8');?>
" alt="">
			<p class="g-uppercase g-font-title g-color-primary g-title--sub g-mb-0">
				<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

			</p>
		</div>
		<div class="g-wrap g-wrap--xm">
			<div class="g-head">
				<?php if ($_smarty_tpl->tpl_vars['node']->value->date) {?>
					<p class="article__date g-mb-30"><?php echo htmlspecialchars(smarty_modifier_date($_smarty_tpl->tpl_vars['node']->value->date,$_smarty_tpl->tpl_vars['lng']->value->code), ENT_QUOTES, 'UTF-8');?>
</p>
				<?php }?>
				<h3 class="article__title article__title--lg g-anim">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h3>
			</div>

			<div class="g-editor">
				<?php echo $_smarty_tpl->tpl_vars['node']->value->text;?>

			</div>
			<div class="g-padded--sm">
				<div class="article__share">
					<p class="article__share-label">
						<i class="icon icon--share"></i>
						<?php echo smarty_modifier_dict("misc.share");?>
:
					</p>
					<?php $_smarty_tpl->_subTemplateRender("file:./_partial/share.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('url'=>(urlencode($_smarty_tpl->tpl_vars['html_meta']->value['url'])),'text'=>$_smarty_tpl->tpl_vars['node']->value->title), 0, false);
?>

				</div>
			</div>
		</div>
	</section>
	<?php if ($_smarty_tpl->tpl_vars['list']->value) {?>
		<section class="g-padded--md g-padded--no-top">
			<div class="g-wrap g-wrap--xl">
				<div class="g-head g-mb-0">
					<p class="g-uppercase g-font-title g-color-primary g-title--sub g-mb-30">
						<?php echo smarty_modifier_dict("misc.news_more");?>

					</p>
				</div>
				<div class="g-row g-gap-1 article-carousel">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['list']->value, 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
						<?php $_smarty_tpl->_subTemplateRender("file:./_partial/news-item.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</div>
			</div>
		</section>
	<?php }
}
}
/* {/block "content"} */
}
