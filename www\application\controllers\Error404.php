<?php

require_once __DIR__ . "/Public_base.php";

class Error404 extends Public_base {
	public function __construct() {
		$script = isset($_SERVER["SCRIPT_NAME"]) ? $_SERVER["SCRIPT_NAME"] : "";
		$base = rtrim(str_replace(basename($script), "", $script), "/") . "/";
		$request_uri = isset($_SERVER["REQUEST_URI"]) ? $_SERVER["REQUEST_URI"] : "";
		$current = "/" . ($request_uri ? substr($request_uri, strlen($base)) : "");

		if (preg_match("|^/resource/(.+)$|", $current)) {
			CI_Controller::__construct();
		
			show_404();
		} else {
			Public_base::__construct();
		
			$this->_404();
		}
	}

	public function index() {

	}
}
