/**********************************************************
 * 
**********************************************************/

$parent: '.underline';

#{$parent} {
	align-items: center;
	display: inline-flex;
	gap: 1rem;
	position: relative;

	&::after {
		background-color: currentColor;
		content: '';
		bottom: -3px;
		height: 1px;
		left: 0;
		position: absolute;
		transform: scaleX(0);
		transform-origin: center bottom;
		transition: transform 0.2s;
		width: 100%;
	}

	&:hover::after {
		transform: scaleX(1);
	}

	&--shown {
		&::after {
			transform: scaleX(1);
		}

		&:hover::after {
			transform: scaleX(0.8);
		}
	}
}