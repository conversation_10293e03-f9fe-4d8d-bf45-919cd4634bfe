<?php

class MY_Log extends CI_Log {
	public function __construct() {
		parent::__construct();
		
		$this->_levels["CUSTOM"] = 5;

		if (!is_dir($this->_log_path)) {
			$mask = umask(0);
			mkdir($this->_log_path);
			umask($mask);
			
			if (!is_really_writable($this->_log_path)) {
				die ("Logs directory is not writeable!");
			}
		}
	}

	function write_log($level = "error", $msg, $php_error = FALSE) {
		// if verbose is enabled, built in logger is practically disabled
		// only if level="error" and only if CI is loaded
		if ($level == "error" && class_exists("CI_Controller") && get_instance() && get_instance()->config && config_item("error_verbose")) {
			$res = $this->log_error_verbose($level, $msg, $php_error);
			if ($res) {
				return true;
			}
		}

		return parent::write_log($level, $msg, $php_error);
	}

	private function log_error_verbose($level, $msg, $php_error) {
		$file = config_item("error_file");
		$no_email = file_exists(get_instance()->config->item("cache_dir") . "stop_email_log");
		$email = config_item("error_email") && !$no_email;

		if ($file || $email) {
			$msg = self::get_verbose($msg);

			if ($file) {
				parent::write_log($level, $msg, $php_error);
			}

			if ($email) {
				self::send_log($msg);
			}
			return true;
		}
		return false;
	}

	public function log_cronjob($name, $text, $max_lines=0) {
		if (!$max_lines) {
			$max_lines = config_item("cron_logger_max_lines");
		}
		$filepath = $this->_log_path.$name.".log";

		$lines = array();
		if (file_exists($filepath)) {
			$lines = explode("\n", file_get_contents($filepath));
		}

		$lines[] = date("Y-m-d H:i:s") . " " . $text;

		$total = count($lines);

		array_splice($lines, 0, -1 * $max_lines);

		file_put_contents($filepath, implode("\n", $lines));
	}

	private static function send_log($text) {
		$config = get_config();

		$to = $config["error_email"];
		$app_domain = $config["app_domain"];

		$subject = "Error on ". $app_domain;

		send_email($subject, $text, $to, "", "text");
	}

	private static function get_verbose($text) {
		$NL = "\r\n";

		global $argv;

		$text .= $NL;
		$text .= $NL."CLIENT IP: "._server("REMOTE_ADDR");
		$text .= $NL."URL: "."http://"._server("HTTP_HOST")._server("REQUEST_URI");
		$text .= $NL."SCRIPT: ". _server("SCRIPT_FILENAME");
		$text .= $NL."AJAX: ". (_server("HTTP_X_REQUESTED_WITH") == "XMLHttpRequest" ? "true" : "false");
		$text .= $NL."QUERY STRING: ". _server("QUERY_STRING");
		$text .= $NL."GET: ";
		$text .= str_replace("\n", $NL, var_export($_GET, true));
		$text .= $NL."POST: ";
		$text .= str_replace("\n", $NL, var_export($_POST, true));
		//$text .= $NL."SESSION:".$NL;
		//$text .= str_replace("\n", $NL, var_export($_SESSION, true));
		$text .= $NL."FILES: ";
		$text .= str_replace("\n", $NL, var_export($_FILES, true));
		$text .= $NL."ARGV: ";
		$text .= str_replace("\n", $NL, var_export($argv, true));
		$text .= $NL."HOST: ";
		$text .= str_replace("\n", $NL, php_uname("n"));
		$text .= $NL."USER: ";
		$text .= str_replace("\n", $NL, exec("whoami"));
		$text .= $NL."TRACE:".$NL;
		$text .= self::get_stacktrace(7);
		$text .= $NL;
		return $text;
	}

	private static function get_stacktrace($traces_to_ignore=0){
		$traces = debug_backtrace();
		$res = array();
		$j = 0;
		for ($i = $traces_to_ignore; $i < count($traces); $i++) {
			$call = $traces[$i];

			$start = "#".str_pad($j++, 3, " ");
			$function = $call["function"];
			$file = isset($call["file"]) ? $call["file"] : "";
			$line = isset($call["line"]) ? $call["line"] : "";
			$res[] = $start.$function."() called at [".$file.":".$line."]";
		}
		return implode("\n", $res);
	}

	public function write_custom($name, $msg)
	{
		$filepath = $this->_log_path.$name.'-'.date('Y-m-d').'.'.$this->_file_ext;
		$message = '';
		
		if ( ! file_exists($filepath))
		{
			$newfile = TRUE;
			// Only add protection to php files
			if ($this->_file_ext === 'php')
			{
				$message .= "<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>\n\n";
			}
		}
		
		if ( ! $fp = @fopen($filepath, 'ab'))
		{
			return FALSE;
		}
		
		flock($fp, LOCK_EX);
		
		// Instantiating DateTime with microseconds appended to initial date is needed for proper support of this format
		if (strpos($this->_date_fmt, 'u') !== FALSE)
		{
			$microtime_full = microtime(TRUE);
			$microtime_short = sprintf("%06d", ($microtime_full - floor($microtime_full)) * 1000000);
			$date = new DateTime(date('Y-m-d H:i:s.'.$microtime_short, $microtime_full));
			$date = $date->format($this->_date_fmt);
		}
		else
		{
			$date = date($this->_date_fmt);
		}
		
		$message .= ($date.' --> '.$msg."\n");
		
		for ($written = 0, $length = strlen($message); $written < $length; $written += $result)
		{
			if (($result = fwrite($fp, substr($message, $written))) === FALSE)
			{
				break;
			}
		}
		
		flock($fp, LOCK_UN);
		fclose($fp);
		
		if (isset($newfile) && $newfile === TRUE)
		{
			chmod($filepath, $this->_file_permissions);
		}
	}
}
