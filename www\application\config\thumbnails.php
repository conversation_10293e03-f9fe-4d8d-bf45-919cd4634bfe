<?php

/*
 * additional thumbnail options example:
 * array("force"=>1, "fill"=>array(192, 192, 32), "watermark"=>"resource/image/user/wm.png", "position"=>5, "rotate"=>"90", "quality"=>90);
 *
 * thumbnail set example:
 * "banner_top" => array("320w" => "banner1", "640w" => "banner2")
*/

$config["thumbnails"]["sizes"] = array(
	"cms" => array(100, 100, "scale"),
	"cms_gallery" => array(60, 60, "scale"),
	"favicon" => array(48, 48, "crop", array("format"=>"ico")),
	"favicon16" => array(16, 16, "crop"),
	"favicon32" => array(32, 32, "crop"),
	"favicon192" => array(192, 192, "crop"),
	"social" => array(600, 600, "scale"),
	"full" => array(1600, 1600, "scale", array("force"=>0)),
	"banner1" => array(1920, 1250, "crop"),
	"banner2" => array(1380, 560, "crop"),
	"banner3" => array(1660, 550, "crop"),
	"banner4" => array(1920, 820, "crop"),
	"banner5" => array(1660, 800, "crop"),
	"banner6" => array(1380, 420, "crop"),
	"news" => array(540, 270, "crop"),
	"icon" => array(24, 24, "scale"),
	"icon2" => array(55, 55, "scale"),
	"feat1" => array(580, 700, "crop"),
	"feat2" => array(820, 550, "crop"),
	"feat3" => array(360, 400, "crop"),
	"feat4" => array(400, 520, "crop"),
	"feat5" => array(225, 290, "crop"),
	"signature" => array(220, 180, "scale"),
	"service" => array(440, 550, "crop"),
	"logo" => array(9999, 50, "scale"),
);

//$config["thumbnails"]["force_format"] = null;
$config["thumbnails"]["force_format"] = "webp";

$config["thumbnails"]["sets"] = array(
);



$config["thumbnails"]["use_cache"] = true;
$config["thumbnails"]["cache_dir"] = get_instance()->config->item("cache_dir") . "/thumbnails/";

$config["thumbnails"]["cache_sharding_level"] = 2;
$config["thumbnails"]["gc_threshold"] = 24 * 20; // in hours
$config["thumbnails"]["cache_interval"] = 24 * 20; // in hours

$config["thumbnails"]["exif_set"] = true;
$config["thumbnails"]["exif_software"] = "jani.cc";
$config["thumbnails"]["exif_copyright"] = "jani.cc";

merge_env(__FILE__, $config);
