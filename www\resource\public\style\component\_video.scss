
$parent: '.video';

#{$parent} {
    background-color: black;
    display: block;
    overflow: hidden;
    position: relative;

    &:hover {
        #{$parent}__play {
            transform: translate(-50%, -50%) scale(0.95);
        }
        #{$parent}__preview {
            opacity: 0.6;
            transform: scale(1.05);
        }
    }

    &__play {
        @include transition-sm;
        @include circle(76);
        background-color: white;
        background-size: auto;
        left: 50%;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
    }

    &__preview {
        @include transition-md;
        display: block;
        transform: scale(1.000001);
        width: 100%;
    }
}