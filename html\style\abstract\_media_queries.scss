@mixin media($breakpoint, $direction:false) {
	// No actual media query passed
	@if $breakpoint == false or $breakpoint == '' {
		@content;
	}
	@else if $breakpoint == 'xl' {
		@media only screen and (min-width : #{$width-xl + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'xl-down' {
		@media only screen and (max-width: #{$width-xl}) {
			@content;
		}
	}
	@else if $breakpoint == 'lg' {
		@media only screen and (min-width : #{$width-lg + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'lg-down' {
		@media only screen and (max-width: #{$width-lg}) {
			@content;
		}
	}
	@else if $breakpoint == 'xm' {
		@media only screen and (min-width : #{$width-xm + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'xm-down' {
		@media only screen and (max-width: #{$width-xm}) {
			@content;
		}
	}
	@else if $breakpoint == 'xm-only' {
		@media only screen and (min-width: #{$width-xm + 1px}) and (max-width: #{$width-lg})  {
			@content;
		}
	}
	@else if $breakpoint == 'md' {
		@media only screen and (min-width: #{$width-md + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'md-down' {
		@media only screen and (max-width: #{$width-md}) {
			@content;
		}
	}
	@else if $breakpoint == 'md-only' {
		@media only screen and (min-width: #{$width-md + 1px}) and (max-width: #{$width-xm}) {
			@content;
		}
	}
	@else if $breakpoint == 'sm' {
		@media only screen and (min-width : #{$width-sm + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'sm-down' {
		@media only screen and (max-width: #{$width-sm}) {
			@content;
		}
	}
	@else if $breakpoint == 'sm-only' {
		@media only screen and (min-width : #{$width-sm + 1px}) and (max-width: #{$width-md}) {
			@content;
		}
	}
	@else if $breakpoint == 'xs' {
		@media only screen and (min-width : #{$width-xs + 1px}) {
			@content;
		}
	}
	@else if $breakpoint == 'xs-down' {
		@media only screen and (max-width: #{$width-xs}) {
			@content;
		}
	}
	@else if $breakpoint == 'xs-only' {
		@media only screen and (min-width : #{$width-xs + 1px}) and (max-width: #{$width-sm}) {
			@content;
		}
	}
	// Unit passed - e.g. 600px, 60rem, etc.
	@else {
		@if $direction == 'down' {
			@media only screen and (max-width: #{$breakpoint}) {
				@content;
			}
		}
		@else {
			@media only screen and (min-width: #{$breakpoint}) {
				@content;
			}
		}
	}
}