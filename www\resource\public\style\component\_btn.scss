/**********************************************************
 * 
**********************************************************/

$parent: '.btn';

#{$parent} {
	@include transition-xs;
	font-family: $font-title;
	align-items: center;
	display: inline-flex;
	cursor: pointer;
	background: none;
	border: none;
	justify-content: center;
	line-height: 1em;
	pointer-events: all;
	position: relative;
	white-space: nowrap;
	border-radius: 3px;
	gap: 1rem;

	&:focus {
		outline: none;
	}
	

	&--sm {
		
	}

	&--md {
		padding: 1rem 1rem 1rem 1.5rem;
	}

	&--lg {
		padding: 1.5rem 2rem;
	}

	&--full {
		width: 100%;
	}

	&--black {
		background-color: $color-black;
		color: $color-white;
		border: 1px solid $color-black;
		&:hover {
			background-color: transparent;
			color: $color-black;
			i {
				filter: invert(1);
			}
		}
	}

	&--white {
		background-color: $color-white;
		color: $color-black;
		border: 1px solid $color-white;
		&:hover {
			background-color: $color-primary;
			color: $color-white;
			i {
				 @include icon('arrow-btn-white', 24, 24);
			}
		}
	}

	&--primary {
		background-color: $color-primary;
		color: $color-white;
		border: 1px solid $color-primary;
		&:hover {
			background-color: $color-white;
			color: $color-primary;
			i {
				@include icon('arrow-btn-primary', 24, 24);
			}
		}
	}

	
	&--cta {
		border: 1px solid $color-primary;
		color: $color-primary;
		font-size: clamp(1.4rem, 1vw, 1.6rem);
		&:hover {
			background-color: $color-primary;
			color: $color-white;
			i {
				@include icon('chat-white', 24, 24);
			}
		}
	}

	&--partner {
		font-size: clamp(1.4rem, 1vw, 1.6rem);
	}


	.icon,
	&__icon {
		@include transition-xs;
		display: inline-block;
	}

	/* Wrap 2 buttons together */
	&__wrap {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;	
	}
}