.facts {
    .slick-list {
        margin-right: -2rem;
    }
    &__slide {
        display: inline-flex !important;
        flex-direction: column;
        gap: 1.5rem;
        // width: min-content;
        margin-right: 2rem;
    }

    &__img-box {
        position: relative;
        width: 100%;
        height: 40rem;
        border-radius: 3px;
        overflow: hidden;
        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
        }
    }

    &__img {
        display: block;
        object-fit: cover;
        width: 100%;
        height: 100%;
    }

    &__content {
        position: absolute;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 1.3rem 1.5vw;
        color: $color-white;
        font-family: $font-title;
        z-index: 1;
    }

    &__stats {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #FFFFFF33;
        border: 1px solid #FFFFFF4D;
        padding: 1.5rem 1vw;
        gap: 1.2rem;
        font-size: clamp(1.3rem, 1.5vw, 1.5rem);
        border-radius: 3px;
        backdrop-filter: blur(25px);
    }

    &__title {
        font-size: clamp(1.8rem, 1.5vw, 2rem);
    }
}