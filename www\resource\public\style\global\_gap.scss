/**********************************************************
 * Gutters between columns
**********************************************************/

$sizes: '', 'xs', 'sm', 'md', 'xm', 'lg', 'xl';


$parent: '.g-gap';

@each $size in $sizes {
	@include media($size) {
		$breakpoint: -#{$size};
		// Avoid '.g-gap--1' output for first size (no size passed) - trim one '-'
		@if $size == '' {
			$breakpoint: #{$size};
		}
		

		#{$parent}#{$breakpoint} { // example output: .g-gap-sm or .g-gap
			@for $i from 0 through 5 {
				&-#{$i} { // example output: .g-gap-sm-0 or .g-gap-0
					// Remove spacing before first and after last column
					margin-left: -#{$i}vw;
					margin-right: -#{$i}vw;
					max-width: none;
					width: auto;
			
					> [class^="col-"],
					> [class*=" col-"] {
						padding-left: #{$i}vw;
						padding-right: #{$i}vw;
					}
				}
			}
		}
	
		#{$parent}-v#{$breakpoint} { // example output: .g-gap-v-sm or .g-gap-v
			@for $i from 0 through 5 {
				&-#{$i} { // example output: .g-gap-v-sm-0 or .g-gap-v-0
					> [class^="col-"],
					> [class*=" col-"] {
						margin-bottom: #{$i*2}rem;
					}
				}
			}
		}
	}
}

// Keep spacing before first and after last column
#{$parent}--keep {
	margin-left: 0;
	margin-right: 0;
}