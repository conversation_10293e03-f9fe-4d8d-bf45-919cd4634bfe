<?php
/* Smarty version 3.1.30, created on 2025-07-07 09:38:44
  from "W:\work\dvm_finance\www\application\views\public\_partial\faq.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_686b6b742c7681_71581456',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '47a8be903aa2a9e52217506ab96b6777afadcb8f' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\_partial\\faq.tpl',
      1 => 1751730224,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_686b6b742c7681_71581456 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
if (!is_callable('smarty_modifier_path')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.path.php';
if (!is_callable('smarty_modifier_dict')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.dict.php';
?>
<section class="g-padded--sm trigger-section">
	<div class="g-wrap g-wrap--xl">
		<div class="g-row g-gap-1 g-gap-v-1 g-gap-v-xm-0">
			<div class="col-1 col-xm-1-2">
				<div class="g-max-700">
					<div class="g-head">
						<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

						</p>
						<h2 class="g-title g-title--md g-title--underline js-anim">
							<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->subtitle, ENT_QUOTES, 'UTF-8');?>

						</h2>
					</div>
					<div class="g-editor g-mb-30">
						<p>
							<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node']->value->text);?>

						</p>
					</div>
					<ul>
						<li>
							<a class="btn btn--md btn--primary" href="<?php echo htmlspecialchars(smarty_modifier_path($_smarty_tpl->tpl_vars['node_faq']->value), ENT_QUOTES, 'UTF-8');?>
">
								<?php echo smarty_modifier_dict("misc.faq_all");?>

								<i class="icon icon--arrow-btn-white"></i>
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div class="col-1 col-xm-1-2">
				<ul class="accordion accordion--horizontal fade-up">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['node']->value->children(), 'i');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['i']->value) {
?>
						<li class="accordion__item">
							<button class="accordion__control">
								<p><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['i']->value->title, ENT_QUOTES, 'UTF-8');?>
</p>
							</button>
							<div class="accordion__panel">
								<div class="g-editor">
									<p>
										<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['i']->value->text);?>

									</p>
								</div>
							</div>
						</li>
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl);
?>

				</ul>
			</div>
		</div>
	</div>
</section><?php }
}
