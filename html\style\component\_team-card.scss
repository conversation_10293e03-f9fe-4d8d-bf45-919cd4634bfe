$parent: '.team-card';

#{$parent} {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 42rem;
    width: 100%;
    background-color: rgba(#FFFFFF, .6);
    backdrop-filter: blur(60px);
    border: 1px solid rgba(#FFFFFF, .3);
    border-radius: 3px;
    margin-left: 2rem;
    margin-right: 2rem;
    padding: 3rem 2.5rem 3rem 6rem;
    z-index: 1;

    &--left {

        #{$parent}__signature {
            right: 2rem;
            bottom: -2.5rem;
        }
    }

    &--right {


        #{$parent}__signature {
            right: 0;
            bottom: -6rem;
        }
    }


    &__icon,
    &__signature {
        position: absolute;
    }

    &__icon {
        width: 2.4rem;
        top: 2rem;
        left: 2rem;
    }

    &__signature {
        width: 50%;
    }

    &__name {
        font-size: clamp(2.2rem, 2vw, 2.8rem);
        font-family: $font-title;
    }

    /* 1024 down */
    @include media(xm-down) {
        &--left {
            margin-bottom: -3rem;
        }
        &--right {
            margin-top: -3rem;
            margin-bottom: 10rem;
        }
    }

    /* 480 down */
    @include media(sm-down) {
        max-width: 90vw;
    } 

    /* 1024 up */
    @include media(xm) {
        position: absolute;
        width: 22vw;
        min-width: 25rem;
        padding: 4.7rem 3vw;

        &--left {
            top: 10%;
            left: 2rem;
        }

        &--right {
            top: 30%;
            right: 2rem;
        }
    }

    /* 1280 up */
    @include media(lg) {
        min-width: 35rem;

        &--left {
            top: 50%;
            left: 7%;
        }

        &--right {
            top: 60%;
            right: 7%;
        }
    }
}