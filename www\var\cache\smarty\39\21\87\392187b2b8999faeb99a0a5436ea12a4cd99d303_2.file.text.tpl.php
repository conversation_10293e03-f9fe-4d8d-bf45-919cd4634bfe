<?php
/* Smarty version 3.1.30, created on 2025-07-04 14:47:50
  from "W:\work\dvm_finance\www\application\views\public\text.tpl" */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.30',
  'unifunc' => 'content_6867bf6691c266_80793518',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '392187b2b8999faeb99a0a5436ea12a4cd99d303' => 
    array (
      0 => 'W:\\work\\dvm_finance\\www\\application\\views\\public\\text.tpl',
      1 => 1751629669,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:./base.tpl' => 1,
  ),
),false)) {
function content_6867bf6691c266_80793518 (Smarty_Internal_Template $_smarty_tpl) {
if (!is_callable('smarty_modifier_pt')) require_once 'W:\\work\\dvm_finance\\www\\application\\libraries\\smarty_plugins\\modifier.pt.php';
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, true);
?>


<?php $_smarty_tpl->_assignInScope('header_cta_css', "g-pin-sign--right");
$_smarty_tpl->_assignInScope('footer_cta', true);
?>

<?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_15380134026867bf6691be42_35150376', "content");
$_smarty_tpl->inheritance->endChild();
$_smarty_tpl->_subTemplateRender("file:./base.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 2, false);
}
/* {block "content"} */
class Block_15380134026867bf6691be42_35150376 extends Smarty_Internal_Block
{
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

	<section class="g-padded--xm">
		<div class="g-wrap g-wrap--xl">
			<div class="g-head g-head--inner g-color-primary">
				<h1 class="g-title g-title--xm g-title--sub">
					<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['node']->value->title, ENT_QUOTES, 'UTF-8');?>

				</h1>
			</div>
		</div>
		<div class="g-wrap g-wrap--xm">
			<div class="g-padded--xs">
				<p class="g-uppercase g-font-title g-mb-20 g-color-primary g-title--sub">
					<?php echo smarty_modifier_pt($_smarty_tpl->tpl_vars['node']->value->preview);?>

				</p>
				<div class="g-editor">
					<?php echo $_smarty_tpl->tpl_vars['node']->value->text;?>

				</div>
			</div>
		</div>
	</section>
<?php
}
}
/* {/block "content"} */
}
