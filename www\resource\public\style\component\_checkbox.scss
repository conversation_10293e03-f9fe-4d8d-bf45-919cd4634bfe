/**********************************************************
 * Hide native checkbox, use label before/after
**********************************************************/

$parent: '.checkbox';

#{$parent} {
	margin-bottom: 0.5rem;
	min-height: 2.5rem;

	&--primary {
		#{$parent}__label {
			&::before {
				border-color: $color-primary;
			}
			&::after {
				background-color: $color-primary;
			}
		}
	}

	&--absolute {
		position: absolute;
		bottom: -1.5rem;
		left: 0;
		transform: translateY(100%);
	}

	&--white {
		#{$parent}__label {
			&::before {
				border-color: white;
			}	
			&::after {
				background-color: white;
			}
		}
	}


	&__input {
		display: none;

		/* Selected states */
		&:checked + #{$parent}__label {
			&::before {
				border-color: black;
			}

			&::after {
				opacity: 1;
				visibility: visible;
			}
		}
	}

	&__label {
		cursor: pointer;
		display: inline-block;
		line-height: 1.2em;
		padding-left: 2.5rem;
		position: relative;
		text-align: left;
		font-size: 1.5rem;

		&::before, &::after {
			content: '';
			display: inline-block;
			left: 0;
			position: absolute;
			top: 3px;
			border-radius: 1rem;
		}

		&::before {
			border: 1px solid black;
			height: 1rem;
			width: 1rem;
			border-radius: 50%;
		}

		&::after {
			@include transition-xs;
			background: black url('#{$img-dir}/icon/check-sm-white.svg') no-repeat  center;
			background-size: 50%;
			display: inline-block;
			height: 1.2rem;
			opacity: 0;
			visibility: hidden;
			width: 1.2rem;
		}
		
		/* Radio buttons */
		&--radio {
			&::before, &::after {
				border-radius: 1rem;
			}
		}

		/* Prevent selection upon fast clicking */
		&::selection {
			background-color: none;
		}

		a {
			text-decoration: underline;

			&:hover {
				opacity: 0.6;
			}
		}
	}
}