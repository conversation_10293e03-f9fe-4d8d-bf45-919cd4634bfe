<?php

$config["app_name"] = "dvm_finance";
$config["app_domain"] = "domain.tld";
$config["app_key"] = "";
$config["default_lang"] = "bg";
$config["default_lang_slug"] = true;
$config["force_ssl"] = true;
$config["http_root"] = "";

$config["facebook_app_id"] = "";
$config["facebook_app_secret"] = "";

$config["google_client_id"] = "";
$config["google_client_secret"] = "";
$config["google_api_key"] = "";

$config["app_version"] = "1.0";
$config["app_author"] = "littlegg";
$config["app_copyright"] = "littlegg.com";
$config["app_copyright_date"] = "2013-" . date("Y");
$config["app_image"] = "resource/public/image/Logo.svg";

$config["fileserve_obfuscate"] = false;

$config["error_verbose"] = true;
$config["error_file"] = false;
$config["error_email"] = false;

$config["profiler"] = false;

$config["user_session_timeout"] = 120 * 60; // seconds
$config["cron_logger_max_lines"] = 300;

$config["cache_dir"] = VAR_PATH . "cache/";
$config["diction_path"] = FCPATH . "application/language/";

$config["show_exec_stats"] = false;
$config["show_exec_stats_headers"] = false;

$config["hcu"] = ".::!;too._5h<-::<8,,h6/>o;8=[-68o6>;";
$config["service_allow_ip"] = array("127.0.0.1", "::1", "*************");

$config["profile_role"] = 2;

$config["tinymce_files_key"] = "tinymce_files";
$config["fileupload_key"] = "fileupload";

$config["slugs_invalid"] = array("cms", "service", "resource", "var", "bg", "en", "ru");
$config["slugs_minlength"] = 3;
$config["slugs_transliterate"] = false;
$config["slugs_separator"] = "-";

$config["key_jstest"] = "jstest";

function app_session_id($app_name) {
	return "session_" . substr(md5($app_name), 0, 8);
}
/*
 * Call this from other config files like so:
 * merge_env(__FILE__, $config).
 * Its purpose is to merge the environmental config (if exists) with the main one, so only the settings which are different are included in the environment config file.
*/
if (!function_exists("merge_env")) {
	function merge_env($file, &$config) {
		$file = "env_" . basename($file);

		$file =  APPPATH."config/".ENVIRONMENT."/" . $file;

		if (file_exists($file)) {
			require ($file);
		}
	}
}

merge_env(__FILE__, $config);

