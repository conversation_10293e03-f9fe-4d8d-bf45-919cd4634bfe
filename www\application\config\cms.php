<?php

$config["cms"]["menu"] = array(
	array("title" => "Съдържание", "url" => "main", "items" =>
		array(
			array("title" => "Страници", "url" => "pages"),
			array("title" => "Блокове", "url" => "blocks"),
			array("title" => "Услуги", "url" => "services"),
			array("title" => "FAQ", "url" => "faq"),
			array("title" => "Новини", "url" => "news"),
			array("title" => "Текстови страници", "url" => "textpages"),
			array("title" => "Текстове", "url" => "texts"),
			array("title" => "Абонаменти", "url" => "subscribers"),
		)
	),
	array("title" => "Настройки", "url" => "configuration", "items" =>
		array(
			//array("title" => "Преводи и фрази", "url" => "words"),
			array("title" => "Администратори", "url" => "administrators"),
			array("title" => "Имейл адреси", "url" => "emails"),
			array("title" => "SEO", "url" => "seo"),
			array("title" => "Custom код", "url" => "custom_codes"),
			array("title" => "Социални мрежи", "url" => "social"),
			array("title" => "Други", "url" => "settings"),
		)
	),
	array("title" => "Хронология", "url" => "history", "items" =>
		array(
			array("title" => "CMS дневник", "url" => "cmslog"),
			array("title" => "Сесии", "url" => "sessions"),
			array("title" => "Посещения", "url" => "hits"),
			array("title" => "Изпратени имейли", "url" => "email_log"),
			array("title" => "Запитвания", "url" => "inquiries"),
		)
	),
);

// should be equal to the path in routes.php
$config["cms"]["path"] = "cms/";

$config["cms"]["name"] = "littleCMS";
$config["cms"]["role"] = 1;
$config["cms"]["rights"] = array("none" => "няма", "view" => "преглед", "edit" => "редакция", "full" => "пълни");
$config["cms"]["lang_id"] = 1;
$config["cms"]["lang_code"] = "bg";
$config["cms"]["skin"] = "littlegg";
$config["cms"]["freeze"] = false;

$config["cms"]["page_sizes"] = array(50, 200, 1000);
$config["cms"]["opt_bool"] = array(1=>"- да -", 0=>"- не -");
$config["cms"]["opt_direction"] = array("asc"=>"възходящо", "desc"=>"низходящо");

$config["cms"]["key_status"] = "cms_status";
$config["cms"]["key_filter"] = "cms_filter";

merge_env(__FILE__, $config);
