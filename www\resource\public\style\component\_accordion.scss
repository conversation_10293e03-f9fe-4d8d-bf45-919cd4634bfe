$parent: '.accordion';

#{$parent} {
    position: relative;
    width: 100%;
    list-style: none;
    z-index: 3;

    &--team {
        .-active-control {
            h2 {
                color: $color-black;
            }
        }
        #{$parent}__control {
            flex-direction: column;
            gap: 0;

            &::after {
                display: none;
            }
        }

        #{$parent}__item {
            border-bottom: none;
        }

        #{$parent}__panel {
            color: $color-black;
        }
    }

    &__box {
        position: relative;
        padding: 6rem 7vw;
        background-color: $color-white;
        border-radius: 2rem;

        &:after {
            content: '';
            position: absolute;
            height: 120%;
            aspect-ratio: 1 / 1;
            right: -5%;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 50%;
            background-color: rgba($color-primary, 0.15);
            filter: blur(50px);
            z-index: -1;
        }
    }


    &__item {
        position: relative;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border-bottom: 1px solid rgba($color-black, .2)
    }

    &__control {
        @include transition-sm;
        position: relative;
        display: flex;
        gap: 2.5rem;
        padding: 2.3rem 2.5rem 2.3rem 0;
        font-size: clamp($font-body-sm, 1.5vw, $font-body-md);
        color: $color-black;
        text-align: left;
        border: none;
        font-weight: 400 !important;
        cursor: pointer;

        &::after {
            @include transition-xs;
            content: "";
            position: absolute;
            width: 17px;
            height: 17px;
            top: 50%;
            right: 0;
            transform: translateY(-50%) rotate(0deg);
            background-image: url(../image/icon/accordion-arrow.svg);
            background-repeat: no-repeat;
            background-position: center;
        }
    }

    &__img-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
    }

    &__img {
        display: block;
        height: 100%;
    }

    .-active-control {
        width: 100%;
        color: $color-primary;
        font-weight: bold !important;
        &::after {
            transform: translateY(-50%) rotate(180deg);
        }
    }

    &__panel {
        display: none;
        width: 100%;
        padding: 2rem 0;
        line-height: 1.5em;
        font-size: clamp($font-body-lg, 1.5vw, $font-title-xs);
        color: $color-primary;
    }

    &__container {
        width: 100%;
        display: flex;
        gap: 4rem 8rem;
        padding-left: 10rem;
    }

    &__col {
        max-width: 45rem;
    }

    &__list {
        margin-left: 2rem;
    }

    &__list-item {
        list-style: disc;
    }

    &__team-box {
        width: 100%;
        max-width: 40rem;
        border-radius: 1.5rem;
        overflow: hidden;
        position: relative;
    }

    &__logo-box {
        position: absolute;
        top: -1.6rem;
        right: -1.2rem;
        width: 5vw;
        aspect-ratio: 1 / 1;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $color-white;
    }

    &__logo {
        width: 50%;
    }


    /* 480 up */
    @include media(sm) {}

    /* 640 up */
    @include media(md) {}

    /* 1024 up */
    @include media(xm) {}

    /* 1280 up */
    @include media(lg) {}

    /* 1600 up */
    @include media(xl) {}
}