(function () {

    $('.image-container').on('mousemove', function (event) {
        let offsetX = event.pageX - $(this).offset().left;
        let offsetY = event.pageY - $(this).offset().top;
        $('.image-container__overlay').css({
            'background': `radial-gradient(circle 150px at ${offsetX}px ${offsetY}px, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%)`
        });
    });

    // Headings - split into words, letters (h1, h2...)
    $('').not('.js-no-anim').each(function () {

        var wrappedText = $(this).text()
            .split(' ')
            .map((word) => wrapWord(word))
            .join(' ');

        // Replace heading with sliced word
        $(this).html(wrappedText);

        // Show heading
        $(this).css('opacity', 1);


        function wrapWord(word) {
            // Wrap letters of each word in span
            var wrappedLetters = word.replace(/\S/g, '<span class="letter">$&</span>');
            // Wrap each word in div
            var wrappedWord = `<div class="word">${wrappedLetters}</div>`;
            return wrappedWord;
        }
    });

    /*
        credits:
        https://tobiasahlin.com/moving-letters/
    */

    // Headings animation (h1, h2...)
    $('').not('.js-no-anim').waypoint({
        handler: function () {
            anime({
                targets: this.element.querySelectorAll('.letter'),
                translateY: [20, 0],
                translateZ: 0,
                skewY: [15, 0],
                opacity: [0, 1],
                easing: 'easeOutExpo',
                duration: 2000,
                delay: anime.stagger(50)
            });

            // Fire only once
            this.destroy();
        },
        // Wait % of screen
        offset: '90%'
    });


    // Blocks animation (img, div, .g-editor)
    $('.js-anim')
        .css({
            opacity: 0
        })
        .waypoint({
            handler: function () {
                anime({
                    targets: this.element,
                    translateY: [30, 0],
                    opacity: [0, 1],
                    easing: 'easeOutExpo',
                    duration: 2500,
                    delay: 200
                });

                // Fire only once
                this.destroy();
            },
            offset: '80%'
        });

    // Reverse direction block animations
    $('.js-anim-rev').not('.js-no-anim')
        .css({
            opacity: 0
        })
        .waypoint({
            handler: function () {
                anime({
                    targets: this.element,
                    translateY: [-50, 0],
                    opacity: [0, 1],
                    easing: 'easeOutExpo',
                    duration: 2500,
                    delay: 200
                });

                // Fire only once
                this.destroy();
            },
            offset: '80%'
        });

    // Staggered animations (multiple items)
    var $staggered =
        $('.js-stagger-item').not('.js-no-anim').css({ opacity: 0 });
    $('.js-stagger')
        .waypoint({
            handler: function () {
                var t = this;
                anime({
                    targets: this.element.querySelectorAll('.js-stagger-item'),
                    translateY: [30, 0],
                    // scale: [1.1, 1],
                    opacity: [0, 1],
                    easing: 'easeOutExpo',
                    duration: 3000,
                    delay: anime.stagger(200),
                    // Remove the new stacking context which 'transform' creates, so that the mobile menu is positioned correctly
                    complete: function (anim) {
                        anim.animatables.forEach(function (item) {
                            item.target.style.transform = '';
                        });

                        if ($(t.element).hasClass('header__inner')) {
                            $(t.element).css('overflow', 'visible');
                        }
                    }
                });

                // Fire only once
                this.destroy();
            },
            offset: '80%'
        });

    $('.gallery__item').on('click', function () {
        $('.gallery__item').removeClass('gallery__item--active');
        $(this).addClass('gallery__item--active');
        const container = $('.gallery-container');
        const item = $(this);
        const offset = item.position().left + item.outerWidth() / 2 - container.width() / 2;

        container.animate({ scrollLeft: container.scrollLeft() + offset }, 500);
    });;

    gsap.registerPlugin(ScrollTrigger);

    // $('.steps').each(function (index, element) {
    //     const $el = $(element);
    //     const section = element.closest('.steps-horizontal');

    //     gsap.fromTo(element,
    //         {
    //             x: () => element.scrollWidth * 0.01
    //         },
    //         {
    //             x: () => (element.scrollWidth * -1) + window.innerWidth,
    //             xPercent: -15,
    //             scrollTrigger: {
    //                 trigger: element,
    //                 start: 'center 55%',
    //                 end: '+=2000px',
    //                 pin: section,
    //                 scrub: true,
    //                 invalidateOnRefresh: true,
    //                 markers: false,
    //             }
    //         }
    //     );
    // });

    $(".trigger-section").each(function (index, el) {
        ScrollTrigger.create({
            trigger: el,
            start: "top center",
            end: "bottom center",
            onEnter: () => {
                if (index % 2 === 0) {
                    $(".g-pin-sign").addClass("switched");
                } else {
                    $(".g-pin-sign").removeClass("switched");
                }
            },
            onLeaveBack: () => {
                if (index % 2 === 0) {
                    $(".g-pin-sign").removeClass("switched");
                } else {
                    $(".g-pin-sign").addClass("switched");
                }
            }
        });
    });

})();