<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="32" height="15" viewBox="0 0 32 15">
  <defs>
    <clipPath id="clip-path">
      <rect width="32" height="15" fill="none"/>
    </clipPath>
  </defs>
  <g id="menu-open" clip-path="url(#clip-path)">
    <g transform="translate(-27 -26)">
      <rect id="line" width="32" height="1" transform="translate(27 26)" fill="#000"/>
    </g>
    <g transform="translate(-27 -19)">
      <rect id="line-2" data-name="line" width="32" height="1" transform="translate(27 26)" fill="#000"/>
    </g>
    <g transform="translate(-27 -12)">
      <rect id="line-3" data-name="line" width="32" height="1" transform="translate(27 26)" fill="#000"/>
    </g>
  </g>
</svg>
